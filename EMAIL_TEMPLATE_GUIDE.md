# 邮件模板自动发送系统使用指南

## 系统概述

本系统为SnowNavi滑雪学校提供了一个完整的邮件模板管理和自动发送功能。当教练完成学生反馈后，系统可以根据预设的触发条件自动向学生发送邮件通知。

## 主要功能

### 1. 邮件模板管理
- **访问路径**: `http://127.0.0.1:8899/email_template_admin.html`
- **功能**: 创建、编辑、预览和管理邮件模板
- **支持的模板类型**:
  - 反馈通知邮件 (feedback)
  - 欢迎邮件 (welcome)
  - 提醒邮件 (reminder)

### 2. 自动触发条件
- **反馈完成时发送** (`onFeedbackComplete`): 当教练首次完成学生反馈时自动发送
- **反馈更新时发送** (`onFeedbackUpdate`): 当教练更新已有反馈时自动发送
- **仅手动发送** (`manualOnly`): 不自动发送，仅支持手动触发

### 3. 模板变量系统
邮件模板支持以下动态变量：
- `{{studentName}}` - 学生姓名
- `{{courseName}}` - 课程名称
- `{{instructorName}}` - 教练姓名
- `{{courseDate}}` - 课程日期
- `{{feedbackContent}}` - 反馈内容
- `{{feedbackUrl}}` - 查看详细反馈的链接

## 使用流程

### 步骤1: 配置邮件模板
1. 访问邮件模板管理页面
2. 选择现有模板或创建新模板
3. 设置模板基本信息：
   - 模板名称
   - 模板类型
   - 状态（激活/停用）
   - 描述
4. 编辑邮件内容：
   - 邮件主题（支持变量）
   - HTML邮件内容（支持变量）
5. 配置触发条件：
   - 选择何时自动发送邮件
6. 预览邮件效果
7. 保存模板

### 步骤2: 教练完成反馈
1. 教练在checkin管理页面为学生添加反馈
2. 点击"Save Feedback"按钮
3. 系统自动检查是否有配置的邮件模板
4. 如果满足触发条件，自动发送邮件给学生

### 步骤3: 学生接收邮件
1. 学生收到邮件通知
2. 邮件包含课程信息和反馈内容
3. 学生可以点击链接查看详细反馈

## 技术实现

### 后端API
- `GET /api/email-templates` - 获取所有邮件模板
- `POST /api/email-templates` - 保存邮件模板
- `GET /api/email-templates/<template_id>` - 获取特定模板
- `POST /api/send-auto-email` - 发送自动邮件

### 前端页面
- `email_template_admin.html` - 邮件模板管理界面
- `checkin_admin.html` - 集成了自动邮件发送功能

### 数据存储
- 邮件模板存储在 `data/email_templates.json`
- 默认创建反馈通知模板

## 配置要求

### 环境变量
确保后端 `.env` 文件包含Brevo邮件服务配置：
```
BREVO_API_KEY=your_brevo_api_key
BREVO_SENDER_EMAIL=your_sender_email
BREVO_SENDER_NAME=SnowNavi
```

### 学生邮箱
确保学生信息中包含有效的邮箱地址，系统会根据学生ID查找对应的邮箱。

## 故障排除

### 常见问题
1. **邮件未发送**
   - 检查邮件模板是否激活
   - 检查触发条件是否正确配置
   - 检查学生是否有有效邮箱地址
   - 检查Brevo API配置

2. **模板变量未替换**
   - 确保变量名称正确（区分大小写）
   - 检查数据是否完整

3. **邮件格式问题**
   - 检查HTML格式是否正确
   - 使用预览功能测试效果

### 日志查看
查看后端日志了解详细错误信息：
```bash
cd backend && python server.py
```

## 扩展功能

### 添加新的模板变量
在 `backend/server.py` 的 `sendAutoEmailNotification` 函数中添加新的变量到 `emailData` 对象。

### 添加新的触发条件
在邮件模板管理界面的触发条件部分添加新的选项，并在后端相应位置添加处理逻辑。

### 自定义邮件样式
修改邮件模板的HTML内容，支持完全自定义的邮件样式和布局。

## 安全考虑

- 邮件模板内容经过HTML转义处理
- API访问需要适当的身份验证
- 敏感信息（如API密钥）存储在环境变量中
- 输入验证防止注入攻击

## 维护建议

- 定期备份邮件模板配置
- 监控邮件发送成功率
- 定期更新邮件内容和样式
- 测试新模板在不同邮件客户端的显示效果
