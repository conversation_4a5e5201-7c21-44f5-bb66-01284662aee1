<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>SnowNavi Admin Panel</title>

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="assets/picture/snownavi_logo.png">
  <link rel="icon" type="image/png" sizes="16x16" href="assets/picture/snownavi_logo.png">
  <link rel="shortcut icon" href="assets/picture/snownavi_logo.png">

  <style>
    :root {
      --main-red: #E53512;
      --bg-light: #F9F4F3;
      --text-dark: #2F2F2F;
      --text-gray: #717171;
      --contrast-white: #FFFFFF;
      --accent-blue: #9ED4E7;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--bg-light);
      color: var(--text-dark);
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .header {
      background: var(--contrast-white);
      padding: 1rem 2rem;
      border-bottom: 3px solid var(--main-red);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logo-section {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .logo-section img {
      width: 50px;
      height: 50px;
    }

    .logo-section h1 {
      color: var(--main-red);
      font-size: 1.8rem;
      margin: 0;
    }

    .nav-links {
      display: flex;
      gap: 1rem;
    }

    .nav-link {
      color: var(--text-gray);
      text-decoration: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      transition: all 0.2s ease;
    }

    .nav-link:hover {
      background: var(--bg-light);
      color: var(--main-red);
    }
    .main-content {
      flex: 1;
      padding: 2rem;
    }

    .page-title {
      text-align: center;
      margin-bottom: 2rem;
    }

    .page-title h2 {
      color: var(--main-red);
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
    }

    .page-title p {
      color: var(--text-gray);
      font-size: 1.1rem;
    }
    .admin-container {
      max-width: 1200px;
      margin: 0 auto;
    }
    .admin-options {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
      max-width: 1200px;
      margin-left: auto;
      margin-right: auto;
    }
    .admin-card {
      background: var(--contrast-white);
      border: 2px solid var(--accent-blue);
      border-radius: 12px;
      padding: 1.5rem;
      text-align: center;
      transition: all 0.2s ease;
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    .admin-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
      border-color: var(--main-red);
    }
    .admin-card h2 {
      color: var(--main-red);
      margin-top: 0;
      margin-bottom: 1rem;
    }
    .admin-card p {
      color: var(--text-gray);
      margin-bottom: 1.5rem;
      line-height: 1.6;
    }
    .admin-btn {
      background: var(--main-red);
      color: var(--contrast-white);
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 6px;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
      transition: all 0.2s ease;
    }
    .admin-btn:hover {
      background: #c42e0f;
      transform: translateY(-2px);
    }
    .admin-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
      color: var(--main-red);
    }
    /* Authentication styles */
    .auth-container {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.9);
      z-index: 1000;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }
    .auth-message {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .auth-message h2 {
      color: #E53512;
      margin-top: 0;
    }
    .auth-btn {
      background: #E53512;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      margin-top: 1rem;
      text-decoration: none;
      display: inline-block;
    }
    .user-info {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    .user-info img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }
    .logout-btn {
      background: none;
      border: none;
      color: var(--main-red);
      cursor: pointer;
      text-decoration: underline;
      padding: 0.25rem 0.5rem;
    }
    .logout-btn:hover {
      background: var(--bg-light);
      border-radius: 4px;
    }

    /* Mobile responsive styles */
    @media (max-width: 768px) {
      .header {
        padding: 0.75rem 1rem;
      }

      .header-content {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
      }

      .logo-section {
        justify-content: center;
      }

      .logo-section h1 {
        font-size: 1.5rem;
      }

      .nav-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
        order: 2;
      }

      .nav-link {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
        white-space: nowrap;
      }

      .user-info {
        justify-content: center;
        order: 1;
        font-size: 0.9rem;
      }

      .user-info img {
        width: 28px;
        height: 28px;
      }

      .main-content {
        padding: 1rem;
      }

      .page-title h2 {
        font-size: 2rem;
      }

      .admin-options {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-top: 1rem;
      }

      .admin-card {
        padding: 1rem;
      }

      .admin-icon {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
      }

      .admin-card h2 {
        font-size: 1.2rem;
        margin: 0.5rem 0;
      }

      .admin-card p {
        font-size: 0.9rem;
        margin-bottom: 1rem;
      }

      .auth-message {
        margin: 1rem;
        padding: 1.5rem;
        max-width: calc(100% - 2rem);
      }
    }

    @media (max-width: 480px) {
      .header {
        padding: 0.5rem;
      }

      .logo-section img {
        width: 40px;
        height: 40px;
      }

      .logo-section h1 {
        font-size: 1.3rem;
      }

      .nav-links {
        gap: 0.25rem;
      }

      .nav-link {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
      }

      .user-info {
        font-size: 0.8rem;
      }

      .user-info img {
        width: 24px;
        height: 24px;
      }

      .main-content {
        padding: 0.5rem;
      }
    }

    @media (max-width: 480px) {
      .admin-container {
        padding: 0.5rem;
      }

      .admin-card {
        padding: 0.75rem;
      }

      .admin-icon {
        font-size: 2rem;
      }

      .admin-card h2 {
        font-size: 1.1rem;
      }

      .admin-card p {
        font-size: 0.8rem;
      }

      .admin-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
      }
    }
  </style>
</head>
<body>
  <!-- Authentication overlay -->
  <div class="auth-container" id="auth-container">
    <div class="auth-message">
      <h2>Authentication Required</h2>
      <p>You need to be logged in to access this page.</p>
      <a href="login.html" class="auth-btn">Go to Login</a>
    </div>
  </div>

  <div class="header">
    <div class="header-content">
      <div class="logo-section">
        <img src="assets/picture/snownavi_logo.png" alt="SnowNavi Logo">
        <h1>SnowNavi Admin</h1>
      </div>
      <nav class="nav-links">
        <a href="admin.html" class="nav-link" style="color: var(--main-red);">🏠 Dashboard</a>
        <a href="course_admin.html" class="nav-link">📚 Courses</a>
        <a href="member_admin.html" class="nav-link">👥 Members</a>
        <a href="activity_admin.html" class="nav-link">📅 Activities</a>
        <a href="checkin_admin.html" class="nav-link">✅ Check-ins</a>
        <a href="feedback_template_admin.html" class="nav-link">📝 Templates</a>
        <a href="email_template_admin.html" class="nav-link">📧 Email Templates</a>
        <a href="email_preview.html" class="nav-link">👁️ Email Preview</a>
      </nav>
      <div class="user-info" id="user-info"></div>
    </div>
  </div>

  <div class="main-content">
    <div class="page-title">
      <h2>SnowNavi Admin Dashboard</h2>
      <p>Welcome to the SnowNavi Administration Panel. Please select the section you would like to manage:</p>
    </div>

    <div class="admin-container">

    <div class="admin-options">
      <div class="admin-card" onclick="window.location.href='course_admin.html'">
        <div class="admin-icon">📚</div>
        <h2>Course Management</h2>
        <p>Manage course information, subcourses, and related content.</p>
        <button class="admin-btn">Manage Courses</button>
      </div>

      <div class="admin-card" onclick="window.location.href='member_admin.html'">
        <div class="admin-icon">👥</div>
        <h2>Member Management</h2>
        <p>Manage member information, membership status, and validity periods.</p>
        <button class="admin-btn">Manage Members</button>
      </div>

      <div class="admin-card" onclick="window.location.href='navigation_admin.html'">
        <div class="admin-icon">🧭</div>
        <h2>Navigation Management</h2>
        <p>Configure the website navigation bar and menu items.</p>
        <button class="admin-btn">Manage Navigation</button>
      </div>

      <div class="admin-card" onclick="window.location.href='activity_admin.html'">
        <div class="admin-icon">📅</div>
        <h2>Activity Management</h2>
        <p>Manage activities, events, and course schedules.</p>
        <button class="admin-btn">Manage Activities</button>
      </div>

      <div class="admin-card" onclick="window.location.href='checkin_admin.html'">
        <div class="admin-icon">✅</div>
        <h2>Check-in Management</h2>
        <p>Scan QR codes to check in members and coaches for activities.</p>
        <button class="admin-btn">Manage Check-ins</button>
      </div>

      <div class="admin-card" onclick="window.location.href='feedback_template_admin.html'">
        <div class="admin-icon">📝</div>
        <h2>Feedback Templates</h2>
        <p>Create and manage feedback templates for different course types and skill levels.</p>
        <button class="admin-btn">Manage Templates</button>
      </div>
    </div>
  </div>

  <script>
    // Fetch configuration and check authentication
    async function checkAuth() {
      try {
        // Fetch the authorized email from the server
        const response = await fetch('/api/config');
        if (!response.ok) {
          throw new Error('Failed to fetch configuration');
        }

        const config = await response.json();

        // Parse allowed emails - try new format first, fallback to old format
        let allowedEmails = [];
        if (config.allowedEmails && Array.isArray(config.allowedEmails)) {
          allowedEmails = config.allowedEmails;
        } else if (config.authorizedEmail) {
          allowedEmails = config.authorizedEmail.split(',').map(email => email.trim());
        }

        const auth = JSON.parse(localStorage.getItem('snownavi_auth') || '{}');
        const authContainer = document.getElementById('auth-container');
        const userInfoContainer = document.getElementById('user-info');

        // Check if auth exists, is not expired, and user is authorized
        const isAuthorized = allowedEmails.includes(auth.email);
        if (isAuthorized && auth.expiresAt && auth.expiresAt > Date.now()) {
          // User is authenticated
          authContainer.style.display = 'none';

          // Display user info
          userInfoContainer.innerHTML = `
            <img src="${auth.picture}" alt="Profile">
            <span>${auth.name}</span>
            <button class="logout-btn" onclick="logout()">Logout</button>
          `;
        } else {
          // User is not authenticated, show auth container
          authContainer.style.display = 'flex';
          userInfoContainer.innerHTML = '';
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        document.getElementById('auth-container').style.display = 'flex';
        document.querySelector('.auth-message p').textContent = 'Error loading configuration. Please try again later.';
      }
    }

    // Logout function
    function logout() {
      localStorage.removeItem('snownavi_auth');
      window.location.href = 'login.html';
    }

    // Initialize the page
    window.onload = checkAuth;
  </script>
</body>
</html>
