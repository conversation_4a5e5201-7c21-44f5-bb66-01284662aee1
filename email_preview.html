<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Preview - SnowNavi Admin</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="assets/picture/snownavi_logo.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/picture/snownavi_logo.png">
    <link rel="shortcut icon" href="assets/picture/snownavi_logo.png">

    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
            --success-green: #28a745;
            --warning-yellow: #ffc107;
            --danger-red: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-light);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .header {
            background: var(--contrast-white);
            padding: 1rem 2rem;
            border-bottom: 3px solid var(--main-red);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-section img {
            width: 50px;
            height: 50px;
        }

        .logo-section h1 {
            color: var(--main-red);
            font-size: 1.8rem;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
        }

        .nav-link {
            color: var(--text-gray);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            background: var(--bg-light);
            color: var(--main-red);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .user-info img {
            width: 32px;
            height: 32px;
            border-radius: 50%;
        }
        .logout-btn {
            background: none;
            border: none;
            color: var(--main-red);
            cursor: pointer;
            text-decoration: underline;
            padding: 0.25rem 0.5rem;
        }
        .logout-btn:hover {
            background: var(--bg-light);
            border-radius: 4px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-title {
            text-align: center;
            margin-bottom: 2rem;
        }

        .page-title h2 {
            color: var(--main-red);
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .page-title p {
            color: var(--text-gray);
            font-size: 1.1rem;
        }

        .main-container {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
        }

        .feedback-list {
            background: var(--contrast-white);
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }

        .feedback-list h3 {
            color: var(--main-red);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .feedback-item {
            padding: 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .feedback-item:hover {
            border-color: var(--main-red);
            background: var(--bg-light);
        }

        .feedback-item.active {
            border-color: var(--main-red);
            background: var(--bg-light);
        }

        .feedback-item h4 {
            color: var(--main-red);
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        .feedback-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .preview-container {
            background: var(--contrast-white);
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .no-selection {
            text-align: center;
            color: var(--text-gray);
            padding: 3rem;
            font-size: 1.1rem;
        }

        .preview-header {
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }

        .preview-header h3 {
            color: var(--main-red);
            margin-bottom: 0.5rem;
        }

        .email-subject {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .email-content {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            background: white;
            overflow: hidden;
        }

        .email-iframe {
            width: 100%;
            min-height: 600px;
            border: none;
            background: white;
        }

        .actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
            margin-top: 2rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: var(--main-red);
            color: white;
        }

        .btn-primary:hover {
            background: #c52e10;
        }

        .btn-secondary {
            background: var(--text-gray);
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .loading {
            text-align: center;
            color: var(--text-gray);
            padding: 2rem;
        }

        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
        }

        .template-selector {
            margin-bottom: 1rem;
        }

        .template-selector label {
            font-weight: bold;
            margin-bottom: 0.5rem;
            display: block;
            color: var(--text-dark);
        }

        .template-selector select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.2s ease;
        }

        .template-selector select:focus {
            outline: none;
            border-color: var(--main-red);
        }

        /* Authentication styles */
        .auth-container {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        .auth-message {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            text-align: center;
            max-width: 400px;
        }
        .auth-message h2 {
            color: #E53512;
            margin-top: 0;
        }
        .auth-btn {
            background: #E53512;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            display: inline-block;
            margin-top: 1rem;
            cursor: pointer;
        }
        .auth-btn:hover {
            background: #c52e10;
        }

        @media (max-width: 768px) {
            .main-container {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .actions {
                flex-direction: column;
                align-items: stretch;
            }

            .auth-message {
                margin: 1rem;
                padding: 1.5rem;
                max-width: calc(100% - 2rem);
            }
        }
    </style>
</head>
<body>
    <!-- Authentication overlay -->
    <div class="auth-container" id="auth-container">
        <div class="auth-message">
            <h2>Authentication Required</h2>
            <p>You need to be logged in to access this page.</p>
            <a href="login.html" class="auth-btn">Go to Login</a>
        </div>
    </div>

    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                <img src="assets/picture/snownavi_logo.png" alt="SnowNavi Logo">
                <h1>SnowNavi Admin</h1>
            </div>
            <nav class="nav-links">
                <a href="admin.html" class="nav-link">🏠 Dashboard</a>
                <a href="course_admin.html" class="nav-link">📚 Courses</a>
                <a href="member_admin.html" class="nav-link">👥 Members</a>
                <a href="activity_admin.html" class="nav-link">📅 Activities</a>
                <a href="checkin_admin.html" class="nav-link">✅ Check-ins</a>
                <a href="feedback_template_admin.html" class="nav-link">📝 Templates</a>
                <a href="email_template_admin.html" class="nav-link">📧 Email Templates</a>
                <a href="email_preview.html" class="nav-link" style="color: var(--main-red);">👁️ Email Preview</a>
            </nav>
            <div class="user-info" id="user-info">
                <!-- User info will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <div class="container">
        <div class="page-title">
            <h2>👁️ Email Preview</h2>
            <p>Select a feedback record to preview the email that would be sent to the student</p>
        </div>

        <div class="main-container">
            <div class="feedback-list" id="feedback-list">
                <h3>📋 Available Feedback Records</h3>
                <div class="template-selector">
                    <label for="email-template-select">Email Template:</label>
                    <select id="email-template-select">
                        <option value="">Loading templates...</option>
                    </select>
                </div>
                <div class="loading">Loading feedback records...</div>
            </div>
            
            <div class="preview-container" id="preview-container">
                <div class="no-selection">Select a feedback record from the left panel to preview the email</div>
            </div>
        </div>

        <div class="actions">
            <button class="btn btn-secondary" onclick="refreshData()">🔄 Refresh Data</button>
            <div style="display: flex; gap: 1rem;">
                <button class="btn btn-primary" onclick="generatePreview()" id="preview-btn" style="display: none;">👁️ Generate Preview</button>
                <button class="btn btn-secondary" onclick="openInNewWindow()" id="open-btn" style="display: none;">🔗 Open in New Window</button>
            </div>
        </div>
    </div>

    <script>
        let feedbacks = {};
        let members = {};
        let activities = {};
        let emailTemplates = {};
        let selectedFeedback = null;
        let selectedTemplate = null;
        let currentPreviewData = null;

        // Check authentication
        async function checkAuth() {
            try {
                // Fetch the authorized email from the server
                const response = await fetch('/api/config');
                if (!response.ok) {
                    throw new Error('Failed to fetch configuration');
                }

                const config = await response.json();

                // Get allowed emails from config
                let allowedEmails = [];
                if (config.allowedEmails && Array.isArray(config.allowedEmails)) {
                    allowedEmails = config.allowedEmails;
                } else if (config.authorizedEmail) {
                    allowedEmails = config.authorizedEmail.split(',').map(email => email.trim());
                }

                const auth = JSON.parse(localStorage.getItem('snownavi_auth') || '{}');
                const authContainer = document.getElementById('auth-container');
                const userInfoContainer = document.getElementById('user-info');

                // Check if auth exists, is not expired, and user is authorized
                const isAuthorized = allowedEmails.includes(auth.email);
                if (isAuthorized && auth.expiresAt && auth.expiresAt > Date.now()) {
                    // User is authenticated
                    authContainer.style.display = 'none';

                    // Display user info
                    userInfoContainer.innerHTML = `
                        <img src="${auth.picture}" alt="Profile">
                        <span>${auth.name}</span>
                        <button class="logout-btn" onclick="logout()">Logout</button>
                    `;

                    // Initialize the page
                    await init();
                } else {
                    // User is not authenticated, show auth container
                    authContainer.style.display = 'flex';
                    userInfoContainer.innerHTML = '';
                }
            } catch (error) {
                console.error('Error checking authentication:', error);
                document.getElementById('auth-container').style.display = 'flex';
                document.querySelector('.auth-message p').textContent = 'Error loading configuration. Please try again later.';
            }
        }

        // Logout function
        function logout() {
            localStorage.removeItem('snownavi_auth');
            window.location.href = 'login.html';
        }

        // Initialize the page (only called after authentication)
        async function init() {
            try {
                await loadAllData();
                await loadEmailTemplates();
                renderFeedbackList();
                renderTemplateSelector();
            } catch (error) {
                console.error('Error initializing page:', error);
                showError('Failed to load data. Please try again later.');
            }
        }

        // Load all required data
        async function loadAllData() {
            try {
                const [feedbacksRes, membersRes, activitiesRes] = await Promise.all([
                    fetch('data/feedbacks.json'),
                    fetch('data/members.json'),
                    fetch('data/activities.json')
                ]);

                if (feedbacksRes.ok) feedbacks = await feedbacksRes.json();
                if (membersRes.ok) members = await membersRes.json();
                if (activitiesRes.ok) activities = await activitiesRes.json();

                console.log('Loaded data:', { feedbacks, members, activities });
            } catch (error) {
                console.error('Error loading data:', error);
                throw error;
            }
        }

        // Load email templates
        async function loadEmailTemplates() {
            try {
                const response = await fetch('/api/email-templates');
                if (response.ok) {
                    emailTemplates = await response.json();
                    console.log('Loaded email templates:', emailTemplates);
                }
            } catch (error) {
                console.error('Error loading email templates:', error);
            }
        }

        // Render template selector
        function renderTemplateSelector() {
            const templateSelect = document.getElementById('email-template-select');

            if (Object.keys(emailTemplates).length === 0) {
                templateSelect.innerHTML = '<option value="">No email templates found</option>';
                return;
            }

            let html = '<option value="">Select an email template</option>';

            Object.entries(emailTemplates).forEach(([templateId, template]) => {
                if (template.active) {
                    html += `<option value="${templateId}">${template.name}</option>`;
                }
            });

            templateSelect.innerHTML = html;

            // Auto-select feedback notification template if available
            if (emailTemplates.feedback_notification && emailTemplates.feedback_notification.active) {
                templateSelect.value = 'feedback_notification';
                selectedTemplate = 'feedback_notification';
            }

            templateSelect.addEventListener('change', (e) => {
                selectedTemplate = e.target.value;
                if (selectedFeedback && selectedTemplate) {
                    generatePreview();
                }
            });
        }

        // Render feedback list
        function renderFeedbackList() {
            const feedbackList = document.getElementById('feedback-list');

            if (Object.keys(feedbacks).length === 0) {
                feedbackList.innerHTML = `
                    <h3>📋 Available Feedback Records</h3>
                    <div class="template-selector">
                        <label for="email-template-select">Email Template:</label>
                        <select id="email-template-select">
                            <option value="">Loading templates...</option>
                        </select>
                    </div>
                    <div style="text-align: center; color: var(--text-gray); padding: 2rem;">
                        No feedback records found.
                    </div>
                `;
                return;
            }

            let html = `
                <h3>📋 Available Feedback Records</h3>
                <div class="template-selector">
                    <label for="email-template-select">Email Template:</label>
                    <select id="email-template-select">
                        <option value="">Loading templates...</option>
                    </select>
                </div>
            `;

            // Sort feedbacks by creation date (newest first)
            const sortedFeedbacks = Object.entries(feedbacks).sort((a, b) => {
                return new Date(b[1].createdAt) - new Date(a[1].createdAt);
            });

            sortedFeedbacks.forEach(([feedbackId, feedback]) => {
                const member = members[feedback.memberId];
                const activity = activities[feedback.activityId];

                if (!member || !activity) return;

                const memberName = member.name || 'Unknown Member';
                const activityName = activity.name?.en || activity.name || 'Unknown Activity';
                const createdDate = new Date(feedback.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });

                html += `
                    <div class="feedback-item ${selectedFeedback === feedbackId ? 'active' : ''}"
                         onclick="selectFeedback('${feedbackId}')" data-feedback-id="${feedbackId}">
                        <h4>${memberName}</h4>
                        <p style="font-size: 0.9rem; color: var(--text-gray); margin-bottom: 0.5rem;">
                            ${activityName}
                        </p>
                        <div class="feedback-meta">
                            <span>📧 ${member.email || 'No email'}</span>
                            <span>${createdDate}</span>
                        </div>
                    </div>
                `;
            });

            feedbackList.innerHTML = html;
            renderTemplateSelector();
        }

        // Select a feedback record
        function selectFeedback(feedbackId) {
            selectedFeedback = feedbackId;
            renderFeedbackList();

            document.getElementById('preview-btn').style.display = 'inline-block';
            document.getElementById('open-btn').style.display = 'inline-block';

            if (selectedTemplate) {
                generatePreview();
            } else {
                document.getElementById('preview-container').innerHTML = `
                    <div class="no-selection">Please select an email template to generate preview</div>
                `;
            }
        }

        // Generate email preview
        async function generatePreview() {
            if (!selectedFeedback || !selectedTemplate) {
                showError('Please select both a feedback record and an email template');
                return;
            }

            const previewContainer = document.getElementById('preview-container');
            previewContainer.innerHTML = '<div class="loading">Generating email preview...</div>';

            try {
                const feedback = feedbacks[selectedFeedback];
                const member = members[feedback.memberId];
                const activity = activities[feedback.activityId];
                const template = emailTemplates[selectedTemplate];

                if (!member.email) {
                    showError('Selected member does not have an email address');
                    return;
                }

                // Generate learning timeline content
                const timelineResponse = await fetch('/api/generate-learning-timeline', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        memberId: member.id,
                        currentFeedbackDate: feedback.createdAt
                    })
                });

                let feedbackContent = '';
                if (timelineResponse.ok) {
                    const timelineData = await timelineResponse.json();
                    feedbackContent = timelineData.timelineContent;
                } else {
                    feedbackContent = feedback.overallFeedback || feedback.feedback || 'No feedback content available';
                }

                // Prepare email data (simplified - only course name and date)
                const emailData = {
                    studentEmail: member.email,
                    studentName: member.name,
                    courseName: activity.name?.en || activity.name || 'Unknown Course',
                    courseDate: new Date(activity.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    }),
                    feedbackUrl: `${window.location.origin}/student_feedback.html?memberId=${member.id}`
                };

                // Replace template variables
                let emailSubject = template.subject;
                let emailHtmlContent = template.htmlContent;

                Object.keys(emailData).forEach(key => {
                    const regex = new RegExp(`{{${key}}}`, 'g');
                    emailSubject = emailSubject.replace(regex, emailData[key]);
                    emailHtmlContent = emailHtmlContent.replace(regex, emailData[key]);
                });

                // Store current preview data for opening in new window
                currentPreviewData = {
                    subject: emailSubject,
                    htmlContent: emailHtmlContent,
                    emailData: emailData
                };

                // Render preview
                renderEmailPreview(emailSubject, emailHtmlContent);

            } catch (error) {
                console.error('Error generating preview:', error);
                showError('Failed to generate email preview. Please try again.');
            }
        }

        // Render email preview
        function renderEmailPreview(subject, htmlContent) {
            const previewContainer = document.getElementById('preview-container');

            const html = `
                <div class="preview-header">
                    <h3>📧 Email Preview</h3>
                    <p style="color: var(--text-gray); margin: 0;">This is exactly how the email will appear to the student</p>
                </div>

                <div class="email-subject">
                    <strong>Subject:</strong> ${subject}
                </div>

                <div class="email-content">
                    <iframe class="email-iframe" srcdoc="${htmlContent.replace(/"/g, '&quot;')}" sandbox="allow-same-origin"></iframe>
                </div>
            `;

            previewContainer.innerHTML = html;
        }

        // Open preview in new window
        function openInNewWindow() {
            if (!currentPreviewData) {
                showError('No preview data available. Please generate a preview first.');
                return;
            }

            const newWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');

            const htmlContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>${currentPreviewData.subject}</title>
                    <style>
                        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #f5f5f5; }
                        .email-container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                        .subject-bar { background: #E53512; color: white; padding: 15px; font-weight: bold; }
                        .email-body { padding: 0; }
                    </style>
                </head>
                <body>
                    <div class="email-container">
                        <div class="subject-bar">
                            📧 ${currentPreviewData.subject}
                        </div>
                        <div class="email-body">
                            ${currentPreviewData.htmlContent}
                        </div>
                    </div>
                </body>
                </html>
            `;

            newWindow.document.write(htmlContent);
            newWindow.document.close();
        }

        // Refresh all data
        async function refreshData() {
            try {
                selectedFeedback = null;
                selectedTemplate = null;
                currentPreviewData = null;

                document.getElementById('preview-btn').style.display = 'none';
                document.getElementById('open-btn').style.display = 'none';
                document.getElementById('preview-container').innerHTML = '<div class="loading">Refreshing data...</div>';

                await loadAllData();
                await loadEmailTemplates();
                renderFeedbackList();
                renderTemplateSelector();

                document.getElementById('preview-container').innerHTML = '<div class="no-selection">Select a feedback record from the left panel to preview the email</div>';

                showSuccess('Data refreshed successfully!');
            } catch (error) {
                console.error('Error refreshing data:', error);
                showError('Failed to refresh data. Please try again.');
            }
        }

        // Show error message
        function showError(message) {
            const container = document.getElementById('preview-container');
            const existingContent = container.innerHTML;
            container.innerHTML = `<div class="error">${message}</div>` + existingContent;
            setTimeout(() => {
                const errorDiv = container.querySelector('.error');
                if (errorDiv) errorDiv.remove();
            }, 5000);
        }

        // Show success message
        function showSuccess(message) {
            const container = document.getElementById('preview-container');
            const existingContent = container.innerHTML;
            container.innerHTML = `<div class="success">${message}</div>` + existingContent;
            setTimeout(() => {
                const successDiv = container.querySelector('.success');
                if (successDiv) successDiv.remove();
            }, 3000);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', checkAuth);
    </script>
</body>
</html>
