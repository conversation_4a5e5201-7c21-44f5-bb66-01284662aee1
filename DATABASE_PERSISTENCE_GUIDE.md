# 🗄️ 数据库持久化和Docker重启指南

## ✅ 数据库持久化状态确认

### 当前配置
- **数据库类型**: PostgreSQL 15
- **容器名称**: `snownavi-db`
- **数据卷**: `snownavi_website_postgres_data`
- **挂载点**: `/var/lib/docker/volumes/snownavi_website_postgres_data/_data`
- **持久化状态**: ✅ **已确保数据持久化**

### 数据库连接信息
```bash
# Docker数据库连接
DATABASE_URL=postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi

# 数据库配置
POSTGRES_DB: snownavi
POSTGRES_USER: snownavi_user
POSTGRES_PASSWORD: snownavi_password
```

## 🔒 数据安全保证

### ✅ 数据持久化机制
1. **Docker卷持久化**: 使用命名卷`postgres_data`存储数据
2. **本地存储**: 数据存储在Docker主机的本地文件系统
3. **容器独立**: 数据与容器生命周期分离
4. **重启安全**: 容器重启、停止、删除都不会影响数据

### 📊 当前数据状态
```bash
# 数据库表数量: 9个表
# 成员数据: 3条记录
# 活动数据: 4条记录
# 其他数据: 完整迁移
```

## 🚀 正确的Docker重启方法

### 1. 安全重启（推荐）
```bash
# 重启所有服务（保留数据）
docker compose restart

# 或者重启特定服务
docker compose restart db
```

### 2. 停止并重新启动
```bash
# 停止服务（保留数据和卷）
docker compose stop

# 重新启动服务
docker compose start

# 或者一步完成
docker compose down && docker compose up -d
```

### 3. 完全重建（保留数据）
```bash
# 停止并删除容器（保留卷和数据）
docker compose down

# 重新创建并启动容器
docker compose up -d
```

## ⚠️ 危险操作（会删除数据）

### 🚫 绝对不要执行的命令
```bash
# ❌ 这会删除所有数据！
docker compose down -v

# ❌ 这会删除数据卷！
docker volume rm snownavi_website_postgres_data

# ❌ 这会删除所有卷！
docker volume prune
```

## 🛡️ 数据备份和恢复

### 数据备份
```bash
# 1. 创建数据库备份
docker exec snownavi-db pg_dump -U snownavi_user -d snownavi > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 备份整个数据卷
docker run --rm -v snownavi_website_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup_$(date +%Y%m%d_%H%M%S).tar.gz -C /data .
```

### 数据恢复
```bash
# 1. 从SQL备份恢复
docker exec -i snownavi-db psql -U snownavi_user -d snownavi < backup_20250612_085500.sql

# 2. 从卷备份恢复
docker run --rm -v snownavi_website_postgres_data:/data -v $(pwd):/backup alpine tar xzf /backup/postgres_backup_20250612_085500.tar.gz -C /data
```

## 🔧 维护操作

### 检查数据库状态
```bash
# 检查容器状态
docker ps | grep snownavi-db

# 检查数据卷
docker volume ls | grep postgres

# 检查数据库连接
docker exec snownavi-db psql -U snownavi_user -d snownavi -c "SELECT current_database(), current_user;"

# 检查表和数据
docker exec snownavi-db psql -U snownavi_user -d snownavi -c "\dt"
docker exec snownavi-db psql -U snownavi_user -d snownavi -c "SELECT COUNT(*) FROM members;"
```

### 数据库维护
```bash
# 查看数据库大小
docker exec snownavi-db psql -U snownavi_user -d snownavi -c "SELECT pg_size_pretty(pg_database_size('snownavi'));"

# 查看表大小
docker exec snownavi-db psql -U snownavi_user -d snownavi -c "SELECT schemaname,tablename,pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size FROM pg_tables WHERE schemaname='public' ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;"
```

## 📋 完整重启流程

### 标准重启流程
```bash
# 1. 检查当前状态
docker ps
docker volume ls | grep postgres

# 2. 创建备份（可选但推荐）
docker exec snownavi-db pg_dump -U snownavi_user -d snownavi > backup_before_restart.sql

# 3. 安全重启
docker compose down
docker compose up -d

# 4. 验证数据完整性
docker exec snownavi-db psql -U snownavi_user -d snownavi -c "SELECT COUNT(*) FROM members;"

# 5. 测试应用连接
cd backend && python -c "
import os
os.environ['DATABASE_URL'] = 'postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi'
from database_access import DataAccessLayer
dal = DataAccessLayer(mode='database')
members = dal.get_all('members')
print(f'✅ 应用连接成功，找到 {len(members)} 个成员')
"
```

## 🎯 故障排除

### 常见问题和解决方案

#### 1. 容器启动失败
```bash
# 检查日志
docker logs snownavi-db

# 检查端口占用
lsof -i :5432

# 强制重建
docker compose down
docker compose up -d --force-recreate
```

#### 2. 数据连接失败
```bash
# 检查数据库是否就绪
docker exec snownavi-db pg_isready -U snownavi_user

# 检查网络连接
docker exec snownavi-db netstat -tlnp | grep 5432

# 重置连接
docker compose restart db
```

#### 3. 数据丢失恢复
```bash
# 检查卷是否存在
docker volume ls | grep postgres

# 如果卷存在但数据丢失，从备份恢复
docker exec -i snownavi-db psql -U snownavi_user -d snownavi < backup_latest.sql
```

## ✅ 数据安全确认清单

- [x] **数据卷配置**: `postgres_data:/var/lib/postgresql/data`
- [x] **持久化存储**: 数据存储在Docker主机本地
- [x] **容器独立**: 数据与容器生命周期分离
- [x] **重启安全**: 使用`docker compose down && docker compose up -d`
- [x] **备份机制**: 定期备份SQL和卷数据
- [x] **恢复测试**: 验证备份可以正常恢复

## 🚀 生产环境建议

### 自动备份脚本
```bash
#!/bin/bash
# 创建 backup_database.sh
BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建SQL备份
docker exec snownavi-db pg_dump -U snownavi_user -d snownavi > "$BACKUP_DIR/snownavi_$DATE.sql"

# 保留最近7天的备份
find "$BACKUP_DIR" -name "snownavi_*.sql" -mtime +7 -delete

echo "✅ 数据库备份完成: snownavi_$DATE.sql"
```

### 定时备份（crontab）
```bash
# 每天凌晨2点自动备份
0 2 * * * /path/to/backup_database.sh
```

## 🧪 持久化验证测试

### 自动化测试脚本
```bash
# 运行完整的持久化测试
python test_database_persistence.py
```

### 测试结果（已验证）
```
🎉 数据库持久化测试 - 全部通过！

✅ 确认事项:
   ✅ Docker容器重启后数据完整保持
   ✅ 数据卷正确配置并持久化
   ✅ 应用可以正常连接数据库
   ✅ 所有数据记录数量一致

🔒 数据安全保证:
   ✅ 数据存储在Docker卷中，与容器生命周期分离
   ✅ 使用 'docker compose down && docker compose up -d' 安全重启
   ✅ 避免使用 'docker compose down -v' 防止数据丢失
```

---

## 📝 总结

**✅ 数据库数据已确保持久化存储，Docker环境关闭后数据不会丢失**

**🔒 安全重启方法**: 使用 `docker compose down && docker compose up -d`

**⚠️ 避免使用**: `docker compose down -v` 或 `docker volume rm`

**🛡️ 建议**: 定期备份，测试恢复流程，监控数据完整性

**✅ 验证状态**: 已通过完整的持久化测试验证

## 🖥️ PostgreSQL GUI管理界面 (pgAdmin)

### 访问pgAdmin
- **URL**: http://localhost:8080
- **用户名**: <EMAIL>
- **密码**: admin123

### 首次设置数据库连接
1. **登录pgAdmin**: 使用上述凭据登录
2. **添加服务器**: 右键点击"Servers" → "Register" → "Server"
3. **配置连接**:
   ```
   General Tab:
   - Name: SnowNavi Database

   Connection Tab:
   - Host name/address: snownavi-db
   - Port: 5432
   - Maintenance database: snownavi
   - Username: snownavi_user
   - Password: snownavi_password
   ```
4. **保存**: 点击"Save"完成连接设置

### 使用pgAdmin查看数据
1. **展开服务器**: 点击"SnowNavi Database"
2. **选择数据库**: 展开"Databases" → "snownavi"
3. **查看表**: 展开"Schemas" → "public" → "Tables"
4. **查看数据**: 右键点击表名 → "View/Edit Data" → "All Rows"

### 常用操作
- **查看表结构**: 右键表名 → "Properties"
- **执行SQL查询**: 点击"Query Tool"图标
- **导出数据**: 右键表名 → "Backup"
- **查看表关系**: 右键数据库 → "ERD Tool"

### pgAdmin服务管理
```bash
# 启动pgAdmin
docker compose up -d pgadmin

# 停止pgAdmin
docker compose stop pgadmin

# 重启pgAdmin
docker compose restart pgadmin

# 查看pgAdmin日志
docker logs snownavi-pgadmin
```
