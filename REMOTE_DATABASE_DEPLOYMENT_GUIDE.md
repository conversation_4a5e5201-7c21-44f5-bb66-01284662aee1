# 🗄️ SnowNavi 远程数据库部署指南

本指南详细说明如何将SnowNavi数据库部署到远程服务器(snownavi.ski)，以及如何从本地安全连接远程数据库。

## 📋 目录

1. [部署前准备](#部署前准备)
2. [服务器端部署](#服务器端部署)
3. [防火墙配置](#防火墙配置)
4. [本地连接配置](#本地连接配置)
5. [安全最佳实践](#安全最佳实践)
6. [故障排除](#故障排除)

## 🚀 部署前准备

### 服务器要求
- **操作系统**: Ubuntu 20.04+ 或 Debian 11+
- **内存**: 最少 2GB RAM (推荐 4GB+)
- **存储**: 最少 20GB 可用空间
- **网络**: 稳定的互联网连接
- **域名**: snownavi.ski 已解析到服务器IP

### 必需软件
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker和Docker Compose
sudo apt install -y docker.io docker-compose-plugin

# 安装防火墙工具
sudo apt install -y ufw

# 安装PostgreSQL客户端工具
sudo apt install -y postgresql-client
```

### SSH密钥配置
确保已配置SSH密钥认证：
```bash
# 在本地生成SSH密钥 (如果还没有)
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 复制公钥到服务器
ssh-copy-id <EMAIL>
```

## 🏗️ 服务器端部署

### 1. 准备项目文件
```bash
# 在服务器上
cd /home/<USER>/git/snownavi_website

# 创建生产环境配置
cp backend/.env.prod.example backend/.env.prod

# 编辑配置文件，设置强密码
nano backend/.env.prod
```

### 2. 配置环境变量
编辑 `backend/.env.prod` 文件：
```bash
# 设置强密码 (至少16位，包含大小写字母、数字、特殊字符)
POSTGRES_PASSWORD=YourVeryStrongPassword123!@#

# 其他重要配置
DATA_MODE=database
ENVIRONMENT=production
DEBUG=false
```

### 3. 创建数据目录
```bash
# 创建PostgreSQL数据目录
sudo mkdir -p /home/<USER>/git/snownavi_website/data/postgres
sudo chown -R lighthouse:lighthouse /home/<USER>/git/snownavi_website/data

# 创建备份目录
mkdir -p /home/<USER>/git/snownavi_website/database/backups
```

### 4. 启动数据库服务
```bash
# 启动生产环境数据库
docker compose --env-file backend/.env.prod -f docker-compose.prod.yml up -d db

# 检查服务状态
docker compose -f docker-compose.prod.yml ps

# 查看日志
docker compose -f docker-compose.prod.yml logs db
```

### 5. 运行数据迁移
```bash
# 等待数据库完全启动 (约30秒)
sleep 30

# 运行数据迁移
export DATABASE_URL="postgresql://snownavi_user:YourVeryStrongPassword123!@#@localhost:5432/snownavi"
python database/migrate/run_all_migrations.py
```

## 🔥 防火墙配置

### 自动配置 (推荐)
```bash
# 运行防火墙配置脚本
sudo backend/scripts/setup_firewall.sh
```

### 手动配置
```bash
# 重置防火墙
sudo ufw --force reset

# 设置默认策略
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许SSH (限制频率)
sudo ufw limit ssh

# 允许HTTP/HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 数据库端口 - 仅本地访问
# 不要开放5432到公网！

# 启用防火墙
sudo ufw --force enable
```

### 端口配置总结
| 端口 | 服务 | 访问权限 | 说明 |
|------|------|----------|------|
| 22 | SSH | 公网 | 限制频率 |
| 80 | HTTP | 公网 | 重定向到HTTPS |
| 443 | HTTPS | 公网 | 主要Web服务 |
| 5432 | PostgreSQL | 仅本地 | 数据库 |
| 8080 | pgAdmin | 仅本地 | 数据库管理 |
| 8899 | Flask | 仅本地 | 后端API |

## 🔗 本地连接配置

### 方法1: SSH隧道连接 (推荐)

#### 创建SSH隧道
```bash
# 使用连接脚本 (推荐)
backend/scripts/connect_remote_db.sh tunnel

# 或手动创建隧道
ssh -L 5432:localhost:5432 <EMAIL>
```

#### 配置本地环境
```bash
# 复制远程连接配置
cp backend/.env.remote.example backend/.env.remote

# 编辑配置
nano backend/.env.remote
```

设置连接字符串：
```bash
DATABASE_URL=postgresql://snownavi_user:YourProductionPassword@localhost:5432/snownavi
DATA_MODE=database
```

#### 测试连接
```bash
# 使用连接脚本测试
backend/scripts/connect_remote_db.sh test

# 或手动测试
python -c "
import psycopg2
conn = psycopg2.connect('postgresql://snownavi_user:password@localhost:5432/snownavi')
print('✅ 连接成功')
conn.close()
"
```

### 方法2: 直接连接 (不推荐)

⚠️ **安全警告**: 此方法需要开放数据库端口到公网，存在安全风险。

如果必须使用直接连接：
```bash
# 在服务器上允许特定IP访问数据库
sudo ufw allow from YOUR_LOCAL_IP to any port 5432

# 修改Docker配置暴露端口
# 在docker-compose.prod.yml中取消注释:
# ports:
#   - "5432:5432"
```

## 🛡️ 安全最佳实践

### 1. 密码安全
- 使用至少16位的强密码
- 包含大小写字母、数字、特殊字符
- 定期更换密码 (建议每3个月)
- 不要在代码中硬编码密码

### 2. 网络安全
- 始终使用SSH隧道连接数据库
- 不要开放数据库端口到公网
- 启用SSH密钥认证，禁用密码登录
- 定期更新系统和软件

### 3. 访问控制
- 限制数据库用户权限
- 使用防火墙限制端口访问
- 启用数据库连接日志
- 定期审查访问日志

### 4. 备份策略
```bash
# 启动自动备份服务
docker compose -f docker-compose.prod.yml up -d db-backup

# 手动备份
docker exec snownavi-db-prod pg_dump -U snownavi_user snownavi > backup.sql

# 下载备份到本地
scp <EMAIL>:/path/to/backup.sql ./local_backup.sql
```

### 5. 监控和日志
```bash
# 查看数据库日志
docker compose -f docker-compose.prod.yml logs db

# 查看防火墙日志
sudo tail -f /var/log/ufw.log

# 监控连接数
docker exec snownavi-db-prod psql -U snownavi_user -d snownavi -c "
SELECT count(*) as connections FROM pg_stat_activity;
"
```

## 🔧 故障排除

### 常见问题

#### 1. SSH连接失败
```bash
# 检查SSH服务
sudo systemctl status ssh

# 检查防火墙
sudo ufw status

# 测试连接
ssh -v <EMAIL>
```

#### 2. 数据库连接失败
```bash
# 检查数据库服务
docker compose -f docker-compose.prod.yml ps db

# 查看数据库日志
docker compose -f docker-compose.prod.yml logs db

# 检查端口
sudo netstat -tlnp | grep 5432
```

#### 3. SSH隧道问题
```bash
# 检查隧道状态
backend/scripts/connect_remote_db.sh status

# 关闭所有隧道
backend/scripts/connect_remote_db.sh kill

# 重新创建隧道
backend/scripts/connect_remote_db.sh tunnel
```

#### 4. 权限问题
```bash
# 检查文件权限
ls -la /home/<USER>/git/snownavi_website/data/

# 修复权限
sudo chown -R lighthouse:lighthouse /home/<USER>/git/snownavi_website/
```

### 紧急恢复

#### 数据库恢复
```bash
# 停止数据库
docker compose -f docker-compose.prod.yml stop db

# 恢复备份
docker compose -f docker-compose.prod.yml run --rm db psql -U snownavi_user snownavi < backup.sql

# 重启数据库
docker compose -f docker-compose.prod.yml start db
```

#### 回滚到JSON模式
```bash
# 修改环境变量
echo "DATA_MODE=json" >> backend/.env.prod

# 重启Flask应用
sudo systemctl restart snownavi-flask
```

## 📞 支持和联系

如果遇到问题：
1. 查看详细的错误日志
2. 检查网络连接和防火墙设置
3. 验证配置文件和密码
4. 参考本指南的故障排除部分

---

**⚠️ 重要提醒**: 
- 在生产环境操作前，请务必在测试环境验证所有步骤
- 定期备份数据库和配置文件
- 保持系统和软件更新
- 监控系统资源和安全日志
