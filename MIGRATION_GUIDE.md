# SnowNavi 数据迁移指南

## 概述

本指南将帮助您将 SnowNavi 项目从 JSON 文件存储迁移到 PostgreSQL 数据库存储。迁移过程支持三种数据存储模式，确保平滑过渡。

## 当前数据结构分析

项目当前使用以下 9 个 JSON 文件存储数据：

1. **activities.json** - 活动数据（4条记录）
2. **members.json** - 会员数据（3条记录）
3. **feedbacks.json** - 反馈数据（1条记录）
4. **checkins.json** - 签到数据（2条记录）
5. **courses.json** - 课程数据（5条记录）
6. **feedback_templates.json** - 反馈模板数据（3条记录）
7. **email_templates.json** - 邮件模板数据（1条记录）
8. **navigation.json** - 导航数据（1条记录）
9. **student_ratings.json** - 学生评分数据（1条记录）

## 数据存储模式

### 1. JSON 模式 (当前)
- 仅使用 JSON 文件存储数据
- 现有实现，无需更改

### 2. 数据库模式 (目标)
- 仅使用 PostgreSQL 数据库存储数据
- 最终目标状态

### 3. 混合模式 (过渡)
- 数据库优先，JSON 作为备用
- 推荐用于迁移过程

## 迁移步骤

### 第一步：环境准备

1. **验证当前设置**
```bash
python test_migration_setup.py
```

2. **配置环境变量**
```bash
# 编辑 backend/.env 文件
DATA_MODE=json  # 开始时保持 JSON 模式
DATABASE_URL=postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi
```

### 第二步：启动数据库

1. **启动 PostgreSQL 数据库**
```bash
docker compose up -d db
```

2. **等待数据库就绪**
```bash
docker compose logs -f db
```

3. **测试数据库连接**
```bash
python database/test_connection.py
```

### 第三步：运行数据迁移

1. **使用 Docker 运行迁移（推荐）**
```bash
docker compose run --rm migrate
```

2. **或使用本地 Python**
```bash
# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -r backend/requirements.txt

# 运行迁移
python database/migrate.py
```

### 第四步：验证迁移

1. **检查数据库表**
```bash
python database/test_connection.py
```

2. **验证数据一致性**
```bash
# 将创建验证脚本
python database/verify_migration.py
```

### 第五步：切换到混合模式

1. **更新环境配置**
```bash
# 编辑 backend/.env
DATA_MODE=hybrid
```

2. **重启 Flask 服务器**
```bash
# 如果使用 systemd
sudo systemctl restart snownavi-flask

# 如果手动运行
cd backend && python server.py
```

### 第六步：测试应用功能

1. **测试所有管理页面**
   - 会员管理
   - 活动管理
   - 签到管理
   - 反馈管理

2. **验证数据读写**
   - 创建新记录
   - 更新现有记录
   - 删除记录

### 第七步：切换到数据库模式

确认一切正常后：

1. **更新环境配置**
```bash
# 编辑 backend/.env
DATA_MODE=database
```

2. **重启服务器**
3. **最终测试**

## 数据库架构

### 主要表结构

- **members** - 会员信息
- **activities** - 课程活动
- **checkins** - 活动签到
- **feedbacks** - 学生反馈
- **feedback_templates** - 反馈模板
- **email_templates** - 邮件模板
- **navigation_items** - 网站导航
- **student_ratings** - 学生评分
- **courses** - 静态课程信息（JSONB 格式）

### 特性

- 自动时间戳更新
- 外键约束
- 索引优化
- JSONB 支持复杂数据结构

## 回滚计划

如需回滚到 JSON 模式：

1. **立即回滚**
```bash
# 编辑 backend/.env
DATA_MODE=json
```

2. **重启服务器**

JSON 文件在迁移过程中保持不变，回滚是即时的。

## 故障排除

### 数据库连接问题

1. **检查 PostgreSQL 状态**
```bash
docker compose ps
docker compose logs db
```

2. **手动测试连接**
```bash
psql postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi
```

### 迁移失败

1. **查看迁移日志**
```bash
docker compose logs migrate
```

2. **重新运行迁移**
```bash
docker compose run --rm migrate
```

### 数据不一致

1. **使用混合模式比较数据源**
2. **运行验证脚本**
3. **检查应用日志**

## 生产部署建议

1. **使用环境特定的 .env 文件**
2. **设置数据库备份**
3. **初期使用混合模式确保安全**
4. **监控应用日志**
5. **验证后切换到数据库模式**

## 开发说明

### 添加新表

1. 更新 `database/schema.sql`
2. 在 `database/migrate.py` 中添加迁移逻辑
3. 更新 `backend/database_access.py` 中的数据访问层

### 测试

```bash
# 运行 API 测试
python database/test_api.py
```

## 支持

如遇问题，请检查：

1. 应用日志
2. 数据库日志
3. 迁移脚本输出
4. 本文档

## 总结

这个迁移计划提供了：

✅ **完整的数据库架构设计**
✅ **Docker 环境支持**
✅ **统一的数据访问层**
✅ **三种存储模式支持**
✅ **自动化迁移脚本**
✅ **验证和测试工具**
✅ **详细的操作指南**
✅ **回滚机制**

迁移过程设计为渐进式，确保在任何阶段都可以安全回滚，同时保持应用的正常运行。
