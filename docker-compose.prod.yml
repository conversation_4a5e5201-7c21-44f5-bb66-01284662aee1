# SnowNavi 生产环境配置 - 远程服务器使用 (snownavi.ski)
# 基于现有backend架构的安全生产环境配置
version: '3.8'

services:
  # PostgreSQL 数据库服务
  db:
    image: postgres:15
    container_name: snownavi-db-prod
    environment:
      POSTGRES_DB: snownavi
      POSTGRES_USER: snownavi_user
      # 从环境变量文件读取密码
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      # 启用安全认证
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
    # 生产环境：仅内部网络访问，不暴露到公网
    expose:
      - "5432"
    # 可选：如需本地开发连接，可临时开启端口映射
    # ports:
    #   - "127.0.0.1:5432:5432"  # 仅绑定本地回环地址
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./database/backups:/backups  # 备份目录
    restart: unless-stopped
    networks:
      - snownavi-internal
    # 安全配置
    security_opt:
      - no-new-privileges:true
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U snownavi_user -d snownavi"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # pgAdmin 数据库管理工具 - 仅在维护时启用
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: snownavi-pgadmin-prod
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD}
      PGADMIN_CONFIG_SERVER_MODE: 'True'
      PGADMIN_CONFIG_ENHANCED_COOKIE_PROTECTION: 'True'
      PGADMIN_CONFIG_LOGIN_BANNER: '"SnowNavi Production Database - Authorized Access Only"'
    # 仅绑定到本地回环地址，通过SSH隧道访问
    ports:
      - "127.0.0.1:8080:80"
    volumes:
      - pgadmin_data_prod:/var/lib/pgadmin
    depends_on:
      db:
        condition: service_healthy
    restart: "no"  # 手动启动，不自动重启
    networks:
      - snownavi-internal
    # 安全配置
    security_opt:
      - no-new-privileges:true
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    # 配置文件
    profiles:
      - admin  # 使用 profile 控制是否启动

  # 数据库自动备份服务
  db-backup:
    image: postgres:15
    container_name: snownavi-backup
    environment:
      PGPASSWORD: ${POSTGRES_PASSWORD}
      BACKUP_RETENTION_DAYS: ${BACKUP_RETENTION_DAYS:-30}
    volumes:
      - ./database/backups:/backups
      - ./database/scripts/backup.sh:/backup.sh:ro
    depends_on:
      db:
        condition: service_healthy
    networks:
      - snownavi-internal
    # 使用cron执行定时备份
    command: >
      sh -c "
        chmod +x /backup.sh &&
        echo '0 2 * * * /backup.sh >> /var/log/backup.log 2>&1' | crontab - &&
        crond -f
      "
    restart: unless-stopped
    # 安全配置
    security_opt:
      - no-new-privileges:true

volumes:
  postgres_data_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /home/<USER>/git/snownavi_website/data/postgres  # 与现有架构一致
  pgadmin_data_prod:
    driver: local

networks:
  snownavi-internal:
    driver: bridge
    internal: false  # 允许容器访问外网（用于备份等）
    ipam:
      config:
        - subnet: **********/16
