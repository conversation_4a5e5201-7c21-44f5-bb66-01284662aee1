# SnowNavi 远程数据库连接配置
# 用于本地开发环境连接远程生产数据库
# 复制此文件为 backend/.env.remote 并填入实际配置

# =============================================================================
# 远程数据库连接配置
# =============================================================================

# 方法1: 直接连接 (需要防火墙开放5432端口给特定IP)
# DATABASE_URL=postgresql://snownavi_user:<EMAIL>:5432/snownavi

# 方法2: SSH隧道连接 (推荐，更安全)
# 先建立SSH隧道: ssh -L 5432:localhost:5432 <EMAIL>
# 然后使用本地连接
DATABASE_URL=postgresql://snownavi_user:YourProductionPassword@localhost:5432/snownavi

# 数据存储模式
DATA_MODE=database

# =============================================================================
# SSH隧道配置信息
# =============================================================================
# SSH连接信息 (仅供参考，实际连接需要在终端执行)
REMOTE_HOST=snownavi.ski
REMOTE_USER=lighthouse
REMOTE_DB_PORT=5432
LOCAL_TUNNEL_PORT=5432

# SSH隧道命令 (复制到终端执行):
# ssh -L 5432:localhost:5432 <EMAIL>

# 或者使用后台运行:
# ssh -f -N -L 5432:localhost:5432 <EMAIL>

# =============================================================================
# 本地开发配置
# =============================================================================
ENVIRONMENT=development
DEBUG=true
FLASK_ENV=development
FLASK_PORT=8899

# =============================================================================
# 安全提醒
# =============================================================================
# 1. 确保已配置SSH密钥认证
# 2. 不要在生产环境开放5432端口到公网
# 3. 使用SSH隧道是最安全的连接方式
# 4. 定期轮换数据库密码
# 5. 监控数据库连接日志

# =============================================================================
# 连接测试命令
# =============================================================================
# 测试SSH连接:
# ssh <EMAIL> "echo 'SSH连接成功'"

# 测试数据库连接 (需要先建立SSH隧道):
# psql postgresql://snownavi_user:password@localhost:5432/snownavi -c "SELECT version();"

# 或使用Python测试:
# python -c "
# import psycopg2
# try:
#     conn = psycopg2.connect('postgresql://snownavi_user:password@localhost:5432/snownavi')
#     print('✅ 数据库连接成功')
#     conn.close()
# except Exception as e:
#     print(f'❌ 数据库连接失败: {e}')
# "

# =============================================================================
# pgAdmin远程访问配置
# =============================================================================
# pgAdmin通过SSH隧道访问:
# ssh -L 8080:localhost:8080 <EMAIL>
# 然后访问: http://localhost:8080

# =============================================================================
# 备份和恢复
# =============================================================================
# 从远程下载备份:
# scp <EMAIL>:/home/<USER>/git/snownavi_website/database/backups/latest.sql ./

# 恢复到本地数据库:
# psql postgresql://snownavi_user:password@localhost:5432/snownavi < latest.sql
