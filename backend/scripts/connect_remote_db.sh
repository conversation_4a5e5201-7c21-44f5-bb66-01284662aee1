#!/bin/bash
# SnowNavi 远程数据库连接脚本
# 提供多种方式连接远程数据库

set -e

# 配置变量
REMOTE_HOST="snownavi.ski"
REMOTE_USER="lighthouse"
REMOTE_DB_PORT="5432"
LOCAL_TUNNEL_PORT="5432"
PGADMIN_PORT="8080"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}SnowNavi 远程数据库连接工具${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  tunnel     创建SSH隧道连接数据库"
    echo "  pgadmin    创建SSH隧道连接pgAdmin"
    echo "  test       测试数据库连接"
    echo "  psql       直接连接psql客户端"
    echo "  status     检查隧道状态"
    echo "  kill       关闭所有SSH隧道"
    echo "  help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 tunnel    # 创建数据库SSH隧道"
    echo "  $0 pgadmin   # 创建pgAdmin SSH隧道"
    echo "  $0 test      # 测试连接"
    echo ""
}

# 检查SSH连接
check_ssh() {
    echo -e "${YELLOW}🔍 检查SSH连接...${NC}"
    if ssh -o ConnectTimeout=5 -o BatchMode=yes "$REMOTE_USER@$REMOTE_HOST" "echo 'SSH连接正常'" 2>/dev/null; then
        echo -e "${GREEN}✅ SSH连接正常${NC}"
        return 0
    else
        echo -e "${RED}❌ SSH连接失败${NC}"
        echo "请检查:"
        echo "1. SSH密钥是否已配置"
        echo "2. 服务器是否可达"
        echo "3. 用户名和主机名是否正确"
        return 1
    fi
}

# 创建数据库SSH隧道
create_db_tunnel() {
    echo -e "${YELLOW}🚇 创建数据库SSH隧道...${NC}"
    
    # 检查端口是否已被占用
    if lsof -Pi :$LOCAL_TUNNEL_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  端口 $LOCAL_TUNNEL_PORT 已被占用${NC}"
        echo "正在检查是否为现有SSH隧道..."
        
        # 检查是否为SSH隧道
        if ps aux | grep -v grep | grep "ssh.*-L.*$LOCAL_TUNNEL_PORT:localhost:$REMOTE_DB_PORT" >/dev/null; then
            echo -e "${GREEN}✅ SSH隧道已存在${NC}"
            return 0
        else
            echo -e "${RED}❌ 端口被其他进程占用${NC}"
            echo "请先关闭占用端口的进程或使用其他端口"
            return 1
        fi
    fi
    
    # 创建SSH隧道
    echo "正在创建SSH隧道: localhost:$LOCAL_TUNNEL_PORT -> $REMOTE_HOST:$REMOTE_DB_PORT"
    ssh -f -N -L "$LOCAL_TUNNEL_PORT:localhost:$REMOTE_DB_PORT" "$REMOTE_USER@$REMOTE_HOST"
    
    # 等待隧道建立
    sleep 2
    
    # 验证隧道
    if lsof -Pi :$LOCAL_TUNNEL_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 数据库SSH隧道创建成功${NC}"
        echo "现在可以通过 localhost:$LOCAL_TUNNEL_PORT 连接远程数据库"
        return 0
    else
        echo -e "${RED}❌ SSH隧道创建失败${NC}"
        return 1
    fi
}

# 创建pgAdmin SSH隧道
create_pgadmin_tunnel() {
    echo -e "${YELLOW}🚇 创建pgAdmin SSH隧道...${NC}"
    
    # 检查端口是否已被占用
    if lsof -Pi :$PGADMIN_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  端口 $PGADMIN_PORT 已被占用${NC}"
        if ps aux | grep -v grep | grep "ssh.*-L.*$PGADMIN_PORT:localhost:$PGADMIN_PORT" >/dev/null; then
            echo -e "${GREEN}✅ pgAdmin SSH隧道已存在${NC}"
            echo "访问地址: http://localhost:$PGADMIN_PORT"
            return 0
        else
            echo -e "${RED}❌ 端口被其他进程占用${NC}"
            return 1
        fi
    fi
    
    # 创建SSH隧道
    echo "正在创建pgAdmin SSH隧道: localhost:$PGADMIN_PORT -> $REMOTE_HOST:$PGADMIN_PORT"
    ssh -f -N -L "$PGADMIN_PORT:localhost:$PGADMIN_PORT" "$REMOTE_USER@$REMOTE_HOST"
    
    # 等待隧道建立
    sleep 2
    
    # 验证隧道
    if lsof -Pi :$PGADMIN_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${GREEN}✅ pgAdmin SSH隧道创建成功${NC}"
        echo "访问地址: http://localhost:$PGADMIN_PORT"
        echo "用户名: <EMAIL>"
        echo "密码: (请查看生产环境配置)"
        return 0
    else
        echo -e "${RED}❌ pgAdmin SSH隧道创建失败${NC}"
        return 1
    fi
}

# 测试数据库连接
test_connection() {
    echo -e "${YELLOW}🧪 测试数据库连接...${NC}"
    
    # 检查是否有SSH隧道
    if ! lsof -Pi :$LOCAL_TUNNEL_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  未检测到SSH隧道，正在创建...${NC}"
        create_db_tunnel || return 1
    fi
    
    # 测试连接 (需要用户提供密码)
    echo "请输入数据库密码:"
    read -s DB_PASSWORD
    
    python3 -c "
import psycopg2
try:
    conn = psycopg2.connect('postgresql://snownavi_user:$DB_PASSWORD@localhost:$LOCAL_TUNNEL_PORT/snownavi')
    cursor = conn.cursor()
    cursor.execute('SELECT version();')
    version = cursor.fetchone()[0]
    print('✅ 数据库连接成功')
    print(f'数据库版本: {version}')
    cursor.execute('SELECT COUNT(*) FROM members;')
    count = cursor.fetchone()[0]
    print(f'成员数量: {count}')
    conn.close()
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    exit(1)
"
}

# 连接psql客户端
connect_psql() {
    echo -e "${YELLOW}🐘 连接psql客户端...${NC}"
    
    # 检查是否有SSH隧道
    if ! lsof -Pi :$LOCAL_TUNNEL_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  未检测到SSH隧道，正在创建...${NC}"
        create_db_tunnel || return 1
    fi
    
    echo "连接到远程数据库..."
    psql "postgresql://snownavi_user@localhost:$LOCAL_TUNNEL_PORT/snownavi"
}

# 检查隧道状态
check_status() {
    echo -e "${BLUE}📊 SSH隧道状态:${NC}"
    echo ""
    
    # 检查数据库隧道
    if lsof -Pi :$LOCAL_TUNNEL_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 数据库隧道 (端口 $LOCAL_TUNNEL_PORT): 活跃${NC}"
    else
        echo -e "${RED}❌ 数据库隧道 (端口 $LOCAL_TUNNEL_PORT): 未连接${NC}"
    fi
    
    # 检查pgAdmin隧道
    if lsof -Pi :$PGADMIN_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${GREEN}✅ pgAdmin隧道 (端口 $PGADMIN_PORT): 活跃${NC}"
    else
        echo -e "${RED}❌ pgAdmin隧道 (端口 $PGADMIN_PORT): 未连接${NC}"
    fi
    
    echo ""
    echo "活跃的SSH进程:"
    ps aux | grep -v grep | grep "ssh.*-L.*$REMOTE_HOST" || echo "无活跃的SSH隧道"
}

# 关闭所有SSH隧道
kill_tunnels() {
    echo -e "${YELLOW}🔪 关闭SSH隧道...${NC}"
    
    # 查找并关闭SSH隧道进程
    SSH_PIDS=$(ps aux | grep -v grep | grep "ssh.*-L.*$REMOTE_HOST" | awk '{print $2}')
    
    if [ -z "$SSH_PIDS" ]; then
        echo -e "${YELLOW}⚠️  未找到活跃的SSH隧道${NC}"
        return 0
    fi
    
    for pid in $SSH_PIDS; do
        echo "关闭进程 $pid"
        kill $pid
    done
    
    sleep 1
    echo -e "${GREEN}✅ SSH隧道已关闭${NC}"
}

# 主函数
main() {
    case "${1:-help}" in
        tunnel)
            check_ssh && create_db_tunnel
            ;;
        pgadmin)
            check_ssh && create_pgadmin_tunnel
            ;;
        test)
            check_ssh && test_connection
            ;;
        psql)
            check_ssh && connect_psql
            ;;
        status)
            check_status
            ;;
        kill)
            kill_tunnels
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 运行主函数
main "$@"
