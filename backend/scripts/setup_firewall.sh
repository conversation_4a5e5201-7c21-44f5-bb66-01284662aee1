#!/bin/bash
# <PERSON>Navi 生产环境防火墙配置脚本
# 适用于 Ubuntu/Debian 系统使用 ufw

set -e

echo "🔥 配置 SnowNavi 生产环境防火墙..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用 sudo 运行此脚本"
    exit 1
fi

# 安装 ufw (如果未安装)
if ! command -v ufw &> /dev/null; then
    echo "📦 安装 ufw..."
    apt update
    apt install -y ufw
fi

# 重置防火墙规则
echo "🔄 重置防火墙规则..."
ufw --force reset

# 设置默认策略
echo "🛡️ 设置默认策略..."
ufw default deny incoming
ufw default allow outgoing

# =============================================================================
# 基础服务端口
# =============================================================================

# SSH (22) - 允许SSH连接，但限制连接频率
echo "🔑 配置 SSH 访问..."
ufw limit ssh comment 'SSH with rate limiting'

# HTTP (80) - 重定向到HTTPS
echo "🌐 允许 HTTP (重定向到HTTPS)..."
ufw allow 80/tcp comment 'HTTP redirect to HTTPS'

# HTTPS (443) - 主要Web服务
echo "🔒 允许 HTTPS..."
ufw allow 443/tcp comment 'HTTPS web service'

# =============================================================================
# 应用特定端口 (仅本地访问)
# =============================================================================

# Flask应用 (8899) - 仅本地访问
echo "🐍 配置 Flask 应用端口 (仅本地)..."
ufw allow from 127.0.0.1 to any port 8899 comment 'Flask app local only'

# 路由服务端口 (5000-5002) - 仅本地访问
echo "🗺️ 配置路由服务端口 (仅本地)..."
ufw allow from 127.0.0.1 to any port 5000 comment 'Route service local only'
ufw allow from 127.0.0.1 to any port 5001 comment 'Morzine route service local only'
ufw allow from 127.0.0.1 to any port 5002 comment 'Palarinsal route service local only'

# Classic服务 (9966) - 仅本地访问
echo "🎿 配置 Classic 服务端口 (仅本地)..."
ufw allow from 127.0.0.1 to any port 9966 comment 'Classic service local only'

# =============================================================================
# 数据库端口配置
# =============================================================================

# PostgreSQL (5432) - 默认拒绝外部访问
echo "🗄️ 配置数据库访问..."
echo "   ⚠️  PostgreSQL 端口 5432 默认拒绝外部访问"
echo "   💡 如需远程连接，请使用 SSH 隧道"

# 可选：允许特定IP访问数据库 (取消注释并替换IP地址)
# ALLOWED_DB_IPS=("*************" "*********")
# for ip in "${ALLOWED_DB_IPS[@]}"; do
#     ufw allow from $ip to any port 5432 comment "PostgreSQL from $ip"
#     echo "   ✅ 允许 $ip 访问数据库"
# done

# pgAdmin (8080) - 仅本地访问
echo "🔧 配置 pgAdmin (仅本地)..."
ufw allow from 127.0.0.1 to any port 8080 comment 'pgAdmin local only'

# =============================================================================
# 安全增强配置
# =============================================================================

# 拒绝常见攻击端口
echo "🛡️ 拒绝常见攻击端口..."
BLOCKED_PORTS=(21 23 25 53 110 143 993 995 1433 3306 3389 5432 6379 27017)
for port in "${BLOCKED_PORTS[@]}"; do
    ufw deny $port comment "Block common attack port $port"
done

# 限制ping (ICMP)
echo "🏓 限制 ICMP (ping)..."
ufw allow from any to any port 0:65535 proto icmp comment 'Allow ICMP'

# =============================================================================
# 日志配置
# =============================================================================

# 启用日志记录
echo "📝 启用防火墙日志..."
ufw logging on

# =============================================================================
# 启用防火墙
# =============================================================================

echo "🚀 启用防火墙..."
ufw --force enable

# =============================================================================
# 显示配置结果
# =============================================================================

echo ""
echo "✅ 防火墙配置完成！"
echo ""
echo "📋 当前防火墙状态："
ufw status verbose

echo ""
echo "🔍 开放的端口："
echo "  - 22 (SSH) - 限制频率"
echo "  - 80 (HTTP) - 重定向到HTTPS"
echo "  - 443 (HTTPS) - 主要Web服务"
echo ""
echo "🏠 仅本地访问的端口："
echo "  - 5432 (PostgreSQL) - 数据库"
echo "  - 8080 (pgAdmin) - 数据库管理"
echo "  - 8899 (Flask) - 后端API"
echo "  - 5000-5002 (路由服务)"
echo "  - 9966 (Classic服务)"
echo ""
echo "💡 远程数据库连接方法："
echo "   ssh -L 5432:localhost:5432 <EMAIL>"
echo "   然后连接到 localhost:5432"
echo ""
echo "⚠️  重要提醒："
echo "   1. 确保SSH密钥认证已配置"
echo "   2. 定期更新系统和防火墙规则"
echo "   3. 监控防火墙日志: tail -f /var/log/ufw.log"
echo ""
