#!/bin/bash
# SnowNavi 生产环境一键部署脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_DIR="/home/<USER>/git/snownavi_website"
ENV_FILE="$PROJECT_DIR/backend/.env.prod"
COMPOSE_FILE="$PROJECT_DIR/docker-compose.prod.yml"

echo -e "${BLUE}🚀 SnowNavi 生产环境部署脚本${NC}"
echo "=================================="

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    echo -e "${RED}❌ 请不要使用root用户运行此脚本${NC}"
    echo "建议使用lighthouse用户"
    exit 1
fi

# 检查当前目录
if [ ! -f "docker-compose.yml" ]; then
    echo -e "${RED}❌ 请在项目根目录运行此脚本${NC}"
    exit 1
fi

echo -e "${YELLOW}📋 部署前检查...${NC}"

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker未安装${NC}"
    echo "请先安装Docker: sudo apt install docker.io"
    exit 1
fi

# 检查Docker Compose
if ! docker compose version &> /dev/null; then
    echo -e "${RED}❌ Docker Compose未安装${NC}"
    echo "请先安装Docker Compose"
    exit 1
fi

# 检查用户是否在docker组
if ! groups $USER | grep -q docker; then
    echo -e "${YELLOW}⚠️  用户不在docker组中${NC}"
    echo "添加用户到docker组: sudo usermod -aG docker $USER"
    echo "然后重新登录"
    exit 1
fi

echo -e "${GREEN}✅ 环境检查通过${NC}"

# 创建环境配置文件
echo -e "${YELLOW}⚙️  配置环境变量...${NC}"

if [ ! -f "$ENV_FILE" ]; then
    echo "创建生产环境配置文件..."
    cp backend/.env.prod.example "$ENV_FILE"
    
    echo -e "${YELLOW}🔐 请设置数据库密码${NC}"
    echo "请输入PostgreSQL密码 (至少16位，包含大小写字母、数字、特殊字符):"
    read -s POSTGRES_PASSWORD
    
    # 验证密码强度
    if [ ${#POSTGRES_PASSWORD} -lt 16 ]; then
        echo -e "${RED}❌ 密码长度不足16位${NC}"
        exit 1
    fi
    
    # 更新配置文件
    sed -i "s/YourVeryStrongPassword123!@#/$POSTGRES_PASSWORD/g" "$ENV_FILE"
    
    echo -e "${GREEN}✅ 环境配置文件已创建${NC}"
else
    echo -e "${GREEN}✅ 环境配置文件已存在${NC}"
fi

# 创建必要目录
echo -e "${YELLOW}📁 创建数据目录...${NC}"
mkdir -p data/postgres
mkdir -p database/backups
mkdir -p logs

echo -e "${GREEN}✅ 目录创建完成${NC}"

# 停止现有服务
echo -e "${YELLOW}🛑 停止现有服务...${NC}"
if docker compose -f docker-compose.yml ps | grep -q "Up"; then
    docker compose -f docker-compose.yml down
fi

if docker compose -f "$COMPOSE_FILE" ps | grep -q "Up"; then
    docker compose --env-file "$ENV_FILE" -f "$COMPOSE_FILE" down
fi

# 启动生产环境数据库
echo -e "${YELLOW}🗄️  启动数据库服务...${NC}"
docker compose --env-file "$ENV_FILE" -f "$COMPOSE_FILE" up -d db

# 等待数据库启动
echo -e "${YELLOW}⏳ 等待数据库启动...${NC}"
sleep 30

# 检查数据库状态
if ! docker compose --env-file "$ENV_FILE" -f "$COMPOSE_FILE" ps db | grep -q "Up"; then
    echo -e "${RED}❌ 数据库启动失败${NC}"
    echo "查看日志:"
    docker compose --env-file "$ENV_FILE" -f "$COMPOSE_FILE" logs db
    exit 1
fi

echo -e "${GREEN}✅ 数据库启动成功${NC}"

# 运行数据迁移
echo -e "${YELLOW}📊 运行数据迁移...${NC}"

# 设置环境变量
export DATABASE_URL="postgresql://snownavi_user:$POSTGRES_PASSWORD@localhost:5432/snownavi"
export DATA_MODE="database"

# 运行迁移
if python database/migrate/run_all_migrations.py; then
    echo -e "${GREEN}✅ 数据迁移完成${NC}"
else
    echo -e "${RED}❌ 数据迁移失败${NC}"
    exit 1
fi

# 启动备份服务
echo -e "${YELLOW}💾 启动备份服务...${NC}"
docker compose --env-file "$ENV_FILE" -f "$COMPOSE_FILE" up -d db-backup

# 配置防火墙 (可选)
echo -e "${YELLOW}🔥 是否配置防火墙? (y/n)${NC}"
read -r configure_firewall
if [[ $configure_firewall =~ ^[Yy]$ ]]; then
    if [ -f "backend/scripts/setup_firewall.sh" ]; then
        echo "配置防火墙..."
        sudo backend/scripts/setup_firewall.sh
    else
        echo -e "${YELLOW}⚠️  防火墙脚本不存在，跳过配置${NC}"
    fi
fi

# 更新Flask服务配置
echo -e "${YELLOW}🐍 更新Flask服务配置...${NC}"
if [ -f "backend/systemd/snownavi-flask.service" ]; then
    # 更新systemd服务文件中的环境文件路径
    sudo sed -i "s|EnvironmentFile=.*|EnvironmentFile=$ENV_FILE|" backend/systemd/snownavi-flask.service
    
    # 重新加载systemd配置
    sudo systemctl daemon-reload
    
    # 重启Flask服务
    sudo systemctl restart snownavi-flask
    
    echo -e "${GREEN}✅ Flask服务已更新${NC}"
else
    echo -e "${YELLOW}⚠️  Flask服务文件不存在，请手动配置${NC}"
fi

# 验证部署
echo -e "${YELLOW}🧪 验证部署...${NC}"

# 检查数据库连接
if docker exec snownavi-db-prod psql -U snownavi_user -d snownavi -c "SELECT 1;" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 数据库连接正常${NC}"
else
    echo -e "${RED}❌ 数据库连接失败${NC}"
fi

# 检查数据
MEMBER_COUNT=$(docker exec snownavi-db-prod psql -U snownavi_user -d snownavi -t -c "SELECT COUNT(*) FROM members;" 2>/dev/null | xargs)
if [ "$MEMBER_COUNT" -gt 0 ]; then
    echo -e "${GREEN}✅ 数据迁移验证通过 (成员数: $MEMBER_COUNT)${NC}"
else
    echo -e "${YELLOW}⚠️  数据验证警告 (成员数: $MEMBER_COUNT)${NC}"
fi

# 显示部署结果
echo ""
echo -e "${BLUE}🎉 部署完成！${NC}"
echo "=================================="
echo -e "${GREEN}✅ PostgreSQL数据库: 运行中${NC}"
echo -e "${GREEN}✅ 自动备份服务: 运行中${NC}"
echo -e "${GREEN}✅ 数据迁移: 完成${NC}"
echo ""
echo -e "${BLUE}📊 服务状态:${NC}"
docker compose --env-file "$ENV_FILE" -f "$COMPOSE_FILE" ps
echo ""
echo -e "${BLUE}🔗 连接信息:${NC}"
echo "数据库: localhost:5432 (仅内部访问)"
echo "pgAdmin: localhost:8080 (通过SSH隧道)"
echo ""
echo -e "${BLUE}💡 下一步:${NC}"
echo "1. 配置SSL证书"
echo "2. 设置域名解析"
echo "3. 配置Nginx反向代理"
echo "4. 测试应用功能"
echo ""
echo -e "${YELLOW}⚠️  重要提醒:${NC}"
echo "- 数据库密码已保存在 $ENV_FILE"
echo "- 请妥善保管配置文件"
echo "- 定期检查备份文件"
echo "- 监控系统资源使用"
echo ""
echo -e "${BLUE}📖 详细文档: REMOTE_DATABASE_DEPLOYMENT_GUIDE.md${NC}"
