"""
Database Access Layer for SnowNavi
Provides unified interface for both JSON file and PostgreSQL database storage
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataAccessLayer:
    """
    Unified data access layer supporting both JSON and PostgreSQL storage

    Storage Modes:
    - 'json': Uses only JSON files for data storage
    - 'database': Uses only PostgreSQL database (FAIL FAST - no JSON fallback)
    - 'hybrid': Uses database with JSON fallback for fault tolerance
    """

    def __init__(self, mode='json', database_url=None):
        """
        Initialize data access layer

        Args:
            mode: 'json', 'database', or 'hybrid'
                - 'json': JSON files only
                - 'database': Database only (fails fast on database errors)
                - 'hybrid': Database with JSON fallback
            database_url: PostgreSQL connection string
        """
        self.mode = mode
        self.database_url = database_url or os.getenv('DATABASE_URL')
        self.data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
        
        # Database connection
        self.engine = None
        self.Session = None
        
        if mode in ['database', 'hybrid']:
            self._init_database()
    
    def _init_database(self):
        """Initialize database connection"""
        try:
            if self.database_url:
                self.engine = create_engine(self.database_url)
                self.Session = sessionmaker(bind=self.engine)
                logger.info("Database connection initialized successfully")
            else:
                logger.warning("No database URL provided, falling back to JSON mode")
                self.mode = 'json'
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            if self.mode == 'hybrid':
                logger.info("Falling back to JSON mode")
                self.mode = 'json'
            else:
                raise
    
    def _format_datetime_for_json(self, dt):
        """格式化日期时间为JSON兼容格式"""
        if dt is None:
            return None

        # 转换为UTC时间并格式化为JSON格式
        if hasattr(dt, 'microsecond') and dt.microsecond:
            # 有微秒的情况
            return dt.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
        else:
            # 没有微秒的情况
            return dt.strftime('%Y-%m-%dT%H:%M:%SZ')

    def _get_json_file_path(self, entity_type: str) -> str:
        """Get JSON file path for entity type"""
        file_mapping = {
            'members': 'members.json',
            'activities': 'activities.json',
            'checkins': 'checkins.json',
            'feedbacks': 'feedbacks.json',
            'feedback_templates': 'feedback_templates.json',
            'email_templates': 'email_templates.json',
            'navigation': 'navigation.json',
            'courses': 'courses.json',
            'student_ratings': 'student_ratings.json'
        }
        return os.path.join(self.data_dir, file_mapping.get(entity_type, f'{entity_type}.json'))
    
    def _load_json_data(self, entity_type: str) -> Dict:
        """Load data from JSON file"""
        file_path = self._get_json_file_path(entity_type)
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Error loading JSON data for {entity_type}: {e}")
            return {}
    
    def _save_json_data(self, entity_type: str, data: Dict) -> bool:
        """Save data to JSON file"""
        file_path = self._get_json_file_path(entity_type)
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving JSON data for {entity_type}: {e}")
            return False
    
    def get_all(self, entity_type: str) -> Dict:
        """Get all records for an entity type"""
        if self.mode == 'json':
            return self._load_json_data(entity_type)
        elif self.mode == 'database':
            # Database mode: fail fast, no fallback
            return self._get_all_from_database(entity_type)
        else:  # hybrid mode
            try:
                return self._get_all_from_database(entity_type)
            except Exception as e:
                logger.warning(f"Database query failed, falling back to JSON: {e}")
                return self._load_json_data(entity_type)
    
    def get_by_id(self, entity_type: str, entity_id: str) -> Optional[Dict]:
        """Get a single record by ID"""
        all_data = self.get_all(entity_type)
        return all_data.get(entity_id)
    
    def create(self, entity_type: str, entity_id: str, data: Dict) -> bool:
        """Create a new record"""
        if self.mode == 'json':
            return self._create_in_json(entity_type, entity_id, data)
        elif self.mode == 'database':
            # Database mode: fail fast, no fallback
            return self._create_in_database(entity_type, entity_id, data)
        else:  # hybrid mode
            # Try database first, then JSON as backup
            try:
                db_success = self._create_in_database(entity_type, entity_id, data)
            except Exception as e:
                logger.warning(f"Database create failed: {e}")
                db_success = False
            json_success = self._create_in_json(entity_type, entity_id, data)
            return db_success or json_success
    
    def update(self, entity_type: str, entity_id: str, data: Dict) -> bool:
        """Update an existing record"""
        if self.mode == 'json':
            return self._update_in_json(entity_type, entity_id, data)
        elif self.mode == 'database':
            # Database mode: fail fast, no fallback
            return self._update_in_database(entity_type, entity_id, data)
        else:  # hybrid mode
            try:
                db_success = self._update_in_database(entity_type, entity_id, data)
            except Exception as e:
                logger.warning(f"Database update failed: {e}")
                db_success = False
            json_success = self._update_in_json(entity_type, entity_id, data)
            return db_success or json_success
    
    def delete(self, entity_type: str, entity_id: str) -> bool:
        """Delete a record"""
        if self.mode == 'json':
            return self._delete_from_json(entity_type, entity_id)
        elif self.mode == 'database':
            # Database mode: fail fast, no fallback
            return self._delete_from_database(entity_type, entity_id)
        else:  # hybrid mode
            try:
                db_success = self._delete_from_database(entity_type, entity_id)
            except Exception as e:
                logger.warning(f"Database delete failed: {e}")
                db_success = False
            json_success = self._delete_from_json(entity_type, entity_id)
            return db_success or json_success
    
    def _create_in_json(self, entity_type: str, entity_id: str, data: Dict) -> bool:
        """Create record in JSON file"""
        all_data = self._load_json_data(entity_type)
        all_data[entity_id] = data
        return self._save_json_data(entity_type, all_data)
    
    def _update_in_json(self, entity_type: str, entity_id: str, data: Dict) -> bool:
        """Update record in JSON file"""
        all_data = self._load_json_data(entity_type)
        if entity_id in all_data:
            all_data[entity_id] = data
            return self._save_json_data(entity_type, all_data)
        return False
    
    def _delete_from_json(self, entity_type: str, entity_id: str) -> bool:
        """Delete record from JSON file"""
        all_data = self._load_json_data(entity_type)
        if entity_id in all_data:
            del all_data[entity_id]
            return self._save_json_data(entity_type, all_data)
        return False
    
    def _get_all_from_database(self, entity_type: str) -> Dict:
        """Get all records from database"""
        if not self.engine:
            raise Exception("Database not initialized")
        
        try:
            with self.engine.connect() as conn:
                if entity_type == 'members':
                    result = conn.execute(text("SELECT * FROM members"))
                    records = {}
                    for row in result:
                        validity_period = ""
                        if hasattr(row, 'validity_period_start') and hasattr(row, 'validity_period_end') and row.validity_period_start and row.validity_period_end:
                            validity_period = f"{row.validity_period_start.strftime('%d/%m/%Y')} - {row.validity_period_end.strftime('%d/%m/%Y')}"

                        # 构建记录，只包含非None的字段
                        record = {
                            'id': row.id,
                            'name': row.name,
                            'isActive': row.is_active,
                            'validityPeriod': validity_period,
                            'gender': row.gender
                        }

                        # 只有当字段不为None时才添加
                        if row.email is not None:
                            record['email'] = row.email
                        if row.password is not None:
                            record['password'] = row.password

                        records[row.id] = record
                    return records
                
                elif entity_type == 'activities':
                    # Get activities directly from main table (no separate translations table)
                    result = conn.execute(text("SELECT * FROM activities"))
                    records = {}
                    for row in result:
                        # 构建记录，处理可选字段
                        record = {
                            'id': row.id,
                            'name': {
                                'en': row.name_en,
                                'zh': row.name_zh,
                                'nl': row.name_nl
                            },
                            'type': row.type,
                            'date': str(row.date) if row.date else None,
                            'location': {
                                'en': row.location_en,
                                'zh': row.location_zh,
                                'nl': row.location_nl
                            },
                            'description': {
                                'en': row.description_en,
                                'zh': row.description_zh,
                                'nl': row.description_nl
                            },
                            'status': row.status,
                            'createdAt': self._format_datetime_for_json(row.created_at),
                            'updatedAt': self._format_datetime_for_json(row.updated_at)
                        }

                        # 只有当feedbackTemplateId不为None时才添加
                        if row.feedback_template_id is not None:
                            record['feedbackTemplateId'] = row.feedback_template_id

                        records[row.id] = record
                    return records
                
                elif entity_type == 'checkins':
                    result = conn.execute(text("SELECT * FROM checkins"))
                    records = {}
                    for row in result:
                        records[row.id] = {
                            'id': row.id,
                            'activityId': row.activity_id,
                            'memberId': row.member_id,
                            'memberName': row.member_name,
                            'memberType': row.member_type,
                            'checkinTime': self._format_datetime_for_json(row.checkin_time),
                            'checkinBy': row.checkin_by,
                            'checkinByEmail': row.checkin_by_email,
                            'notes': row.notes,
                            'createdAt': self._format_datetime_for_json(row.created_at)
                        }
                    return records

                elif entity_type == 'feedbacks':
                    result = conn.execute(text("SELECT * FROM feedbacks"))
                    records = {}
                    for row in result:
                        # 构建基础记录
                        record = {
                            'id': row.id,
                            'checkinId': row.checkin_id,
                            'activityId': row.activity_id,
                            'memberId': row.member_id,
                            'memberName': row.member_name,
                            'overallFeedback': row.overall_feedback,
                            'skillAssessment': json.loads(row.skill_assessment) if isinstance(row.skill_assessment, str) else (row.skill_assessment or {}),
                            'feedback': row.feedback,
                            'createdBy': row.created_by,
                            'createdByEmail': row.created_by_email,
                            'createdAt': self._format_datetime_for_json(row.created_at),
                            'updatedAt': self._format_datetime_for_json(row.updated_at)
                        }

                        # 添加可选字段
                        if hasattr(row, 'updated_by') and row.updated_by is not None:
                            record['updatedBy'] = row.updated_by
                        if hasattr(row, 'updated_by_email') and row.updated_by_email is not None:
                            record['updatedByEmail'] = row.updated_by_email

                        # 只有当studentRating不为None时才添加
                        if row.student_rating is not None:
                            record['studentRating'] = json.loads(row.student_rating) if isinstance(row.student_rating, str) else row.student_rating

                        records[row.checkin_id] = record
                    return records

                elif entity_type == 'feedback_templates':
                    # Get feedback templates directly from main table
                    result = conn.execute(text("SELECT * FROM feedback_templates"))
                    records = {}
                    for row in result:
                        records[row.id] = {
                            'id': row.id,
                            'name': {
                                'en': row.name_en,
                                'zh': row.name_zh,
                                'nl': row.name_nl
                            },
                            'description': {
                                'en': row.description_en,
                                'zh': row.description_zh,
                                'nl': row.description_nl
                            },
                            'targetLevel': row.target_level,
                            'sport': row.sport,
                            'sections': json.loads(row.sections) if isinstance(row.sections, str) else (row.sections or {}),
                            'version': row.version,
                            'createdBy': row.created_by,
                            'createdAt': self._format_datetime_for_json(row.created_at),
                            'updatedAt': self._format_datetime_for_json(row.updated_at)
                        }
                    return records

                elif entity_type == 'courses':
                    # Get courses directly from main table (stored as JSONB)
                    result = conn.execute(text("SELECT * FROM courses"))
                    records = {}
                    for row in result:
                        # Courses are stored as JSON data in the database
                        if hasattr(row, 'course_data') and row.course_data:
                            course_data = json.loads(row.course_data) if isinstance(row.course_data, str) else row.course_data
                            records[row.id] = course_data
                        else:
                            # Fallback for simple structure
                            records[row.id] = {
                                'id': row.id,
                                'data': 'Course data'
                            }
                    return records

                elif entity_type == 'navigation':
                    # Get navigation items directly from main table
                    result = conn.execute(text("SELECT * FROM navigation_items ORDER BY order_index"))

                    # Navigation has a different structure - it's wrapped in an "items" array
                    items = []
                    for row in result:
                        items.append({
                            'id': row.id,
                            'translations': {
                                'en': row.translation_en,
                                'zh': row.translation_zh,
                                'nl': row.translation_nl
                            },
                            'url': row.url,
                            'visible': row.visible,
                            'order': row.order_index
                        })

                    return {'items': items}

                elif entity_type == 'email_templates':
                    result = conn.execute(text("SELECT * FROM email_templates"))
                    records = {}
                    for row in result:
                        records[row.id] = {
                            'id': row.id,
                            'name': row.name,
                            'subject': row.subject,
                            'description': row.description,
                            'htmlContent': row.html_content,
                            'active': row.active,
                            'triggers': row.triggers,  # JSON field
                            'type': row.type,
                            'createdAt': self._format_datetime_for_json(row.created_at),
                            'updatedAt': self._format_datetime_for_json(row.updated_at)
                        }
                    return records

                elif entity_type == 'student_ratings':
                    result = conn.execute(text("SELECT * FROM student_ratings"))
                    records = {}
                    for row in result:
                        records[row.id] = {
                            'id': row.id,
                            'feedbackId': row.feedback_id,
                            'studentEmail': row.student_email,
                            'studentName': row.student_name,
                            'memberId': row.member_id,
                            'activityId': row.activity_id,
                            'instructorName': row.instructor_name,
                            'rating': row.rating,
                            'comment': row.comment,
                            'submittedAt': self._format_datetime_for_json(row.submitted_at)
                        }
                    return records

                elif entity_type == 'courses':
                    result = conn.execute(text("SELECT * FROM courses"))
                    # For courses, we need to combine all course records into a single structure
                    # that matches the expected courses.json format
                    courses_data = {}
                    for row in result:
                        # Each row contains course data for a specific course category
                        if isinstance(row.course_data, dict):
                            courses_data[row.id] = row.course_data
                    return courses_data

                # Add more entity types as needed
                else:
                    logger.warning(f"Database operations for {entity_type} not yet implemented")
                    raise NotImplementedError(f"Database operations for {entity_type} not yet implemented")
                    
        except Exception as e:
            logger.error(f"Error getting all {entity_type} from database: {e}")
            raise
    
    def _create_in_database(self, entity_type: str, entity_id: str, data: Dict) -> bool:
        """Create record in database"""
        if not self.engine:
            raise Exception("Database not initialized")

        try:
            with self.engine.begin() as conn:  # Use begin() for transaction
                if entity_type == 'members':
                    # Parse validity period
                    validity_start = None
                    validity_end = None
                    if 'validityPeriod' in data and isinstance(data['validityPeriod'], str) and ' - ' in data['validityPeriod']:
                        start_str, end_str = data['validityPeriod'].split(' - ')
                        try:
                            validity_start = datetime.strptime(start_str.strip(), '%d/%m/%Y').date()
                            validity_end = datetime.strptime(end_str.strip(), '%d/%m/%Y').date()
                        except ValueError:
                            pass

                    conn.execute(text("""
                        INSERT INTO members (id, name, email, is_active, validity_period_start, validity_period_end, password, gender)
                        VALUES (:id, :name, :email, :is_active, :validity_period_start, :validity_period_end, :password, :gender)
                    """), {
                        'id': entity_id,
                        'name': data.get('name'),
                        'email': data.get('email'),
                        'is_active': data.get('isActive', True),
                        'validity_period_start': validity_start,
                        'validity_period_end': validity_end,
                        'password': data.get('password'),
                        'gender': data.get('gender')
                    })
                    return True

                elif entity_type == 'checkins':
                    # Parse checkin time
                    checkin_time = None
                    if 'checkinTime' in data:
                        try:
                            checkin_time = datetime.fromisoformat(data['checkinTime'].replace('Z', '+00:00'))
                        except ValueError:
                            pass

                    conn.execute(text("""
                        INSERT INTO checkins (id, activity_id, member_id, member_name, member_type, checkin_time, checkin_by, checkin_by_email, notes)
                        VALUES (:id, :activity_id, :member_id, :member_name, :member_type, :checkin_time, :checkin_by, :checkin_by_email, :notes)
                    """), {
                        'id': entity_id,
                        'activity_id': data.get('activityId'),
                        'member_id': data.get('memberId'),
                        'member_name': data.get('memberName'),
                        'member_type': data.get('memberType'),
                        'checkin_time': checkin_time,
                        'checkin_by': data.get('checkinBy'),
                        'checkin_by_email': data.get('checkinByEmail'),
                        'notes': data.get('notes')
                    })
                    return True

                elif entity_type == 'feedbacks':
                    # Note: created_at and updated_at are handled by database triggers

                    conn.execute(text("""
                        INSERT INTO feedbacks (id, checkin_id, activity_id, member_id, member_name,
                                             overall_feedback, skill_assessment, feedback,
                                             created_by, created_by_email, student_rating)
                        VALUES (:id, :checkin_id, :activity_id, :member_id, :member_name,
                                :overall_feedback, :skill_assessment, :feedback,
                                :created_by, :created_by_email, :student_rating)
                    """), {
                        'id': entity_id,
                        'checkin_id': data.get('checkinId'),
                        'activity_id': data.get('activityId'),
                        'member_id': data.get('memberId'),
                        'member_name': data.get('memberName'),
                        'overall_feedback': data.get('overallFeedback'),
                        'skill_assessment': json.dumps(data.get('skillAssessment', {})),
                        'feedback': data.get('feedback'),
                        'created_by': data.get('createdBy'),
                        'created_by_email': data.get('createdByEmail'),
                        'student_rating': json.dumps(data.get('studentRating', {})) if data.get('studentRating') else None
                    })
                    return True

                elif entity_type == 'activities':
                    # Parse date and time
                    activity_date = None
                    if 'date' in data:
                        try:
                            activity_date = datetime.strptime(data['date'], '%Y-%m-%d').date()
                        except ValueError:
                            pass

                    created_at = None
                    updated_at = None
                    if 'createdAt' in data:
                        try:
                            created_at = datetime.fromisoformat(data['createdAt'].replace('Z', '+00:00'))
                        except ValueError:
                            pass
                    if 'updatedAt' in data:
                        try:
                            updated_at = datetime.fromisoformat(data['updatedAt'].replace('Z', '+00:00'))
                        except ValueError:
                            pass

                    conn.execute(text("""
                        INSERT INTO activities (
                            id, name_en, name_zh, name_nl, type, date,
                            location_en, location_zh, location_nl,
                            description_en, description_zh, description_nl,
                            status, feedback_template_id, created_at, updated_at
                        )
                        VALUES (:id, :name_en, :name_zh, :name_nl, :type, :date,
                                :location_en, :location_zh, :location_nl,
                                :description_en, :description_zh, :description_nl,
                                :status, :feedback_template_id, :created_at, :updated_at)
                    """), {
                        'id': entity_id,
                        'name_en': data.get('name', {}).get('en'),
                        'name_zh': data.get('name', {}).get('zh'),
                        'name_nl': data.get('name', {}).get('nl'),
                        'type': data.get('type'),
                        'date': activity_date,
                        'location_en': data.get('location', {}).get('en'),
                        'location_zh': data.get('location', {}).get('zh'),
                        'location_nl': data.get('location', {}).get('nl'),
                        'description_en': data.get('description', {}).get('en'),
                        'description_zh': data.get('description', {}).get('zh'),
                        'description_nl': data.get('description', {}).get('nl'),
                        'status': data.get('status', 'active'),
                        'feedback_template_id': data.get('feedbackTemplateId'),
                        'created_at': created_at,
                        'updated_at': updated_at
                    })
                    return True

                elif entity_type == 'student_ratings':
                    # Parse submitted time
                    submitted_at = None
                    if 'submittedAt' in data:
                        try:
                            submitted_at = datetime.fromisoformat(data['submittedAt'].replace('Z', '+00:00'))
                        except ValueError:
                            pass

                    conn.execute(text("""
                        INSERT INTO student_ratings (
                            id, feedback_id, student_email, student_name, member_id,
                            activity_id, instructor_name, rating, comment, submitted_at
                        )
                        VALUES (:id, :feedback_id, :student_email, :student_name, :member_id,
                                :activity_id, :instructor_name, :rating, :comment, :submitted_at)
                    """), {
                        'id': entity_id,
                        'feedback_id': data.get('feedbackId'),
                        'student_email': data.get('studentEmail'),
                        'student_name': data.get('studentName'),
                        'member_id': data.get('memberId'),
                        'activity_id': data.get('activityId'),
                        'instructor_name': data.get('instructorName'),
                        'rating': data.get('rating'),
                        'comment': data.get('comment'),
                        'submitted_at': submitted_at
                    })
                    return True

                elif entity_type == 'feedback_templates':
                    # Parse timestamps
                    created_at = None
                    updated_at = None
                    if 'createdAt' in data:
                        try:
                            created_at = datetime.fromisoformat(data['createdAt'].replace('Z', '+00:00'))
                        except ValueError:
                            pass
                    if 'updatedAt' in data:
                        try:
                            updated_at = datetime.fromisoformat(data['updatedAt'].replace('Z', '+00:00'))
                        except ValueError:
                            pass

                    conn.execute(text("""
                        INSERT INTO feedback_templates (id, name_en, name_zh, name_nl, description_en, description_zh, description_nl,
                                                       target_level, sport, sections, version, created_by, created_at, updated_at)
                        VALUES (:id, :name_en, :name_zh, :name_nl, :description_en, :description_zh, :description_nl,
                                :target_level, :sport, :sections, :version, :created_by, :created_at, :updated_at)
                    """), {
                        'id': entity_id,
                        'name_en': data.get('name', {}).get('en', ''),
                        'name_zh': data.get('name', {}).get('zh', ''),
                        'name_nl': data.get('name', {}).get('nl', ''),
                        'description_en': data.get('description', {}).get('en', ''),
                        'description_zh': data.get('description', {}).get('zh', ''),
                        'description_nl': data.get('description', {}).get('nl', ''),
                        'target_level': data.get('targetLevel', 'beginner'),
                        'sport': data.get('sport', 'snowboard'),
                        'sections': json.dumps(data.get('sections', {})),
                        'version': data.get('version', '1.0'),
                        'created_by': data.get('createdBy', 'admin'),
                        'created_at': created_at or datetime.now(),
                        'updated_at': updated_at or datetime.now()
                    })
                    return True

                elif entity_type == 'email_templates':
                    # Parse timestamps
                    created_at = None
                    updated_at = None
                    if 'createdAt' in data:
                        try:
                            created_at = datetime.fromisoformat(data['createdAt'].replace('Z', '+00:00'))
                        except ValueError:
                            pass
                    if 'updatedAt' in data:
                        try:
                            updated_at = datetime.fromisoformat(data['updatedAt'].replace('Z', '+00:00'))
                        except ValueError:
                            pass

                    conn.execute(text("""
                        INSERT INTO email_templates (id, name, description, type, subject, html_content, triggers, active, created_at, updated_at)
                        VALUES (:id, :name, :description, :type, :subject, :html_content, :triggers, :active, :created_at, :updated_at)
                    """), {
                        'id': entity_id,
                        'name': data.get('name', ''),
                        'description': data.get('description', ''),
                        'type': data.get('type', 'feedback'),
                        'subject': data.get('subject', ''),
                        'html_content': data.get('htmlContent', ''),
                        'triggers': json.dumps(data.get('triggers', {})),
                        'active': data.get('active', True),
                        'created_at': created_at or datetime.now(),
                        'updated_at': updated_at or datetime.now()
                    })
                    return True

                elif entity_type == 'courses':
                    # For courses, we need to handle bulk creation
                    # The data should be the complete courses object with multiple course categories
                    # First, clear existing courses
                    conn.execute(text("DELETE FROM courses"))

                    # Then insert each course category as a separate record
                    for course_id, course_data in data.items():
                        conn.execute(text("""
                            INSERT INTO courses (id, course_data, created_at, updated_at)
                            VALUES (:id, :course_data, :created_at, :updated_at)
                        """), {
                            'id': course_id,
                            'course_data': json.dumps(course_data),
                            'created_at': datetime.now(),
                            'updated_at': datetime.now()
                        })
                    return True

                else:
                    raise NotImplementedError(f"Database create operations for {entity_type} not yet implemented")

        except Exception as e:
            logger.error(f"Error creating {entity_type} in database: {e}")
            raise
    
    def _update_in_database(self, entity_type: str, entity_id: str, data: Dict) -> bool:
        """Update record in database"""
        if not self.engine:
            raise Exception("Database not initialized")

        try:
            with self.engine.begin() as conn:
                if entity_type == 'members':
                    # Parse validity period
                    validity_start = None
                    validity_end = None
                    if 'validityPeriod' in data and isinstance(data['validityPeriod'], str) and ' - ' in data['validityPeriod']:
                        start_str, end_str = data['validityPeriod'].split(' - ')
                        try:
                            validity_start = datetime.strptime(start_str.strip(), '%d/%m/%Y').date()
                            validity_end = datetime.strptime(end_str.strip(), '%d/%m/%Y').date()
                        except ValueError:
                            pass

                    conn.execute(text("""
                        UPDATE members
                        SET name = :name, email = :email, is_active = :is_active,
                            validity_period_start = :validity_period_start, validity_period_end = :validity_period_end,
                            password = :password, gender = :gender, updated_at = NOW()
                        WHERE id = :id
                    """), {
                        'id': entity_id,
                        'name': data.get('name'),
                        'email': data.get('email'),
                        'is_active': data.get('isActive', True),
                        'validity_period_start': validity_start,
                        'validity_period_end': validity_end,
                        'password': data.get('password'),
                        'gender': data.get('gender')
                    })
                    return True

                elif entity_type == 'checkins':
                    # Parse checkin time
                    checkin_time = None
                    if 'checkinTime' in data:
                        try:
                            checkin_time = datetime.fromisoformat(data['checkinTime'].replace('Z', '+00:00'))
                        except ValueError:
                            pass

                    conn.execute(text("""
                        UPDATE checkins
                        SET activity_id = :activity_id, member_id = :member_id, member_name = :member_name,
                            member_type = :member_type, checkin_time = :checkin_time,
                            checkin_by = :checkin_by, checkin_by_email = :checkin_by_email, notes = :notes
                        WHERE id = :id
                    """), {
                        'id': entity_id,
                        'activity_id': data.get('activityId'),
                        'member_id': data.get('memberId'),
                        'member_name': data.get('memberName'),
                        'member_type': data.get('memberType'),
                        'checkin_time': checkin_time,
                        'checkin_by': data.get('checkinBy'),
                        'checkin_by_email': data.get('checkinByEmail'),
                        'notes': data.get('notes')
                    })
                    return True

                elif entity_type == 'feedbacks':
                    conn.execute(text("""
                        UPDATE feedbacks
                        SET activity_id = :activity_id, member_id = :member_id, member_name = :member_name,
                            overall_feedback = :overall_feedback, skill_assessment = :skill_assessment,
                            feedback = :feedback, created_by = :created_by, created_by_email = :created_by_email,
                            student_rating = :student_rating, updated_at = NOW()
                        WHERE checkin_id = :checkin_id
                    """), {
                        'checkin_id': entity_id,  # entity_id is checkin_id for feedbacks
                        'activity_id': data.get('activityId'),
                        'member_id': data.get('memberId'),
                        'member_name': data.get('memberName'),
                        'overall_feedback': data.get('overallFeedback'),
                        'skill_assessment': json.dumps(data.get('skillAssessment', {})),
                        'feedback': data.get('feedback'),
                        'created_by': data.get('createdBy'),
                        'created_by_email': data.get('createdByEmail'),
                        'student_rating': json.dumps(data.get('studentRating', {})) if data.get('studentRating') else None
                    })
                    return True

                elif entity_type == 'activities':
                    # Parse date and time
                    activity_date = None
                    if 'date' in data:
                        try:
                            activity_date = datetime.strptime(data['date'], '%Y-%m-%d').date()
                        except ValueError:
                            pass

                    updated_at = None
                    if 'updatedAt' in data:
                        try:
                            updated_at = datetime.fromisoformat(data['updatedAt'].replace('Z', '+00:00'))
                        except ValueError:
                            pass

                    conn.execute(text("""
                        UPDATE activities
                        SET name_en = :name_en, name_zh = :name_zh, name_nl = :name_nl,
                            type = :type, date = :date,
                            location_en = :location_en, location_zh = :location_zh, location_nl = :location_nl,
                            description_en = :description_en, description_zh = :description_zh, description_nl = :description_nl,
                            status = :status, feedback_template_id = :feedback_template_id, updated_at = :updated_at
                        WHERE id = :id
                    """), {
                        'id': entity_id,
                        'name_en': data.get('name', {}).get('en'),
                        'name_zh': data.get('name', {}).get('zh'),
                        'name_nl': data.get('name', {}).get('nl'),
                        'type': data.get('type'),
                        'date': activity_date,
                        'location_en': data.get('location', {}).get('en'),
                        'location_zh': data.get('location', {}).get('zh'),
                        'location_nl': data.get('location', {}).get('nl'),
                        'description_en': data.get('description', {}).get('en'),
                        'description_zh': data.get('description', {}).get('zh'),
                        'description_nl': data.get('description', {}).get('nl'),
                        'status': data.get('status', 'active'),
                        'feedback_template_id': data.get('feedbackTemplateId'),
                        'updated_at': updated_at or datetime.now()
                    })
                    return True

                elif entity_type == 'student_ratings':
                    # Parse submitted time
                    submitted_at = None
                    if 'submittedAt' in data:
                        try:
                            submitted_at = datetime.fromisoformat(data['submittedAt'].replace('Z', '+00:00'))
                        except ValueError:
                            pass

                    conn.execute(text("""
                        UPDATE student_ratings
                        SET feedback_id = :feedback_id, student_email = :student_email, student_name = :student_name,
                            member_id = :member_id, activity_id = :activity_id, instructor_name = :instructor_name,
                            rating = :rating, comment = :comment, submitted_at = :submitted_at
                        WHERE id = :id
                    """), {
                        'id': entity_id,
                        'feedback_id': data.get('feedbackId'),
                        'student_email': data.get('studentEmail'),
                        'student_name': data.get('studentName'),
                        'member_id': data.get('memberId'),
                        'activity_id': data.get('activityId'),
                        'instructor_name': data.get('instructorName'),
                        'rating': data.get('rating'),
                        'comment': data.get('comment'),
                        'submitted_at': submitted_at
                    })
                    return True

                elif entity_type == 'feedback_templates':
                    # Parse timestamps
                    updated_at = None
                    if 'updatedAt' in data:
                        try:
                            updated_at = datetime.fromisoformat(data['updatedAt'].replace('Z', '+00:00'))
                        except ValueError:
                            pass

                    conn.execute(text("""
                        UPDATE feedback_templates
                        SET name_en = :name_en, name_zh = :name_zh, name_nl = :name_nl,
                            description_en = :description_en, description_zh = :description_zh, description_nl = :description_nl,
                            target_level = :target_level, sport = :sport, sections = :sections, version = :version,
                            updated_at = :updated_at
                        WHERE id = :id
                    """), {
                        'id': entity_id,
                        'name_en': data.get('name', {}).get('en', ''),
                        'name_zh': data.get('name', {}).get('zh', ''),
                        'name_nl': data.get('name', {}).get('nl', ''),
                        'description_en': data.get('description', {}).get('en', ''),
                        'description_zh': data.get('description', {}).get('zh', ''),
                        'description_nl': data.get('description', {}).get('nl', ''),
                        'target_level': data.get('targetLevel', 'beginner'),
                        'sport': data.get('sport', 'snowboard'),
                        'sections': json.dumps(data.get('sections', {})),
                        'version': data.get('version', '1.0'),
                        'updated_at': updated_at or datetime.now()
                    })
                    return True

                elif entity_type == 'email_templates':
                    # Parse timestamps
                    updated_at = None
                    if 'updatedAt' in data:
                        try:
                            updated_at = datetime.fromisoformat(data['updatedAt'].replace('Z', '+00:00'))
                        except ValueError:
                            pass

                    conn.execute(text("""
                        UPDATE email_templates
                        SET name = :name, description = :description, type = :type, subject = :subject,
                            html_content = :html_content, triggers = :triggers, active = :active, updated_at = :updated_at
                        WHERE id = :id
                    """), {
                        'id': entity_id,
                        'name': data.get('name', ''),
                        'description': data.get('description', ''),
                        'type': data.get('type', 'feedback'),
                        'subject': data.get('subject', ''),
                        'html_content': data.get('htmlContent', ''),
                        'triggers': json.dumps(data.get('triggers', {})),
                        'active': data.get('active', True),
                        'updated_at': updated_at or datetime.now()
                    })
                    return True

                elif entity_type == 'courses':
                    # For courses, we need to handle bulk update
                    # The data should be the complete courses object with multiple course categories
                    # First, clear existing courses
                    conn.execute(text("DELETE FROM courses"))

                    # Then insert each course category as a separate record
                    for course_id, course_data in data.items():
                        conn.execute(text("""
                            INSERT INTO courses (id, course_data, created_at, updated_at)
                            VALUES (:id, :course_data, :created_at, :updated_at)
                        """), {
                            'id': course_id,
                            'course_data': json.dumps(course_data),
                            'created_at': datetime.now(),
                            'updated_at': datetime.now()
                        })
                    return True

                else:
                    raise NotImplementedError(f"Database update operations for {entity_type} not yet implemented")

        except Exception as e:
            logger.error(f"Error updating {entity_type} in database: {e}")
            raise
    
    def _delete_from_database(self, entity_type: str, entity_id: str) -> bool:
        """Delete record from database"""
        if not self.engine:
            raise Exception("Database not initialized")

        try:
            with self.engine.begin() as conn:
                if entity_type == 'members':
                    result = conn.execute(text("DELETE FROM members WHERE id = :id"), {'id': entity_id})
                    return result.rowcount > 0

                elif entity_type == 'checkins':
                    result = conn.execute(text("DELETE FROM checkins WHERE id = :id"), {'id': entity_id})
                    return result.rowcount > 0

                elif entity_type == 'activities':
                    # Delete activity (no separate translations table)
                    result = conn.execute(text("DELETE FROM activities WHERE id = :id"), {'id': entity_id})
                    return result.rowcount > 0

                elif entity_type == 'feedbacks':
                    result = conn.execute(text("DELETE FROM feedbacks WHERE checkin_id = :checkin_id"), {'checkin_id': entity_id})
                    return result.rowcount > 0

                elif entity_type == 'feedback_templates':
                    result = conn.execute(text("DELETE FROM feedback_templates WHERE id = :id"), {'id': entity_id})
                    return result.rowcount > 0

                elif entity_type == 'courses':
                    # For courses, delete all course records
                    result = conn.execute(text("DELETE FROM courses"))
                    return result.rowcount > 0

                elif entity_type == 'navigation':
                    result = conn.execute(text("DELETE FROM navigation_items WHERE id = :id"), {'id': entity_id})
                    return result.rowcount > 0

                elif entity_type == 'email_templates':
                    result = conn.execute(text("DELETE FROM email_templates WHERE id = :id"), {'id': entity_id})
                    return result.rowcount > 0

                elif entity_type == 'student_ratings':
                    result = conn.execute(text("DELETE FROM student_ratings WHERE id = :id"), {'id': entity_id})
                    return result.rowcount > 0

                elif entity_type == 'feedback_templates':
                    result = conn.execute(text("DELETE FROM feedback_templates WHERE id = :id"), {'id': entity_id})
                    return result.rowcount > 0

                else:
                    raise NotImplementedError(f"Database delete operations for {entity_type} not yet implemented")

        except Exception as e:
            logger.error(f"Error deleting {entity_type} from database: {e}")
            raise


# Global data access instance
data_access = DataAccessLayer(mode=os.getenv('DATA_MODE', 'json'))
