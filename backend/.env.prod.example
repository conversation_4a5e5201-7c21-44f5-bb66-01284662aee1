# SnowNavi 生产环境配置文件
# 复制此文件为 backend/.env.prod 并填入实际的生产环境配置

# =============================================================================
# 数据库配置 (PostgreSQL)
# =============================================================================
# 生产环境强密码 (至少16位，包含大小写字母、数字、特殊字符)
POSTGRES_PASSWORD=YourVeryStrongPassword123!@#
DATABASE_URL=postgresql://snownavi_user:YourVeryStrongPassword123!@#@localhost:5432/snownavi

# 数据存储模式
# 'database' = 仅使用数据库 (生产环境推荐)
# 'hybrid' = 数据库+JSON备份 (开发环境)
# 'json' = 仅JSON文件 (本地开发)
DATA_MODE=database

# =============================================================================
# 应用配置
# =============================================================================
ENVIRONMENT=production
DEBUG=false
FLASK_ENV=production

# Flask应用端口 (内部使用，通过nginx代理)
FLASK_PORT=8899

# =============================================================================
# 安全配置
# =============================================================================
# 允许的主机域名
ALLOWED_HOSTS=snownavi.ski,www.snownavi.ski

# CORS配置
CORS_ALLOWED_ORIGINS=https://snownavi.ski,https://www.snownavi.ski

# 会话密钥 (生成强随机密钥)
SECRET_KEY=your-very-long-random-secret-key-here-change-this-in-production

# =============================================================================
# SSL/TLS配置
# =============================================================================
SSL_CERT_PATH=/etc/nginx/snownavi.ski_bundle.pem
SSL_KEY_PATH=/etc/nginx/snownavi.ski.key

# =============================================================================
# 文件上传配置
# =============================================================================
UPLOAD_FOLDER=/home/<USER>/git/snownavi_website/uploads
MAX_CONTENT_LENGTH=16777216  # 16MB in bytes

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL=INFO
LOG_FILE=/var/log/snownavi/app.log
LOG_MAX_BYTES=10485760  # 10MB
LOG_BACKUP_COUNT=5

# =============================================================================
# 数据库备份配置
# =============================================================================
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点

# 可选：云存储备份
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# BACKUP_S3_BUCKET=snownavi-backups

# =============================================================================
# 监控和健康检查
# =============================================================================
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true

# =============================================================================
# 邮件配置 (如果使用)
# =============================================================================
# SMTP_SERVER=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_app_password
# SMTP_USE_TLS=true

# =============================================================================
# 第三方服务配置
# =============================================================================
# Google OAuth (如果使用)
# GOOGLE_CLIENT_ID=your_google_client_id
# GOOGLE_CLIENT_SECRET=your_google_client_secret

# =============================================================================
# 性能配置
# =============================================================================
# 数据库连接池
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# 缓存配置
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=300
