# SnowNavi Backend Configuration

# Flask Configuration
FLASK_SECRET_KEY=change-this-to-a-random-string

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
ALLOWED_EMAILS=<EMAIL>

# Database Configuration
DATABASE_URL=postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi

# Data Storage Mode
# Options: 'json', 'database', 'hybrid'
# - json: Use only JSON files for data storage
# - database: Use only PostgreSQL database for data storage  
# - hybrid: Use database with JSON fallback (recommended for transition)
DATA_MODE=json

# Email Configuration (Brevo)
BREVO_API_KEY=your-brevo-api-key-here

# Server Configuration
HOST=0.0.0.0
PORT=8899
