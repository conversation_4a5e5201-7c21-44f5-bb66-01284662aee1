# SnowNavi Email Service Environment Configuration
# Copy this file to .env and update with your actual values

# Google OAuth2 Configuration
GOOGLE_CLIENT_ID=your-google-client-id-here
ALLOWED_EMAILS=<EMAIL>,<EMAIL>

# Server Configuration
HOST=0.0.0.0
PORT=8899

# Email API Server Configuration
EMAIL_API_PORT=3001

# Brevo Email Service Configuration
BREVO_API_KEY=your-brevo-api-key-here
BREVO_API_ENDPOINT=https://api.brevo.com/v3/smtp/email
BREVO_SENDER_EMAIL=<EMAIL>
BREVO_SENDER_NAME=your-sender-name-here

# SMTP Configuration (backup)
BREVO_SMTP_SERVER=smtp-relay.brevo.com
BREVO_SMTP_PORT=587
BREVO_SMTP_LOGIN=your-smtp-login
BREVO_SMTP_PASSWORD=your-smtp-password

# Application Environment
NODE_ENV=production

# CORS Configuration
CORS_ORIGIN=http://127.0.0.1:8899
