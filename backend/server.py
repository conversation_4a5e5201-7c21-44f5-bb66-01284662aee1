from flask import Flask, send_from_directory, request, jsonify
from flask_cors import CORS
import os
import json
import mimetypes
import uuid
import time
import datetime
import hashlib
import re
import requests
from werkzeug.utils import secure_filename
from dotenv import load_dotenv
from database_access import data_access

# Load environment variables from .env file
env_path = os.path.join(os.path.dirname(__file__), '.env')
load_dotenv(env_path)

# Root directory of the project
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
DATA_DIR = os.path.join(ROOT_DIR, 'data')
ASSETS_DIR = os.path.join(ROOT_DIR, 'assets')
UPLOADS_DIR = os.path.join(ROOT_DIR, 'uploads')
JSON_FILE = os.path.join(DATA_DIR, 'courses.json')
NAVIGATION_FILE = os.path.join(DATA_DIR, 'navigation.json')

# Password generation and hashing functions
def generate_default_password(member_id):
    """Generate a default password based on member ID using a fixed algorithm"""
    # Use a combination of member_id and a fixed salt for generating the password
    salt = "SnowNavi2025Salt"  # Fixed salt for all passwords
    raw_password = f"{member_id}{salt}"
    # Create a SHA-256 hash of the raw password
    hashed = hashlib.sha256(raw_password.encode()).hexdigest()
    # Return the first 8 characters as the password
    return hashed[:8]

def hash_password(password):
    """Hash a password for storing"""
    # Use SHA-256 for password hashing
    return hashlib.sha256(password.encode()).hexdigest()

# Create uploads directory if it doesn't exist
if not os.path.exists(UPLOADS_DIR):
    os.makedirs(UPLOADS_DIR)

# Create subdirectories for images and PDFs
images_dir = os.path.join(UPLOADS_DIR, 'images')
pdfs_dir = os.path.join(UPLOADS_DIR, 'pdfs')

if not os.path.exists(images_dir):
    os.makedirs(images_dir)

if not os.path.exists(pdfs_dir):
    os.makedirs(pdfs_dir)

# Allowed file extensions
ALLOWED_EXTENSIONS = {
    'images': {'png', 'jpg', 'jpeg', 'gif', 'webp'},
    'pdfs': {'pdf'}
}

# Initialize Flask app
app = Flask(__name__, static_folder=None)  # Disable default static folder
CORS(app)  # Enable CORS

# Set maximum content length for file uploads (16MB)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024

# Ensure proper MIME types are registered
mimetypes.add_type('text/javascript', '.js')
mimetypes.add_type('text/css', '.css')
mimetypes.add_type('image/svg+xml', '.svg')

# Main routes
@app.route('/')
def index():
    return send_from_directory(ROOT_DIR, 'index.html')

@app.route('/index.html')
def serve_index():
    return send_from_directory(ROOT_DIR, 'index.html')

@app.route('/course.html')
def serve_course():
    return send_from_directory(ROOT_DIR, 'course.html')

@app.route('/admin.html')
def serve_admin_page():
    return send_from_directory(ROOT_DIR, 'admin.html')

@app.route('/course_admin.html')
def serve_course_admin_page():
    return send_from_directory(ROOT_DIR, 'course_admin.html')

@app.route('/member_admin.html')
def serve_member_admin_page():
    return send_from_directory(ROOT_DIR, 'member_admin.html')

@app.route('/navigation_admin.html')
def serve_navigation_admin_page():
    return send_from_directory(ROOT_DIR, 'navigation_admin.html')

@app.route('/activity_admin.html')
def serve_activity_admin_page():
    return send_from_directory(ROOT_DIR, 'activity_admin.html')

@app.route('/checkin_admin.html')
def serve_checkin_admin_page():
    return send_from_directory(ROOT_DIR, 'checkin_admin.html')

@app.route('/feedback_template_admin.html')
def serve_feedback_template_admin_page():
    return send_from_directory(ROOT_DIR, 'feedback_template_admin.html')

@app.route('/email_template_admin.html')
def serve_email_template_admin_page():
    return send_from_directory(ROOT_DIR, 'email_template_admin.html')

@app.route('/email_preview.html')
def serve_email_preview_page():
    return send_from_directory(ROOT_DIR, 'email_preview.html')

@app.route('/test_email_preview.html')
def serve_test_email_preview_page():
    return send_from_directory(ROOT_DIR, 'test_email_preview.html')

@app.route('/student_feedback.html')
def serve_student_feedback_page():
    return send_from_directory(ROOT_DIR, 'student_feedback.html')

@app.route('/login.html')
def serve_login_page():
    return send_from_directory(ROOT_DIR, 'login.html')

@app.route('/auth_callback.html')
def serve_auth_callback_page():
    return send_from_directory(ROOT_DIR, 'auth_callback.html')

@app.route('/member.html')
def serve_member_page():
    return send_from_directory(ROOT_DIR, 'member.html')

# API routes
@app.route('/api/config')
def get_config():
    # Get environment variables
    google_client_id = os.environ.get('GOOGLE_CLIENT_ID', '')
    allowed_emails_str = os.environ.get('ALLOWED_EMAILS', '')

    # Parse allowed emails into a list
    allowed_emails = [email.strip() for email in allowed_emails_str.split(',') if email.strip()]

    # Log the values being returned (remove in production)
    app.logger.info(f"Returning config: googleClientId={google_client_id}, allowedEmails={allowed_emails}")

    # Return the necessary configuration from environment variables
    return jsonify({
        'googleClientId': google_client_id,
        'allowedEmails': allowed_emails,
        # Keep backward compatibility for now
        'authorizedEmail': allowed_emails_str
    })

# Data routes
@app.route('/data/courses.json', methods=['GET'])
def get_courses():
    """Get all courses using data access layer"""
    try:
        courses_data = data_access.get_all('courses')
        return jsonify(courses_data)
    except Exception as e:
        app.logger.error(f"Error getting courses: {str(e)}")
        # Fallback to file-based access for compatibility
        return send_from_directory(DATA_DIR, 'courses.json')

@app.route('/data/courses.json', methods=['POST'])
def update_courses():
    """Update courses using data access layer"""
    try:
        data = request.get_json()

        # Use data access layer for bulk update
        # The data contains the complete courses structure
        # We use a dummy ID since the create/update methods handle the bulk operation internally
        success = data_access.update('courses', 'bulk', data)

        if success:
            return jsonify({'status': 'success'}), 200
        else:
            return jsonify({'error': 'Failed to save courses'}), 500

    except Exception as e:
        app.logger.error(f"Error updating courses: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/data/members.json', methods=['GET'])
def get_members():
    """Get all members using data access layer"""
    try:
        members_data = data_access.get_all('members')
        return jsonify(members_data)
    except Exception as e:
        app.logger.error(f"Error getting members: {str(e)}")
        # Fallback to file-based access for compatibility
        return send_from_directory(DATA_DIR, 'members.json')

@app.route('/data/members.json', methods=['POST'])
def update_members():
    """Update members using data access layer"""
    try:
        data = request.get_json()
        # For now, still use file-based approach for bulk updates
        # TODO: Implement bulk update in data access layer
        members_file = os.path.join(DATA_DIR, 'members.json')
        with open(members_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return jsonify({'status': 'success'}), 200
    except Exception as e:
        app.logger.error(f"Error updating members: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

# Activities routes
@app.route('/data/activities.json', methods=['GET'])
def get_activities():
    """Get all activities using data access layer"""
    try:
        activities_data = data_access.get_all('activities')
        return jsonify(activities_data)
    except Exception as e:
        app.logger.error(f"Error getting activities: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/data/activities.json', methods=['POST'])
def update_activities():
    """Update activities using data access layer"""
    try:
        data = request.get_json()

        # Clear existing activities and add new ones
        # This is a bulk replace operation
        success_count = 0
        error_count = 0

        # Get existing activities to determine which to delete
        existing_activities = data_access.get_all('activities')

        # Delete activities not in the new data
        for activity_id in existing_activities:
            if activity_id not in data:
                if data_access.delete('activities', activity_id):
                    success_count += 1
                else:
                    error_count += 1

        # Update or create activities from the new data
        for activity_id, activity_data in data.items():
            if data_access.update('activities', activity_id, activity_data):
                success_count += 1
            else:
                # Try to create if update failed
                if data_access.create('activities', activity_id, activity_data):
                    success_count += 1
                else:
                    error_count += 1

        if error_count == 0:
            return jsonify({'status': 'success', 'updated': success_count}), 200
        else:
            return jsonify({'status': 'partial_success', 'updated': success_count, 'errors': error_count}), 207

    except Exception as e:
        app.logger.error(f"Error updating activities: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

# Checkins routes
@app.route('/data/checkins.json', methods=['GET'])
def get_checkins():
    """Get all checkins using data access layer"""
    try:
        checkins_data = data_access.get_all('checkins')
        return jsonify(checkins_data)
    except Exception as e:
        app.logger.error(f"Error getting checkins: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/data/checkins.json', methods=['POST'])
def update_checkins():
    """Update checkins using data access layer"""
    try:
        data = request.get_json()

        # Clear existing checkins and add new ones
        # This is a bulk replace operation
        success_count = 0
        error_count = 0

        # Get existing checkins to determine which to delete
        existing_checkins = data_access.get_all('checkins')

        # Delete checkins not in the new data
        for checkin_id in existing_checkins:
            if checkin_id not in data:
                if data_access.delete('checkins', checkin_id):
                    success_count += 1
                else:
                    error_count += 1

        # Update or create checkins from the new data
        for checkin_id, checkin_data in data.items():
            if data_access.update('checkins', checkin_id, checkin_data):
                success_count += 1
            else:
                # Try to create if update failed
                if data_access.create('checkins', checkin_id, checkin_data):
                    success_count += 1
                else:
                    error_count += 1

        if error_count == 0:
            return jsonify({'status': 'success', 'updated': success_count}), 200
        else:
            return jsonify({'status': 'partial_success', 'updated': success_count, 'errors': error_count}), 207

    except Exception as e:
        app.logger.error(f"Error updating checkins: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

# Feedbacks routes
@app.route('/data/feedbacks.json', methods=['GET'])
def get_feedbacks():
    """Get all feedbacks using data access layer"""
    try:
        feedbacks_data = data_access.get_all('feedbacks')
        return jsonify(feedbacks_data)
    except Exception as e:
        app.logger.error(f"Error getting feedbacks: {str(e)}")
        # Fallback to file-based access for compatibility
        feedbacks_file = os.path.join(DATA_DIR, 'feedbacks.json')
        if not os.path.exists(feedbacks_file):
            # Create empty feedbacks file if it doesn't exist
            with open(feedbacks_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
        return send_from_directory(DATA_DIR, 'feedbacks.json')

@app.route('/data/feedbacks.json', methods=['POST'])
def update_feedbacks():
    """Update feedbacks using data access layer"""
    try:
        data = request.get_json()
        # For now, still use file-based approach for bulk updates
        # TODO: Implement bulk update in data access layer
        feedbacks_file = os.path.join(DATA_DIR, 'feedbacks.json')
        with open(feedbacks_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return jsonify({'status': 'success'}), 200
    except Exception as e:
        app.logger.error(f"Error updating feedbacks: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

# Feedback Templates routes - now using RESTful API only
# Old file-based routes removed in favor of /api/feedback-template* endpoints

@app.route('/data/navigation.json', methods=['GET'])
def get_navigation():
    """Get navigation using data access layer"""
    try:
        navigation_data = data_access.get_all('navigation')
        return jsonify(navigation_data)
    except Exception as e:
        app.logger.error(f"Error getting navigation: {str(e)}")
        # Fallback to file-based access for compatibility
        # Check if navigation.json exists, if not create a default one
        if not os.path.exists(NAVIGATION_FILE):
            default_navigation = {
            "items": [
                {
                    "id": "courses",
                    "url": "index.html#courses",
                    "translations": {
                        "en": "Courses",
                        "zh": "课程",
                        "nl": "Cursussen"
                    },
                    "visible": True,
                    "order": 1
                },
                {
                    "id": "map",
                    "url": "index.html#map",
                    "translations": {
                        "en": "Interactive Ski Map",
                        "zh": "在线滑雪地图",
                        "nl": "Interactieve Skikaart"
                    },
                    "visible": True,
                    "order": 2
                },
                {
                    "id": "story",
                    "url": "index.html#story",
                    "translations": {
                        "en": "Our Story",
                        "zh": "我们的故事",
                        "nl": "Ons Verhaal"
                    },
                    "visible": True,
                    "order": 3
                },
                {
                    "id": "contact",
                    "url": "index.html#contact",
                    "translations": {
                        "en": "Contact",
                        "zh": "联系我们",
                        "nl": "Contact"
                    },
                    "visible": True,
                    "order": 4
                }
            ]
        }
        with open(NAVIGATION_FILE, 'w', encoding='utf-8') as f:
            json.dump(default_navigation, f, ensure_ascii=False, indent=2)

    return send_from_directory(DATA_DIR, 'navigation.json')

@app.route('/data/navigation.json', methods=['POST'])
def update_navigation():
    """Update navigation using data access layer"""
    try:
        data = request.get_json()
        # For now, still use file-based approach for bulk updates
        # TODO: Implement bulk update in data access layer
        with open(NAVIGATION_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return jsonify({'status': 'success'}), 200
    except Exception as e:
        app.logger.error(f"Error updating navigation: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

# Student Ratings routes
@app.route('/data/student_ratings.json', methods=['GET'])
def get_student_ratings():
    """Get all student ratings using data access layer"""
    try:
        ratings_data = data_access.get_all('student_ratings')
        return jsonify(ratings_data)
    except Exception as e:
        app.logger.error(f"Error getting student ratings: {str(e)}")
        # Fallback to file-based access for compatibility
        ratings_file = os.path.join(DATA_DIR, 'student_ratings.json')
        if not os.path.exists(ratings_file):
            # Create empty ratings file if it doesn't exist
            with open(ratings_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
        return send_from_directory(DATA_DIR, 'student_ratings.json')

# Email Templates routes
@app.route('/data/email_templates.json', methods=['GET'])
def get_email_templates_data():
    """Get all email templates using data access layer"""
    try:
        templates_data = data_access.get_all('email_templates')
        return jsonify(templates_data)
    except Exception as e:
        app.logger.error(f"Error getting email templates: {str(e)}")
        # Fallback to file-based access for compatibility
        templates_file = os.path.join(DATA_DIR, 'email_templates.json')
        if not os.path.exists(templates_file):
            # Create empty templates file if it doesn't exist
            with open(templates_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
        return send_from_directory(DATA_DIR, 'email_templates.json')

@app.route('/data/student_ratings.json', methods=['POST'])
def update_student_ratings():
    """Update student ratings using data access layer"""
    try:
        data = request.get_json()
        # For now, still use file-based approach for bulk updates
        # TODO: Implement bulk update in data access layer
        ratings_file = os.path.join(DATA_DIR, 'student_ratings.json')
        with open(ratings_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return jsonify({'status': 'success'}), 200
    except Exception as e:
        app.logger.error(f"Error updating student ratings: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/member/<member_id>', methods=['GET'])
def get_member(member_id):
    """Get a single member using data access layer"""
    try:
        member = data_access.get_by_id('members', member_id)
        if member:
            return jsonify(member)
        else:
            return jsonify({'error': 'Member not found'}), 404
    except Exception as e:
        app.logger.error(f"Error retrieving member data: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/member/<member_id>', methods=['PUT'])
def update_member(member_id):
    """Update a member using data access layer"""
    try:
        # Validate member_id format to prevent injection attacks
        if not re.match(r'^SN\d{4}\d{4}$', member_id):
            return jsonify({'error': 'Invalid member ID format'}), 400

        # Check if the member exists
        existing_member = data_access.get_by_id('members', member_id)
        if not existing_member:
            return jsonify({'error': 'Member not found'}), 404

        # Get the updated member data from the request
        updated_member = request.get_json()

        # Validate the updated member data
        if not updated_member or not isinstance(updated_member, dict):
            return jsonify({'error': 'Invalid member data'}), 400

        # Ensure the member ID in the data matches the URL parameter
        if 'id' not in updated_member or updated_member['id'] != member_id:
            return jsonify({'error': 'Member ID mismatch'}), 400

        # Validate required fields
        required_fields = ['name', 'isActive']
        for field in required_fields:
            if field not in updated_member:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        # Validate validityPeriod if present (can be string or empty)
        if 'validityPeriod' in updated_member:
            validity_period = updated_member['validityPeriod']
            if validity_period is not None and not isinstance(validity_period, str):
                return jsonify({'error': 'validityPeriod must be a string or null'}), 400

        # Update the member using data access layer
        success = data_access.update('members', member_id, updated_member)

        if success:
            # Get the updated member data to return
            updated_member_data = data_access.get_by_id('members', member_id)
            return jsonify({
                'status': 'success',
                'member': updated_member_data  # Return the complete updated member object
            }), 200
        else:
            return jsonify({'error': 'Failed to update member'}), 500

    except Exception as e:
        app.logger.error(f"Error updating member data: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/member/<member_id>', methods=['DELETE'])
def delete_member(member_id):
    """Delete a member using data access layer"""
    try:
        # Check if the member exists
        existing_member = data_access.get_by_id('members', member_id)
        if not existing_member:
            return jsonify({'error': 'Member not found'}), 404

        # Delete the member using data access layer
        success = data_access.delete('members', member_id)

        if success:
            return jsonify({'status': 'success'}), 200
        else:
            return jsonify({'error': 'Failed to delete member'}), 500

    except Exception as e:
        app.logger.error(f"Error deleting member data: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/member', methods=['POST'])
def create_member():
    """Create a new member using data access layer"""
    try:
        # Get the new member data from the request
        new_member = request.get_json()

        # Ensure the member has an ID
        if 'id' not in new_member:
            return jsonify({'error': 'Member ID is required'}), 400

        member_id = new_member['id']

        # Check if a member with this ID already exists
        existing_member = data_access.get_by_id('members', member_id)
        if existing_member:
            return jsonify({'error': 'A member with this ID already exists'}), 409

        # Generate default password for the new member
        default_password = generate_default_password(member_id)
        # Store the hashed password
        new_member['password'] = default_password
        # Store the plain text password temporarily for the response
        plain_password = default_password

        # Create the member using data access layer
        success = data_access.create('members', member_id, new_member)

        if success:
            return jsonify({
                'status': 'success',
                'id': member_id,
                'password': plain_password,  # Return the plain text password in the response
                'member': new_member  # Return the complete member object
            }), 201
        else:
            return jsonify({'error': 'Failed to create member'}), 500

    except Exception as e:
        app.logger.error(f"Error creating member data: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/member/search-by-email', methods=['POST'])
def search_member_by_email():
    """Search for a member by email address using data access layer"""
    try:
        # Get the email from the request body
        data = request.get_json()
        if not data or 'email' not in data:
            return jsonify({'error': 'Email is required'}), 400

        email = data['email'].strip().lower()

        # Basic email validation
        if not email or '@' not in email:
            return jsonify({'error': 'Invalid email format'}), 400

        # Get all members using data access layer
        members = data_access.get_all('members')

        # Search for member with matching email
        for member_id, member_data in members.items():
            if 'email' in member_data and member_data['email'].strip().lower() == email:
                return jsonify({
                    'found': True,
                    'memberId': member_id,
                    'memberName': member_data.get('name', 'Unknown')
                })

        # No member found with this email
        return jsonify({'found': False}), 404

    except Exception as e:
        app.logger.error(f"Error searching member by email: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/member/<member_id>/check-expiration', methods=['POST'])
def check_member_expiration(member_id):
    """Check if a member's validity period has expired and update status if needed"""
    try:
        # Validate member_id format to prevent injection attacks
        if not re.match(r'^SN\d{4}\d{4}$', member_id):
            return jsonify({'error': 'Invalid member ID format'}), 400

        members_file = os.path.join(DATA_DIR, 'members.json')
        if not os.path.exists(members_file):
            return jsonify({'error': 'Members data not found'}), 404

        with open(members_file, 'r', encoding='utf-8') as f:
            members = json.load(f)

        # Check if the member exists
        if member_id not in members:
            return jsonify({'error': 'Member not found'}), 404

        member = members[member_id]

        # Only proceed if the member is currently active
        if not member.get('isActive', False):
            return jsonify({
                'status': 'success',
                'message': 'Member is already marked as expired',
                'updated': False
            }), 200

        # Check if validity period exists
        if not member.get('validityPeriod'):
            return jsonify({'error': 'Missing validity period data'}), 400

        # Get the validity period (could be string or object with language keys)
        validity_period = None
        if isinstance(member['validityPeriod'], str):
            # New format: single string
            validity_period = member['validityPeriod']
        elif isinstance(member['validityPeriod'], dict):
            # Old format: object with language keys
            for lang in ['en', 'zh', 'nl']:
                if lang in member['validityPeriod'] and member['validityPeriod'][lang]:
                    validity_period = member['validityPeriod'][lang]
                    break
        else:
            return jsonify({'error': 'Invalid validity period format'}), 400

        if not validity_period:
            return jsonify({
                'status': 'success',
                'message': 'No validity period found to check',
                'updated': False
            }), 200

        # Check if the validity period is in the dd/mm/yyyy - dd/mm/yyyy format
        is_expired = False
        match = re.match(r'^(\d{2}/\d{2}/\d{4}) - (\d{2}/\d{2}/\d{4})$', validity_period)
        if match:
            end_date_str = match.group(2)
            try:
                # Parse the end date
                day, month, year = map(int, end_date_str.split('/'))
                end_date = datetime.datetime(year, month, day, 23, 59, 59)

                # Compare with current date
                now = datetime.datetime.now()
                is_expired = now > end_date
            except ValueError:
                app.logger.error(f"Error parsing date: {end_date_str}")
                return jsonify({'error': 'Invalid date format'}), 400
        else:
            # If not in standard format, try to extract year from the end date
            parts = validity_period.split(' - ')
            if len(parts) == 2:
                year_match = re.search(r'\d{4}', parts[1])
                if year_match:
                    year = int(year_match.group(0))
                    is_expired = datetime.datetime.now().year > year

        # If expired, update the member status
        if is_expired:
            member['isActive'] = False

            # Write the updated members dictionary back to the file
            with open(members_file, 'w', encoding='utf-8') as f:
                json.dump(members, f, ensure_ascii=False, indent=2)

            return jsonify({
                'status': 'success',
                'message': 'Member status updated to expired',
                'updated': True
            }), 200
        else:
            return jsonify({
                'status': 'success',
                'message': 'Member validity period has not expired',
                'updated': False
            }), 200

    except Exception as e:
        app.logger.error(f"Error checking member expiration: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

# Activity API endpoints
@app.route('/api/activity/<activity_id>', methods=['GET'])
def get_activity(activity_id):
    """Get a specific activity by ID using data access layer"""
    try:
        # Validate activity_id format to prevent injection attacks
        if not re.match(r'^ACT\d{4}\d{4}$', activity_id):
            return jsonify({'error': 'Invalid activity ID format'}), 400

        # Get activity using data access layer
        activity = data_access.get_by_id('activities', activity_id)

        if activity:
            return jsonify(activity)
        else:
            return jsonify({'error': 'Activity not found'}), 404

    except Exception as e:
        app.logger.error(f"Error retrieving activity data: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/activity', methods=['POST'])
def create_activity():
    """Create a new activity using data access layer"""
    try:
        new_activity = request.get_json()

        # Ensure the activity has an ID
        if 'id' not in new_activity:
            return jsonify({'error': 'Activity ID is required'}), 400

        activity_id = new_activity['id']

        # Validate activity_id format
        if not re.match(r'^ACT\d{4}\d{4}$', activity_id):
            return jsonify({'error': 'Invalid activity ID format'}), 400

        # Check if an activity with this ID already exists
        existing_activity = data_access.get_by_id('activities', activity_id)
        if existing_activity:
            return jsonify({'error': 'An activity with this ID already exists'}), 409

        # Add timestamps
        new_activity['createdAt'] = datetime.datetime.now().isoformat() + 'Z'
        new_activity['updatedAt'] = datetime.datetime.now().isoformat() + 'Z'

        # Save using data access layer
        success = data_access.create('activities', activity_id, new_activity)

        if success:
            return jsonify({'status': 'success', 'activity': new_activity}), 201
        else:
            return jsonify({'error': 'Failed to create activity'}), 500

    except Exception as e:
        app.logger.error(f"Error creating activity: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/activity/<activity_id>', methods=['PUT'])
def update_activity(activity_id):
    """Update an existing activity using data access layer"""
    try:
        # Validate activity_id format
        if not re.match(r'^ACT\d{4}\d{4}$', activity_id):
            return jsonify({'error': 'Invalid activity ID format'}), 400

        # Check if activity exists
        existing_activity = data_access.get_by_id('activities', activity_id)
        if not existing_activity:
            return jsonify({'error': 'Activity not found'}), 404

        updated_activity = request.get_json()

        # Ensure the activity ID matches
        updated_activity['id'] = activity_id

        # Update timestamp
        updated_activity['updatedAt'] = datetime.datetime.now().isoformat() + 'Z'

        # Preserve creation timestamp if it exists
        if 'createdAt' in existing_activity:
            updated_activity['createdAt'] = existing_activity['createdAt']

        # Update using data access layer
        success = data_access.update('activities', activity_id, updated_activity)

        if success:
            return jsonify({'status': 'success', 'activity': updated_activity}), 200
        else:
            return jsonify({'error': 'Failed to update activity'}), 500

    except Exception as e:
        app.logger.error(f"Error updating activity: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/activity/<activity_id>', methods=['DELETE'])
def delete_activity(activity_id):
    """Delete an activity using data access layer"""
    try:
        # Validate activity_id format
        if not re.match(r'^ACT\d{4}\d{4}$', activity_id):
            return jsonify({'error': 'Invalid activity ID format'}), 400

        # Check if activity exists
        existing_activity = data_access.get_by_id('activities', activity_id)
        if not existing_activity:
            return jsonify({'error': 'Activity not found'}), 404

        # Delete using data access layer
        success = data_access.delete('activities', activity_id)

        if success:
            return jsonify({'status': 'success', 'message': f'Activity {activity_id} deleted successfully'}), 200
        else:
            return jsonify({'error': 'Failed to delete activity'}), 500

    except Exception as e:
        app.logger.error(f"Error deleting activity: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

# Checkin API endpoints
@app.route('/api/checkin', methods=['POST'])
def create_checkin():
    """Create a new checkin record using data access layer"""
    try:
        new_checkin = request.get_json()

        # Validate required fields
        required_fields = ['activityId', 'memberId', 'memberName', 'memberType']
        for field in required_fields:
            if field not in new_checkin:
                return jsonify({'error': f'{field} is required'}), 400

        # Validate member ID format
        if not re.match(r'^SN\d{4}\d{4}$', new_checkin['memberId']):
            return jsonify({'error': 'Invalid member ID format'}), 400

        # Validate activity ID format
        if not re.match(r'^ACT\d{4}\d{4}$', new_checkin['activityId']):
            return jsonify({'error': 'Invalid activity ID format'}), 400

        # Check if member exists using data access layer
        try:
            members = data_access.get_all('members')
            if new_checkin['memberId'] not in members:
                return jsonify({'error': 'Member not found'}), 404
        except Exception as e:
            app.logger.warning(f"Could not verify member existence: {e}")
            # Continue anyway - member validation is not critical

        # Check if activity exists using data access layer
        try:
            activities = data_access.get_all('activities')
            if new_checkin['activityId'] not in activities:
                return jsonify({'error': 'Activity not found'}), 404
        except Exception as e:
            app.logger.warning(f"Could not verify activity existence: {e}")
            # Continue anyway - activity validation is not critical

        # Get existing checkins to generate ID and check duplicates
        try:
            checkins = data_access.get_all('checkins')
        except Exception as e:
            app.logger.warning(f"Could not get existing checkins: {e}")
            checkins = {}

        # Generate checkin ID if not provided
        if 'id' not in new_checkin:
            current_year = datetime.datetime.now().year
            prefix = f"CHK{current_year}"

            max_sequence = 0
            for checkin_id in checkins.keys():
                if checkin_id.startswith(prefix):
                    try:
                        sequence = int(checkin_id[len(prefix):])
                        max_sequence = max(max_sequence, sequence)
                    except ValueError:
                        continue

            new_sequence = max_sequence + 1
            new_checkin['id'] = f"{prefix}{new_sequence:04d}"

        checkin_id = new_checkin['id']

        # Check for duplicate checkin
        for existing_checkin in checkins.values():
            if (existing_checkin.get('activityId') == new_checkin['activityId'] and
                existing_checkin.get('memberId') == new_checkin['memberId']):
                return jsonify({'error': 'Member already checked in for this activity'}), 409

        # Add timestamps
        new_checkin['checkinTime'] = datetime.datetime.now().isoformat() + 'Z'
        new_checkin['createdAt'] = datetime.datetime.now().isoformat() + 'Z'

        # Save using data access layer
        success = data_access.create('checkins', checkin_id, new_checkin)

        if success:
            return jsonify({'status': 'success', 'checkin': new_checkin}), 201
        else:
            return jsonify({'error': 'Failed to save checkin'}), 500

    except Exception as e:
        app.logger.error(f"Error creating checkin: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/checkin/<checkin_id>', methods=['GET'])
def get_checkin(checkin_id):
    """Get a specific checkin by ID using data access layer"""
    try:
        # Validate checkin_id format
        if not re.match(r'^CHK\d{4}\d{4}$', checkin_id):
            return jsonify({'error': 'Invalid checkin ID format'}), 400

        # Get checkin using data access layer
        checkin = data_access.get_by_id('checkins', checkin_id)

        if checkin:
            return jsonify(checkin)
        else:
            return jsonify({'error': 'Checkin not found'}), 404

    except Exception as e:
        app.logger.error(f"Error retrieving checkin data: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/checkin/<checkin_id>', methods=['PUT'])
def update_checkin(checkin_id):
    """Update an existing checkin using data access layer"""
    try:
        # Validate checkin_id format
        if not re.match(r'^CHK\d{4}\d{4}$', checkin_id):
            return jsonify({'error': 'Invalid checkin ID format'}), 400

        # Check if checkin exists
        existing_checkin = data_access.get_by_id('checkins', checkin_id)
        if not existing_checkin:
            return jsonify({'error': 'Checkin not found'}), 404

        updated_checkin = request.get_json()

        # Ensure the checkin ID matches
        updated_checkin['id'] = checkin_id

        # Update timestamp
        updated_checkin['updatedAt'] = datetime.datetime.now().isoformat() + 'Z'

        # Preserve creation timestamp if it exists
        if 'createdAt' in existing_checkin:
            updated_checkin['createdAt'] = existing_checkin['createdAt']
        if 'checkinTime' in existing_checkin:
            updated_checkin['checkinTime'] = existing_checkin['checkinTime']

        # Update using data access layer
        success = data_access.update('checkins', checkin_id, updated_checkin)

        if success:
            return jsonify({'status': 'success', 'checkin': updated_checkin}), 200
        else:
            return jsonify({'error': 'Failed to update checkin'}), 500

    except Exception as e:
        app.logger.error(f"Error updating checkin: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/checkin/<checkin_id>', methods=['DELETE'])
def delete_checkin(checkin_id):
    """Delete a checkin using data access layer"""
    try:
        # Validate checkin_id format
        if not re.match(r'^CHK\d{4}\d{4}$', checkin_id):
            return jsonify({'error': 'Invalid checkin ID format'}), 400

        # Check if checkin exists
        existing_checkin = data_access.get_by_id('checkins', checkin_id)
        if not existing_checkin:
            return jsonify({'error': 'Checkin not found'}), 404

        # Delete using data access layer
        success = data_access.delete('checkins', checkin_id)

        if success:
            return jsonify({'status': 'success', 'message': f'Checkin {checkin_id} deleted successfully'}), 200
        else:
            return jsonify({'error': 'Failed to delete checkin'}), 500

    except Exception as e:
        app.logger.error(f"Error deleting checkin: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

# Feedback API endpoints
@app.route('/api/feedbacks', methods=['GET'])
def get_all_feedbacks():
    """Get all feedbacks using data access layer"""
    try:
        feedbacks_data = data_access.get_all('feedbacks')
        return jsonify(feedbacks_data)
    except Exception as e:
        app.logger.error(f"Error getting all feedbacks: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/feedback-templates', methods=['GET'])
def get_all_feedback_templates():
    """Get all feedback templates using data access layer"""
    try:
        templates_data = data_access.get_all('feedback_templates')
        return jsonify(templates_data)
    except Exception as e:
        app.logger.error(f"Error getting all feedback templates: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/feedback-template', methods=['POST'])
def create_feedback_template():
    """Create a new feedback template using data access layer"""
    try:
        new_template = request.get_json()

        # Validate required fields
        required_fields = ['id', 'name']
        for field in required_fields:
            if field not in new_template:
                return jsonify({'error': f'{field} is required'}), 400

        # Validate template ID format (should be unique)
        template_id = new_template['id']
        if not template_id or not isinstance(template_id, str):
            return jsonify({'error': 'Invalid template ID format'}), 400

        # Check if template already exists
        existing_template = data_access.get_by_id('feedback_templates', template_id)
        if existing_template:
            return jsonify({'error': 'Template with this ID already exists'}), 409

        # Create template using data access layer
        success = data_access.create('feedback_templates', template_id, new_template)

        if success:
            return jsonify({'status': 'success', 'template': new_template}), 201
        else:
            return jsonify({'error': 'Failed to create template'}), 500

    except Exception as e:
        app.logger.error(f"Error creating feedback template: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/feedback-template/<template_id>', methods=['GET'])
def get_feedback_template(template_id):
    """Get a specific feedback template using data access layer"""
    try:
        # Get template using data access layer
        template = data_access.get_by_id('feedback_templates', template_id)

        if template:
            return jsonify(template)
        else:
            return jsonify({'error': 'Template not found'}), 404
    except Exception as e:
        app.logger.error(f"Error retrieving feedback template: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/feedback-template/<template_id>', methods=['PUT'])
def update_feedback_template(template_id):
    """Update a feedback template using data access layer"""
    try:
        # Get existing template
        existing_template = data_access.get_by_id('feedback_templates', template_id)
        if not existing_template:
            return jsonify({'error': 'Template not found'}), 404

        updated_template = request.get_json()

        # Validate required fields
        if 'name' not in updated_template:
            return jsonify({'error': 'name is required'}), 400

        # Update using data access layer
        success = data_access.update('feedback_templates', template_id, updated_template)

        if success:
            return jsonify({'status': 'success', 'template': updated_template}), 200
        else:
            return jsonify({'error': 'Failed to update template'}), 500

    except Exception as e:
        app.logger.error(f"Error updating feedback template: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/feedback-template/<template_id>', methods=['DELETE'])
def delete_feedback_template(template_id):
    """Delete a feedback template using data access layer"""
    try:
        # Check if template exists
        existing_template = data_access.get_by_id('feedback_templates', template_id)
        if not existing_template:
            return jsonify({'error': 'Template not found'}), 404

        # Delete using data access layer
        success = data_access.delete('feedback_templates', template_id)

        if success:
            return jsonify({'status': 'success'}), 200
        else:
            return jsonify({'error': 'Failed to delete template'}), 500

    except Exception as e:
        app.logger.error(f"Error deleting feedback template: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/feedback', methods=['POST'])
def create_feedback():
    """Create a new feedback record using data access layer"""
    try:
        new_feedback = request.get_json()

        # Validate required fields
        required_fields = ['checkinId', 'activityId', 'memberId', 'memberName']
        for field in required_fields:
            if field not in new_feedback:
                return jsonify({'error': f'{field} is required'}), 400

        # Validate that there's some feedback content
        has_overall_feedback = new_feedback.get('overallFeedback', '').strip()
        has_legacy_feedback = new_feedback.get('feedback', '').strip()
        has_skill_assessment = new_feedback.get('skillAssessment', {})

        if not has_overall_feedback and not has_legacy_feedback and not has_skill_assessment:
            return jsonify({'error': 'At least one type of feedback is required'}), 400

        # Validate ID formats
        if not re.match(r'^CHK\d{4}\d{4}$', new_feedback['checkinId']):
            return jsonify({'error': 'Invalid checkin ID format'}), 400
        if not re.match(r'^SN\d{4}\d{4}$', new_feedback['memberId']):
            return jsonify({'error': 'Invalid member ID format'}), 400
        if not re.match(r'^ACT\d{4}\d{4}$', new_feedback['activityId']):
            return jsonify({'error': 'Invalid activity ID format'}), 400

        # Check if checkin exists using data access layer
        checkins = data_access.get_all('checkins')
        if new_feedback['checkinId'] not in checkins:
            return jsonify({'error': 'Checkin not found'}), 404

        # Generate feedback ID if not provided
        if 'id' not in new_feedback:
            feedbacks = data_access.get_all('feedbacks')
            current_year = datetime.datetime.now().year
            prefix = f"FB{current_year}"

            max_sequence = 0
            for feedback in feedbacks.values():
                if 'id' in feedback and feedback['id'].startswith(prefix):
                    try:
                        sequence = int(feedback['id'][len(prefix):])
                        max_sequence = max(max_sequence, sequence)
                    except ValueError:
                        continue

            new_sequence = max_sequence + 1
            new_feedback['id'] = f"{prefix}{new_sequence:04d}"

        # Use provided createdBy or default to 'admin'
        if 'createdBy' not in new_feedback:
            new_feedback['createdBy'] = 'admin'

        # Create feedback using data access layer
        checkin_id = new_feedback['checkinId']
        success = data_access.create('feedbacks', checkin_id, new_feedback)

        if success:
            return jsonify({'status': 'success', 'feedback': new_feedback}), 201
        else:
            return jsonify({'error': 'Failed to create feedback'}), 500

    except Exception as e:
        app.logger.error(f"Error creating feedback: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/feedback/<checkin_id>', methods=['GET'])
def get_feedback(checkin_id):
    """Get feedback for a specific checkin using data access layer"""
    try:
        # Validate checkin_id format
        if not re.match(r'^CHK\d{4}\d{4}$', checkin_id):
            return jsonify({'error': 'Invalid checkin ID format'}), 400

        # Get feedback using data access layer
        feedback = data_access.get_by_id('feedbacks', checkin_id)

        if feedback:
            return jsonify(feedback)
        else:
            return jsonify({'error': 'Feedback not found'}), 404
    except Exception as e:
        app.logger.error(f"Error retrieving feedback: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/feedback/<checkin_id>', methods=['PUT'])
def update_feedback(checkin_id):
    """Update feedback for a specific checkin using data access layer"""
    try:
        # Validate checkin_id format
        if not re.match(r'^CHK\d{4}\d{4}$', checkin_id):
            return jsonify({'error': 'Invalid checkin ID format'}), 400

        # Get existing feedback using data access layer
        existing_feedback = data_access.get_by_id('feedbacks', checkin_id)
        if not existing_feedback:
            return jsonify({'error': 'Feedback not found'}), 404

        updated_feedback = request.get_json()

        # Validate that there's some feedback content
        has_overall_feedback = updated_feedback.get('overallFeedback', '').strip()
        has_legacy_feedback = updated_feedback.get('feedback', '').strip()
        has_skill_assessment = updated_feedback.get('skillAssessment', {})

        if not has_overall_feedback and not has_legacy_feedback and not has_skill_assessment:
            return jsonify({'error': 'At least one type of feedback is required'}), 400

        # Update the feedback fields
        if 'overallFeedback' in updated_feedback:
            existing_feedback['overallFeedback'] = updated_feedback['overallFeedback']

        if 'skillAssessment' in updated_feedback:
            existing_feedback['skillAssessment'] = updated_feedback['skillAssessment']

        # Update legacy feedback field for backward compatibility
        if 'feedback' in updated_feedback:
            existing_feedback['feedback'] = updated_feedback['feedback']
        elif 'overallFeedback' in updated_feedback:
            existing_feedback['feedback'] = updated_feedback['overallFeedback']

        # Update metadata
        if 'updatedBy' in updated_feedback:
            existing_feedback['updatedBy'] = updated_feedback['updatedBy']
        if 'updatedByEmail' in updated_feedback:
            existing_feedback['updatedByEmail'] = updated_feedback['updatedByEmail']

        # Update using data access layer
        success = data_access.update('feedbacks', checkin_id, existing_feedback)

        if success:
            return jsonify({'status': 'success', 'feedback': existing_feedback}), 200
        else:
            return jsonify({'error': 'Failed to update feedback'}), 500

    except Exception as e:
        app.logger.error(f"Error updating feedback: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/feedback/<checkin_id>', methods=['DELETE'])
def delete_feedback(checkin_id):
    """Delete feedback for a specific checkin using data access layer"""
    try:
        # Validate checkin_id format
        if not re.match(r'^CHK\d{4}\d{4}$', checkin_id):
            return jsonify({'error': 'Invalid checkin ID format'}), 400

        # Check if feedback exists
        existing_feedback = data_access.get_by_id('feedbacks', checkin_id)
        if not existing_feedback:
            return jsonify({'error': 'Feedback not found'}), 404

        # Delete using data access layer
        success = data_access.delete('feedbacks', checkin_id)

        if success:
            return jsonify({'status': 'success'}), 200
        else:
            return jsonify({'error': 'Failed to delete feedback'}), 500

    except Exception as e:
        app.logger.error(f"Error deleting feedback: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/member/<member_id>/feedbacks', methods=['GET'])
def get_member_feedbacks(member_id):
    """Get all feedbacks for a specific member"""
    try:
        # Validate member_id format
        if not re.match(r'^SN\d{4}\d{4}$', member_id):
            return jsonify({'error': 'Invalid member ID format'}), 400

        feedbacks_file = os.path.join(DATA_DIR, 'feedbacks.json')
        if not os.path.exists(feedbacks_file):
            return jsonify({'feedbacks': []}), 200

        with open(feedbacks_file, 'r', encoding='utf-8') as f:
            feedbacks = json.load(f)

        # Filter feedbacks for this member
        member_feedbacks = []
        for feedback in feedbacks.values():
            if feedback.get('memberId') == member_id:
                member_feedbacks.append(feedback)

        # Sort by creation date (newest first)
        member_feedbacks.sort(key=lambda x: x.get('createdAt', ''), reverse=True)

        return jsonify({'feedbacks': member_feedbacks}), 200

    except Exception as e:
        app.logger.error(f"Error retrieving member feedbacks: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/members/generate-passwords', methods=['POST'])
def generate_all_passwords():
    """Generate or update passwords for all members using data access layer"""
    try:
        # Get all members using data access layer
        members = data_access.get_all('members')
        if not members:
            return jsonify({'error': 'No members found'}), 404

        # Track members that had passwords generated
        updated_members = []

        # Generate passwords for all members
        for member_id, member in members.items():
            # Generate a default password based on member ID
            default_password = generate_default_password(member_id)
            # Store the hashed password
            member['password'] = default_password

            # Update the member using data access layer
            success = data_access.update('members', member_id, member)

            if success:
                # Add to the list of updated members with plain text password
                updated_members.append({
                    'id': member_id,
                    'name': member.get('name', ''),
                    'password': default_password  # Plain text password for display
                })

        return jsonify({
            'status': 'success',
            'message': f'Generated passwords for {len(updated_members)} members',
            'members': updated_members
        }), 200
    except Exception as e:
        app.logger.error(f"Error generating passwords: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/member/<member_id>/generate-password', methods=['POST'])
def generate_single_password(member_id):
    """Generate or update password for a single member using data access layer"""
    try:
        # Validate member_id format to prevent injection attacks
        if not re.match(r'^SN\d{4}\d{4}$', member_id):
            return jsonify({'error': 'Invalid member ID format'}), 400

        # Check if member exists using data access layer
        existing_member = data_access.get_by_id('members', member_id)
        if not existing_member:
            return jsonify({'error': 'Member not found'}), 404

        # Generate new password
        new_password = generate_default_password(member_id)

        # Update member password
        existing_member['password'] = new_password

        # Update the member using data access layer
        success = data_access.update('members', member_id, existing_member)

        if success:
            return jsonify({
                'status': 'success',
                'message': f'Generated new password for member {member_id}',
                'password': new_password
            }), 200
        else:
            return jsonify({'error': 'Failed to update member password'}), 500

    except Exception as e:
        app.logger.error(f"Error generating password for member {member_id}: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

# File upload helper functions
def allowed_file(filename, file_type):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS.get(file_type, set())

@app.route('/api/submit-student-rating', methods=['POST'])
def submit_student_rating():
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['feedbackId', 'studentEmail', 'rating']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'{field} is required'}), 400

        # Validate rating range
        rating = data.get('rating')
        if not isinstance(rating, (int, float)) or rating < 0.5 or rating > 5:
            return jsonify({'success': False, 'error': 'Rating must be between 0.5 and 5'}), 400

        # Load current feedbacks
        feedbacks_file = os.path.join(DATA_DIR, 'feedbacks.json')
        if os.path.exists(feedbacks_file):
            with open(feedbacks_file, 'r', encoding='utf-8') as f:
                feedbacks = json.load(f)
        else:
            return jsonify({'success': False, 'error': 'Feedbacks file not found'}), 404

        # Find the feedback to update
        feedback_id = data.get('feedbackId')
        feedback_found = False

        for feedback in feedbacks.values():
            if feedback.get('id') == feedback_id:
                # Add student rating to the feedback
                feedback['studentRating'] = {
                    'rating': rating,
                    'comment': data.get('comment', ''),
                    'submittedAt': data.get('submittedAt'),
                    'studentEmail': data.get('studentEmail'),
                    'studentName': data.get('studentName'),
                    'memberId': data.get('memberId')
                }
                feedback_found = True
                break

        if not feedback_found:
            return jsonify({'success': False, 'error': 'Feedback not found'}), 404

        # Save updated feedbacks
        with open(feedbacks_file, 'w', encoding='utf-8') as f:
            json.dump(feedbacks, f, indent=2, ensure_ascii=False)

        # Also save to a separate student ratings file for easy access
        ratings_file = os.path.join(DATA_DIR, 'student_ratings.json')
        if os.path.exists(ratings_file):
            with open(ratings_file, 'r', encoding='utf-8') as f:
                ratings = json.load(f)
        else:
            ratings = {}

        # Add the rating with a unique ID
        rating_id = f"rating_{int(time.time() * 1000)}"
        ratings[rating_id] = {
            'id': rating_id,
            'feedbackId': feedback_id,
            'studentEmail': data.get('studentEmail'),
            'studentName': data.get('studentName'),
            'memberId': data.get('memberId'),
            'activityId': data.get('activityId'),
            'instructorName': data.get('instructorName'),
            'rating': rating,
            'comment': data.get('comment', ''),
            'submittedAt': data.get('submittedAt')
        }

        with open(ratings_file, 'w', encoding='utf-8') as f:
            json.dump(ratings, f, indent=2, ensure_ascii=False)

        return jsonify({'success': True, 'message': 'Rating submitted successfully', 'ratingId': rating_id})

    except Exception as e:
        print(f"Error submitting student rating: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/member-feedback-history/<member_id>', methods=['GET'])
def get_member_feedback_history(member_id):
    """Get historical feedback for a specific member"""
    try:
        # Load feedbacks
        feedbacks_file = os.path.join(DATA_DIR, 'feedbacks.json')
        if not os.path.exists(feedbacks_file):
            return jsonify({'feedbacks': []}), 200

        with open(feedbacks_file, 'r', encoding='utf-8') as f:
            feedbacks = json.load(f)

        # Load activities to get template information
        activities_file = os.path.join(DATA_DIR, 'activities.json')
        activities = {}
        if os.path.exists(activities_file):
            with open(activities_file, 'r', encoding='utf-8') as f:
                activities = json.load(f)

        # Filter feedbacks for the specific member
        member_feedbacks = []
        for feedback in feedbacks.values():
            if feedback.get('memberId') == member_id:
                # Add activity template information
                activity_id = feedback.get('activityId')
                if activity_id and activity_id in activities:
                    feedback['templateId'] = activities[activity_id].get('feedbackTemplateId')
                member_feedbacks.append(feedback)

        # Sort by creation date (oldest first for historical progression)
        member_feedbacks.sort(key=lambda x: x.get('createdAt', ''))

        return jsonify({'feedbacks': member_feedbacks}), 200

    except Exception as e:
        app.logger.error(f"Error getting member feedback history: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/member-skills-progress/<member_id>', methods=['GET'])
def get_member_skills_progress(member_id):
    """Get aggregated skills progress for a specific member by template"""
    try:
        # Load feedbacks
        feedbacks_file = os.path.join(DATA_DIR, 'feedbacks.json')
        if not os.path.exists(feedbacks_file):
            return jsonify({'skillsProgress': {}}), 200

        with open(feedbacks_file, 'r', encoding='utf-8') as f:
            feedbacks = json.load(f)

        # Load activities to get template information
        activities_file = os.path.join(DATA_DIR, 'activities.json')
        activities = {}
        if os.path.exists(activities_file):
            with open(activities_file, 'r', encoding='utf-8') as f:
                activities = json.load(f)

        # Aggregate skills by template
        skills_progress = {}

        for feedback in feedbacks.values():
            if feedback.get('memberId') == member_id:
                activity_id = feedback.get('activityId')
                template_id = None

                if activity_id and activity_id in activities:
                    template_id = activities[activity_id].get('feedbackTemplateId')

                if template_id:
                    if template_id not in skills_progress:
                        skills_progress[template_id] = {
                            'completedSkills': set(),
                            'lastUpdated': feedback.get('createdAt', ''),
                            'feedbackCount': 0
                        }

                    # Add completed skills from this feedback
                    skill_assessment = feedback.get('skillAssessment', {})
                    completed_skills = skill_assessment.get('completedSkills', [])

                    for skill in completed_skills:
                        skills_progress[template_id]['completedSkills'].add(skill)

                    # Update last updated time if this feedback is newer
                    if feedback.get('createdAt', '') > skills_progress[template_id]['lastUpdated']:
                        skills_progress[template_id]['lastUpdated'] = feedback.get('createdAt', '')

                    skills_progress[template_id]['feedbackCount'] += 1

        # Convert sets to lists for JSON serialization
        for template_id in skills_progress:
            skills_progress[template_id]['completedSkills'] = list(skills_progress[template_id]['completedSkills'])

        return jsonify({'skillsProgress': skills_progress}), 200

    except Exception as e:
        app.logger.error(f"Error getting member skills progress: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

def generate_unique_filename(filename):
    # Get file extension
    ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    # Generate unique filename with timestamp and UUID
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    unique_id = str(uuid.uuid4())[:8]
    return f"{timestamp}_{unique_id}.{ext}"

# File upload endpoints
@app.route('/api/upload/<file_type>', methods=['POST'])
def upload_file(file_type):
    try:
        if file_type not in ['images', 'pdfs']:
            return jsonify({'error': 'Invalid file type'}), 400

        if 'file' not in request.files:
            return jsonify({'error': 'No file part'}), 400

        file = request.files['file']

        if file.filename == '':
            return jsonify({'error': 'No selected file'}), 400

        if not allowed_file(file.filename, file_type):
            return jsonify({'error': f'File type not allowed. Allowed types for {file_type}: {ALLOWED_EXTENSIONS[file_type]}'}), 400

        # Process the file
        filename = secure_filename(file.filename)
        unique_filename = generate_unique_filename(filename)
        upload_folder = os.path.join(UPLOADS_DIR, file_type)
        file_path = os.path.join(upload_folder, unique_filename)

        # Ensure the upload folder exists
        if not os.path.exists(upload_folder):
            os.makedirs(upload_folder)

        # Save the file
        file.save(file_path)

        # Return the relative path to the file
        relative_path = f"/uploads/{file_type}/{unique_filename}"
        return jsonify({
            'success': True,
            'filename': unique_filename,
            'originalName': filename,
            'path': relative_path,
            'url': relative_path
        })
    except Exception as e:
        # Log the error
        app.logger.error(f"Error uploading file: {str(e)}")
        # Return a JSON error response
        return jsonify({'error': f'Server error: {str(e)}'}), 500

# File listing endpoint
@app.route('/api/files/<file_type>', methods=['GET'])
def list_files(file_type):
    try:
        if file_type not in ['images', 'pdfs']:
            return jsonify({'error': 'Invalid file type'}), 400

        upload_folder = os.path.join(UPLOADS_DIR, file_type)
        files = []

        # Create the directory if it doesn't exist
        if not os.path.exists(upload_folder):
            os.makedirs(upload_folder)
            return jsonify({'files': []})

        # List files in the directory
        for filename in os.listdir(upload_folder):
            file_path = os.path.join(upload_folder, filename)
            if os.path.isfile(file_path) and allowed_file(filename, file_type):
                file_url = f"/uploads/{file_type}/{filename}"
                files.append({
                    'name': filename,
                    'path': file_url,
                    'url': file_url,
                    'size': os.path.getsize(file_path),
                    'lastModified': os.path.getmtime(file_path)
                })

        # Sort files by last modified time (newest first)
        files.sort(key=lambda x: x['lastModified'], reverse=True)

        return jsonify({
            'files': files
        })
    except Exception as e:
        # Log the error
        app.logger.error(f"Error listing files: {str(e)}")
        # Return a JSON error response
        return jsonify({'error': f'Server error: {str(e)}', 'files': []}), 500

# Serve uploaded files
@app.route('/uploads/')
@app.route('/uploads/<path:subpath>')
def serve_uploads(subpath=''):
    if not subpath:
        return jsonify({'error': 'Directory listing not allowed'}), 403

    parts = subpath.split('/')
    if len(parts) != 2:
        return jsonify({'error': 'Invalid path format'}), 400

    file_type, filename = parts
    return serve_uploaded_file(file_type, filename)

@app.route('/uploads/<file_type>/<filename>')
def serve_uploaded_file(file_type, filename):
    try:
        if file_type not in ['images', 'pdfs']:
            return jsonify({'error': 'Invalid file type'}), 400

        file_path = os.path.join(UPLOADS_DIR, file_type, filename)
        if not os.path.exists(file_path):
            return jsonify({'error': 'File not found'}), 404

        return send_from_directory(os.path.join(UPLOADS_DIR, file_type), filename)
    except Exception as e:
        app.logger.error(f"Error serving file: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

# Static files routes
@app.route('/assets/<path:filename>')
def serve_assets(filename):
    return send_from_directory(ASSETS_DIR, filename)

@app.route('/<path:filename>')
def serve_static_files(filename):
    # Check if the file exists in the root directory
    if os.path.isfile(os.path.join(ROOT_DIR, filename)):
        return send_from_directory(ROOT_DIR, filename)
    else:
        return 'File not found', 404

# Add some basic logging
@app.before_request
def log_request_info():
    app.logger.debug('Request Headers: %s', request.headers)
    app.logger.debug('Request Path: %s', request.path)

# ============================================================================
# EMAIL API ENDPOINTS
# ============================================================================

class BrevoEmailService:
    """Brevo Email Service for SnowNavi"""

    def __init__(self):
        self.api_key = os.environ.get('BREVO_API_KEY')
        self.api_endpoint = os.environ.get('BREVO_API_ENDPOINT', 'https://api.brevo.com/v3/smtp/email')
        self.default_sender = {
            'email': os.environ.get('BREVO_SENDER_EMAIL', '<EMAIL>'),
            'name': os.environ.get('BREVO_SENDER_NAME', 'SnowNavi System')
        }

        if not self.api_key:
            app.logger.error('BREVO_API_KEY environment variable is required')
            raise ValueError('BREVO_API_KEY environment variable is required')

        app.logger.info(f'✅ Brevo Email Service initialized')
        app.logger.info(f'📧 Default sender: {self.default_sender["name"]} <{self.default_sender["email"]}>')

    def send_email(self, email_data):
        """Send email using Brevo API"""
        try:
            # Validate required fields
            if not email_data.get('to') or not email_data.get('subject') or not (email_data.get('content') or email_data.get('htmlContent')):
                raise ValueError('Missing required fields: to, subject, and content are required')

            # Prepare sender information
            sender = email_data.get('sender', self.default_sender)

            # Prepare email payload
            payload = {
                'sender': {
                    'name': sender.get('name', self.default_sender['name']),
                    'email': sender.get('email', self.default_sender['email'])
                },
                'to': [{
                    'email': email_data['to'],
                    'name': email_data.get('toName', email_data['to'].split('@')[0])
                }],
                'subject': email_data['subject']
            }

            # Add content (prefer HTML if provided, otherwise convert text to HTML)
            if email_data.get('htmlContent'):
                payload['htmlContent'] = email_data['htmlContent']
            else:
                payload['htmlContent'] = self._text_to_html(email_data['content'], email_data['subject'])

            # Add plain text content if available
            if email_data.get('content') and not email_data.get('htmlContent'):
                payload['textContent'] = email_data['content']

            app.logger.info(f'📤 Sending email to: {email_data["to"]}')
            app.logger.info(f'📋 Subject: {email_data["subject"]}')

            # Send email via API
            headers = {
                'Accept': 'application/json',
                'api-key': self.api_key,
                'Content-Type': 'application/json'
            }

            response = requests.post(self.api_endpoint, headers=headers, json=payload)

            if not response.ok:
                error_message = f'HTTP {response.status_code}: {response.reason}'
                try:
                    error_data = response.json()
                    error_message = f'Brevo API Error ({response.status_code}): {error_data.get("message", error_data.get("code", response.reason))}'
                    app.logger.error(f'❌ Brevo API Error Details: {error_data}')
                except:
                    app.logger.error('❌ Failed to parse error response')
                raise Exception(error_message)

            result = response.json()
            app.logger.info(f'✅ Email sent successfully: {result.get("messageId")}')
            return result

        except Exception as error:
            app.logger.error(f'❌ Email sending failed: {error}')
            raise error

    def _text_to_html(self, text, subject):
        """Convert plain text to HTML with basic formatting"""
        html_content = text.replace('\n', '<br>').replace('**', '<strong>').replace('*', '<em>')

        return f"""
            <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="border-bottom: 3px solid #E53512; margin-bottom: 20px;">
                        <h2 style="color: #E53512; margin: 0; padding-bottom: 10px;">
                            {subject}
                        </h2>
                    </div>
                    <div style="margin: 20px 0; font-size: 16px;">
                        {html_content}
                    </div>
                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">
                    <div style="text-align: center; font-size: 12px; color: #666;">
                        <p>Sent via SnowNavi Email System</p>
                        <p style="margin: 5px 0;">Powered by Brevo</p>
                    </div>
                </body>
            </html>
        """

# Initialize email service
try:
    email_service = BrevoEmailService()
except Exception as e:
    app.logger.error(f'Failed to initialize email service: {e}')
    email_service = None

@app.route('/api/email/config', methods=['GET'])
def get_email_config():
    """Get email configuration (without sensitive data)"""
    try:
        if not email_service:
            return jsonify({'success': False, 'error': 'Email service not available'}), 500

        config = {
            'apiEndpoint': email_service.api_endpoint,
            'senderEmail': email_service.default_sender['email'],
            'senderName': email_service.default_sender['name'],
            'hasApiKey': bool(email_service.api_key),
            'apiKeyPreview': email_service.api_key[:20] + '...' if email_service.api_key else 'Not set'
        }

        return jsonify({'success': True, 'config': config})
    except Exception as e:
        app.logger.error(f'Error getting email config: {e}')
        return jsonify({'success': False, 'error': 'Failed to get configuration'}), 500

@app.route('/api/email/test', methods=['POST'])
def send_test_email():
    """Send test email"""
    try:
        if not email_service:
            return jsonify({'success': False, 'error': 'Email service not available'}), 500

        data = request.get_json()
        email = data.get('email')

        if not email:
            return jsonify({'success': False, 'error': 'Email address is required'}), 400

        subject = 'SnowNavi 邮件系统测试 / Email System Test'
        content = f"""您好！/ Hello!

这是来自 SnowNavi 系统的测试邮件。
This is a test email from SnowNavi system.

如果您收到这封邮件，说明邮件发送功能正常工作。
If you receive this email, it means the email sending function is working properly.

测试时间 / Test Time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

祝好！/ Best regards,
SnowNavi Team"""

        result = email_service.send_email({
            'to': email,
            'subject': subject,
            'content': content
        })

        return jsonify({
            'success': True,
            'messageId': result.get('messageId'),
            'message': 'Test email sent successfully'
        })
    except Exception as e:
        app.logger.error(f'Error sending test email: {e}')
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/email/send', methods=['POST'])
def send_custom_email():
    """Send custom email"""
    try:
        if not email_service:
            return jsonify({'success': False, 'error': 'Email service not available'}), 500

        data = request.get_json()
        to = data.get('to')
        subject = data.get('subject')
        content = data.get('content')
        html_content = data.get('htmlContent')
        to_name = data.get('toName')

        if not to or not subject or not (content or html_content):
            return jsonify({
                'success': False,
                'error': 'Missing required fields: to, subject, and content/htmlContent'
            }), 400

        result = email_service.send_email({
            'to': to,
            'subject': subject,
            'content': content,
            'htmlContent': html_content,
            'toName': to_name
        })

        return jsonify({
            'success': True,
            'messageId': result.get('messageId'),
            'message': 'Email sent successfully'
        })
    except Exception as e:
        app.logger.error(f'Error sending email: {e}')
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/email/feedback', methods=['POST'])
def send_feedback_email():
    """Send feedback email to student"""
    try:
        if not email_service:
            return jsonify({'success': False, 'error': 'Email service not available'}), 500

        data = request.get_json()
        student_email = data.get('studentEmail')
        student_name = data.get('studentName')
        course_name = data.get('courseName')
        instructor_name = data.get('instructorName')
        feedback_content = data.get('feedbackContent')
        feedback_url = data.get('feedbackUrl')

        if not all([student_email, student_name, course_name, instructor_name, feedback_content]):
            return jsonify({
                'success': False,
                'error': 'Missing required fields: studentEmail, studentName, courseName, instructorName, feedbackContent'
            }), 400

        subject = f'🎿 SnowNavi 课程反馈 - {course_name}'

        html_content = f"""
            <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background: linear-gradient(135deg, #E53512 0%, #9ED4E7 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center;">
                        <h1 style="margin: 0; font-size: 24px;">🎿 SnowNavi</h1>
                        <p style="margin: 10px 0 0 0; font-size: 16px;">滑雪课程反馈报告</p>
                    </div>

                    <div style="background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px;">
                        <h2 style="color: #E53512; margin-top: 0;">亲爱的 {student_name}，</h2>

                        <p>感谢您参加我们的滑雪课程！以下是您的课程反馈：</p>

                        <div style="background: white; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #E53512;">
                            <h3 style="margin-top: 0; color: #E53512;">课程信息</h3>
                            <p><strong>课程名称：</strong>{course_name}</p>
                            <p><strong>教练：</strong>{instructor_name}</p>
                            <p><strong>反馈时间：</strong>{datetime.datetime.now().strftime('%Y年%m月%d日')}</p>
                        </div>

                        <div style="background: white; padding: 15px; border-radius: 6px; margin: 20px 0;">
                            <h3 style="margin-top: 0; color: #E53512;">教练反馈</h3>
                            <div style="white-space: pre-line; font-size: 16px;">
                                {feedback_content}
                            </div>
                        </div>

                        {f'''
                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{feedback_url}"
                               style="background: #E53512; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                                📊 查看详细反馈报告
                            </a>
                        </div>
                        ''' if feedback_url else ''}

                        <div style="background: #e8f4f8; padding: 15px; border-radius: 6px; margin: 20px 0;">
                            <h4 style="margin-top: 0; color: #0c5460;">💡 学习建议</h4>
                            <p>继续保持练习，每一次滑雪都是进步的机会！如有任何问题，请随时联系我们的教练团队。</p>
                        </div>

                        <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">

                        <div style="text-align: center; color: #666; font-size: 14px;">
                            <p>祝您滑雪愉快！🎿</p>
                            <p><strong>SnowNavi 团队</strong></p>
                            <p style="font-size: 12px; margin-top: 20px;">
                                此邮件由 SnowNavi 系统自动发送，请勿直接回复。
                            </p>
                        </div>
                    </div>
                </body>
            </html>
        """

        result = email_service.send_email({
            'to': student_email,
            'toName': student_name,
            'subject': subject,
            'htmlContent': html_content
        })

        return jsonify({
            'success': True,
            'messageId': result.get('messageId'),
            'message': 'Feedback email sent successfully'
        })
    except Exception as e:
        app.logger.error(f'Error sending feedback email: {e}')
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/email/welcome', methods=['POST'])
def send_welcome_email():
    """Send welcome email to new student"""
    try:
        if not email_service:
            return jsonify({'success': False, 'error': 'Email service not available'}), 500

        data = request.get_json()
        email = data.get('email')
        name = data.get('name')
        course_name = data.get('courseName')

        if not email or not name:
            return jsonify({
                'success': False,
                'error': 'Missing required fields: email, name'
            }), 400

        subject = '🎿 欢迎加入 SnowNavi！'

        html_content = f"""
            <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background: linear-gradient(135deg, #E53512 0%, #9ED4E7 100%); color: white; padding: 30px; border-radius: 8px; text-align: center;">
                        <h1 style="margin: 0; font-size: 28px;">🎿 欢迎来到 SnowNavi！</h1>
                        <p style="margin: 15px 0 0 0; font-size: 18px;">让每一次滑雪都成为进步的阶梯</p>
                    </div>

                    <div style="padding: 30px 20px;">
                        <h2 style="color: #E53512;">亲爱的 {name}，</h2>

                        <p style="font-size: 16px;">欢迎加入 SnowNavi 滑雪学习系统！我们很高兴能够陪伴您的滑雪学习之旅。</p>

                        <div style="background: #f9f4f3; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #E53512;">
                            <h3 style="margin-top: 0; color: #E53512;">🎯 您将获得：</h3>
                            <ul style="margin: 0; padding-left: 20px;">
                                <li>📊 详细的学习进度追踪</li>
                                <li>🎓 个性化的教练反馈</li>
                                <li>📱 随时查看学习报告</li>
                                <li>🏆 技能成就系统</li>
                                <li>📈 长期学习规划</li>
                            </ul>
                        </div>

                        {f'''
                        <div style="background: #e8f4f8; padding: 20px; border-radius: 8px; margin: 25px 0;">
                            <h3 style="margin-top: 0; color: #0c5460;">📚 您的课程</h3>
                            <p style="font-size: 16px; margin: 0;"><strong>{course_name}</strong></p>
                        </div>
                        ''' if course_name else ''}

                        <div style="text-align: center; margin: 30px 0;">
                            <p style="font-size: 18px; color: #E53512; font-weight: bold;">准备好开始您的滑雪之旅了吗？🎿</p>
                        </div>

                        <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">

                        <div style="text-align: center; color: #666; font-size: 14px;">
                            <p>祝您学习愉快！</p>
                            <p><strong>SnowNavi 团队</strong></p>
                        </div>
                    </div>
                </body>
            </html>
        """

        result = email_service.send_email({
            'to': email,
            'toName': name,
            'subject': subject,
            'htmlContent': html_content
        })

        return jsonify({
            'success': True,
            'messageId': result.get('messageId'),
            'message': 'Welcome email sent successfully'
        })
    except Exception as e:
        app.logger.error(f'Error sending welcome email: {e}')
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/email-health', methods=['GET'])
def email_health_check():
    """Email service health check"""
    try:
        if not email_service:
            return jsonify({
                'status': 'error',
                'message': 'Email service not available',
                'timestamp': datetime.datetime.now().isoformat()
            }), 500

        return jsonify({
            'status': 'ok',
            'service': 'SnowNavi Email API',
            'timestamp': datetime.datetime.now().isoformat(),
            'config': {
                'senderEmail': email_service.default_sender['email'],
                'hasApiKey': bool(email_service.api_key)
            }
        })
    except Exception as e:
        app.logger.error(f'Email health check failed: {e}')
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.datetime.now().isoformat()
        }), 500

# ============================================================================
# EMAIL TEMPLATES API
# ============================================================================

@app.route('/api/email-templates', methods=['GET'])
def get_email_templates():
    """Get all email templates using data access layer"""
    try:
        templates_data = data_access.get_all('email_templates')

        # If no templates exist, create default ones
        if not templates_data:
            default_template = {
                "id": "feedback_notification",
                "name": "Feedback Notification / 反馈通知",
                "description": "Automatic bilingual email sent to students when feedback is completed / 反馈完成时自动发送给学生的双语邮件",
                "type": "feedback",
                "subject": "🎿 SnowNavi Course Feedback / 课程反馈 - {{courseName}}",
                "htmlContent": """<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <div style="background: linear-gradient(135deg, #E53512 0%, #9ED4E7 100%); color: white; padding: 20px; text-align: center;">
        <h1 style="margin: 0;">🎿 SnowNavi</h1>
        <p style="margin: 10px 0 0 0;">Course Feedback Report / 滑雪课程反馈报告</p>
    </div>
    <div style="padding: 20px; background: white;">
        <p>Dear {{studentName}} / 亲爱的 {{studentName}}，</p>
        <p>Hello! Your skiing course feedback has been completed. Please check the details below.</p>
        <p>您好！您的滑雪课程反馈已经完成，请查看以下详细信息：</p>

        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #E53512; margin-top: 0;">Course Information / 课程信息</h3>
            <p><strong>Course Name / 课程名称：</strong>{{courseName}}</p>
            <p><strong>Instructor / 教练：</strong>{{instructorName}}</p>
            <p><strong>Date / 日期：</strong>{{courseDate}}</p>
        </div>

        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #28a745; margin-top: 0;">Learning Progress / 学习进度</h3>
            {{feedbackContent}}
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{feedbackUrl}}" style="background: #E53512; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                View Detailed Feedback / 查看详细反馈
            </a>
        </div>

        <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">

        <div style="text-align: center; color: #666; font-size: 14px;">
            <p>Happy skiing! / 祝您滑雪愉快！🎿</p>
            <p><strong>SnowNavi Team / SnowNavi 团队</strong></p>
            <p style="font-size: 12px; margin-top: 20px;">
                This email is automatically sent by SnowNavi system, please do not reply directly.<br>
                此邮件由 SnowNavi 系统自动发送，请勿直接回复。
            </p>
        </div>
    </div>
</div>""",
                "triggers": {
                    "onFeedbackComplete": True,
                    "onFeedbackUpdate": False,
                    "manualOnly": False
                },
                "active": True,
                "createdAt": datetime.datetime.now().isoformat(),
                "updatedAt": datetime.datetime.now().isoformat()
            }

            # Create the default template using data access layer
            data_access.create('email_templates', 'feedback_notification', default_template)
            templates_data = {"feedback_notification": default_template}

        return jsonify(templates_data)
    except Exception as e:
        app.logger.error(f'Error getting email templates: {e}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/email-templates', methods=['POST'])
def save_email_templates():
    """Save email templates using data access layer"""
    try:
        email_templates = request.get_json()

        # Save each template individually using data access layer
        success_count = 0
        errors = []

        for template_id, template_data in email_templates.items():
            try:
                # Check if template exists
                existing_template = data_access.get_by_id('email_templates', template_id)

                if existing_template:
                    # Update existing template
                    data_access.update('email_templates', template_id, template_data)
                else:
                    # Create new template
                    data_access.create('email_templates', template_id, template_data)

                success_count += 1
            except Exception as template_error:
                error_msg = f'Failed to save template {template_id}: {str(template_error)}'
                errors.append(error_msg)
                app.logger.error(error_msg)

        if errors:
            return jsonify({
                'success': False,
                'message': f'Saved {success_count} templates, {len(errors)} failed',
                'errors': errors
            }), 500
        else:
            return jsonify({'success': True, 'message': f'All {success_count} email templates saved successfully'})

    except Exception as e:
        app.logger.error(f'Error saving email templates: {e}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/email-templates/<template_id>', methods=['GET'])
def get_email_template(template_id):
    """Get a specific email template using data access layer"""
    try:
        template = data_access.get_by_id('email_templates', template_id)

        if not template:
            return jsonify({'error': 'Email template not found'}), 404

        return jsonify(template)
    except Exception as e:
        app.logger.error(f'Error getting email template: {e}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/generate-learning-timeline', methods=['POST'])
def generate_learning_timeline_api():
    """Generate learning timeline content for email"""
    try:
        data = request.get_json()
        member_id = data.get('memberId')
        current_feedback_date = data.get('currentFeedbackDate')

        if not member_id or not current_feedback_date:
            return jsonify({'error': 'Missing required parameters'}), 400

        timeline_content = generate_learning_timeline_content(member_id, current_feedback_date)

        return jsonify({
            'success': True,
            'timelineContent': timeline_content
        })

    except Exception as e:
        app.logger.error(f'Error generating learning timeline API: {e}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/send-auto-email', methods=['POST'])
def send_auto_email():
    """Send automatic email based on triggers"""
    try:
        if not email_service:
            return jsonify({'success': False, 'error': 'Email service not available'}), 500

        data = request.get_json()
        trigger_type = data.get('triggerType')  # e.g., 'onFeedbackComplete'
        email_data = data.get('emailData', {})

        # Load email templates using data access layer
        email_templates = data_access.get_all('email_templates')

        if not email_templates:
            return jsonify({'success': False, 'error': 'Email templates not found'}), 404

        # Find templates that should be triggered
        triggered_templates = []
        for template_id, template in email_templates.items():
            if (template.get('active', False) and
                template.get('triggers', {}).get(trigger_type, False)):
                triggered_templates.append(template)

        if not triggered_templates:
            return jsonify({'success': True, 'message': 'No templates configured for this trigger', 'sent': 0})

        sent_count = 0
        errors = []

        for template in triggered_templates:
            try:
                # Replace template variables
                subject = replace_template_variables(template['subject'], email_data)
                html_content = replace_template_variables(template['htmlContent'], email_data)

                # Send email
                result = email_service.send_email({
                    'to': email_data.get('studentEmail'),
                    'toName': email_data.get('studentName'),
                    'subject': subject,
                    'htmlContent': html_content
                })

                sent_count += 1
                app.logger.info(f'Auto email sent using template {template["id"]} to {email_data.get("studentEmail")}')

            except Exception as template_error:
                error_msg = f'Failed to send email with template {template["id"]}: {str(template_error)}'
                errors.append(error_msg)
                app.logger.error(error_msg)

        return jsonify({
            'success': True,
            'message': f'Sent {sent_count} emails',
            'sent': sent_count,
            'errors': errors
        })

    except Exception as e:
        app.logger.error(f'Error sending auto email: {e}')
        return jsonify({'success': False, 'error': str(e)}), 500

def generate_learning_timeline_content(member_id, current_feedback_date):
    """Generate learning timeline content for email"""
    try:
        # Load required data
        feedbacks_file = os.path.join(DATA_DIR, 'feedbacks.json')
        activities_file = os.path.join(DATA_DIR, 'activities.json')
        templates_file = os.path.join(DATA_DIR, 'feedback_templates.json')

        feedbacks = {}
        activities = {}
        templates = {}

        if os.path.exists(feedbacks_file):
            with open(feedbacks_file, 'r', encoding='utf-8') as f:
                feedbacks = json.load(f)

        if os.path.exists(activities_file):
            with open(activities_file, 'r', encoding='utf-8') as f:
                activities = json.load(f)

        if os.path.exists(templates_file):
            with open(templates_file, 'r', encoding='utf-8') as f:
                templates = json.load(f)

        # Get member feedbacks up to current date
        current_time = datetime.datetime.fromisoformat(current_feedback_date.replace('Z', '+00:00')).timestamp() * 1000
        member_feedbacks = []

        for feedback in feedbacks.values():
            if feedback.get('memberId') == member_id:
                feedback_time = datetime.datetime.fromisoformat(feedback['createdAt'].replace('Z', '+00:00')).timestamp() * 1000
                if feedback_time <= current_time:
                    member_feedbacks.append(feedback)

        # Sort by creation date
        member_feedbacks.sort(key=lambda x: x['createdAt'])

        if len(member_feedbacks) <= 1:
            return "这是您的第一次课程反馈！/ This is your first course feedback!"

        # Generate timeline HTML
        timeline_html = """
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #9ED4E7;">
            <h3 style="color: #E53512; margin-top: 0; margin-bottom: 15px;">🎯 您的学习历程 / Your Learning Journey</h3>
        """

        # Track cumulative skills
        all_previous_skills = set()

        for i, feedback in enumerate(member_feedbacks):
            activity = activities.get(feedback.get('activityId'), {})
            activity_name_zh = activity.get('name', {}).get('zh', '未知活动')
            activity_name_en = activity.get('name', {}).get('en', 'Unknown Activity')

            # Format date
            try:
                date_obj = datetime.datetime.fromisoformat(activity.get('date', feedback['createdAt']).replace('Z', '+00:00'))
                formatted_date = date_obj.strftime('%b %d, %Y')
            except:
                formatted_date = 'Unknown Date'

            # Get skills for this session
            current_skills = set(feedback.get('skillAssessment', {}).get('completedSkills', []))
            new_skills = current_skills - all_previous_skills
            all_previous_skills.update(current_skills)

            # Get skill names
            skill_names = get_skill_names_for_email(list(new_skills), templates, activity.get('feedbackTemplateId'))

            # Check if this is the current session
            is_current = abs(datetime.datetime.fromisoformat(feedback['createdAt'].replace('Z', '+00:00')).timestamp() * 1000 - current_time) < 1000

            timeline_html += f"""
            <div style="margin-bottom: 15px; padding: 12px; background: white; border-radius: 6px; border-left: 3px solid {'#E53512' if is_current else '#9ED4E7'};">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                    <strong style="color: {'#E53512' if is_current else '#2F2F2F'};">
                        第{i+1}次课程 / Session {i+1}: {activity_name_zh} / {activity_name_en}
                        {' (当前课程 / Current)' if is_current else ''}
                    </strong>
                    <span style="font-size: 0.9em; color: #717171;">{formatted_date}</span>
                </div>
                <div style="margin-bottom: 8px;">
                    <span style="background: #e3f2fd; color: #1976d2; padding: 2px 8px; border-radius: 12px; font-size: 0.85em; margin-right: 8px;">
                        🆕 {len(new_skills)} 新技能 / new skills
                    </span>
                    <span style="background: #f3e5f5; color: #7b1fa2; padding: 2px 8px; border-radius: 12px; font-size: 0.85em;">
                        📊 {len(all_previous_skills)} 累计技能 / total skills
                    </span>
                </div>
            """

            if skill_names:
                timeline_html += """
                <div style="margin-top: 8px;">
                    <strong style="color: #E53512; font-size: 0.9em;">新掌握技能 / New skills mastered:</strong>
                    <ul style="margin: 5px 0 0 20px; padding: 0;">
                """
                for skill_name in skill_names:
                    timeline_html += f"<li style='margin-bottom: 3px; font-size: 0.85em;'>{skill_name}</li>"
                timeline_html += "</ul></div>"

            timeline_html += "</div>"

        timeline_html += "</div>"
        return timeline_html

    except Exception as e:
        app.logger.error(f'Error generating learning timeline: {e}')
        return "学习历程加载中... / Learning progress loading..."

def get_skill_names_for_email(skill_ids, templates, template_id):
    """Get skill names for email from templates"""
    skill_names = []

    # Default skill names as fallback
    default_skills = {
        'equipment-intro': '滑雪装备介绍 (Equipment Introduction)',
        'single-foot-familiarity': '单脚熟悉雪板 (Single Foot Board Familiarity)',
        'single-foot-sliding': '单脚滑板式滑动 (Single Foot Skateboard Sliding)',
        'single-foot-climbing': '单脚爬坡 (Single Foot Climbing)',
        'single-foot-straight': '单脚直滑降 (Single Foot Straight Descent)',
        'single-foot-heel-brake': '单脚脚后跟减速 (Single Foot Heel Braking)',
        'single-foot-j-turn': '单脚J弯 (Single Foot J-Turn)',
        'static-gas-pedal': '静态踩油门练习 (Static Gas Pedal Practice)',
        'single-heel-side-push': '单脚后刃推坡 (Single Foot Heel Side Push)',
        'single-toe-side-push': '单脚前刃推坡 (Single Foot Toe Side Push)',
        'both-heel-side-push': '双脚后刃推坡 (Both Feet Heel Side Push)',
        'both-toe-side-push': '双脚前刃推坡 (Both Feet Toe Side Push)',
        'both-heel-falling-leaf': '双脚后刃落叶飘 (Both Feet Heel Side Falling Leaf)',
        'both-toe-falling-leaf': '双脚前刃落叶飘 (Both Feet Toe Side Falling Leaf)',
        'both-heel-power-falling-leaf': '双脚后刃强力落叶飘 (Both Feet Heel Side Power Falling Leaf)',
        'both-toe-power-falling-leaf': '双脚前刃强力落叶飘 (Both Feet Toe Side Power Falling Leaf)',
        'static-rotation': '静态旋转练习 (Static Rotation Practice)',
        'step-turns': '阶梯转弯 (Step Turns)',
        'j-turns': 'J弯 (J-Turns)',
        'walking-edge-change': '走步模拟换刃 (Walking Edge Change Simulation)',
        'beginner-turns': '新手转弯 (Beginner Turns)',
        'edge-change-traverse': '换刃后增加横穿雪道 (Edge Change with Slope Traverse)',
        'traverse-body-movement': '横穿雪道加入身体起伏 (Traverse with Body Movement)',
        'continuous-edge-change': '连续换刃 (Continuous Edge Changes)',
        'scrub-360': '搓雪360 (Scrub 360)'
    }

    # Try to get names from template
    if template_id and template_id in templates:
        template_data = templates[template_id]
        if 'sections' in template_data:
            for section in template_data['sections'].values():
                if 'skills' in section:
                    for skill_id, skill_data in section['skills'].items():
                        if skill_id in skill_ids:
                            skill_name_zh = skill_data.get('name', {}).get('zh', skill_id)
                            skill_name_en = skill_data.get('name', {}).get('en', skill_id)
                            if '(' in skill_name_zh:
                                skill_names.append(skill_name_zh)
                            else:
                                skill_names.append(f"{skill_name_zh} ({skill_name_en})")

    # Fallback to default names for any missing skills
    for skill_id in skill_ids:
        if not any(skill_id in name for name in skill_names):
            skill_names.append(default_skills.get(skill_id, skill_id))

    return skill_names

def replace_template_variables(template_string, data):
    """Replace template variables with actual data"""
    import re

    # Simple template variable replacement
    for key, value in data.items():
        if value is not None:
            # Handle multilingual objects (extract English version)
            if isinstance(value, dict) and 'en' in value:
                value = value['en']
            elif isinstance(value, dict):
                # If it's a dict but no 'en' key, convert to string representation
                value = str(value)

            template_string = template_string.replace(f'{{{{{key}}}}}', str(value))

    # Handle conditional blocks (simple implementation for {{#if variable}}...{{/if}})
    def replace_conditional(match):
        var_name = match.group(1)
        content = match.group(2)
        if data.get(var_name):
            return content
        return ''

    template_string = re.sub(r'\{\{#if (\w+)\}\}(.*?)\{\{/if\}\}', replace_conditional, template_string, flags=re.DOTALL)

    return template_string

# ============================================================================
# ERROR HANDLERS
# ============================================================================

# Error handlers
@app.errorhandler(404)
def page_not_found(e):
    return jsonify(error=str(e)), 404

@app.errorhandler(413)
def request_entity_too_large(e):
    max_size_mb = app.config['MAX_CONTENT_LENGTH'] / (1024 * 1024)
    return jsonify(error=f"File too large. Maximum allowed size is {max_size_mb:.1f}MB"), 413

@app.errorhandler(500)
def server_error(e):
    app.logger.error('Server Error: %s', e)
    return jsonify(error=str(e)), 500

if __name__ == '__main__':
    # Set up logging
    import logging
    logging.basicConfig(level=logging.INFO)

    # Get host and port from environment variables or use defaults
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 8899))

    print(f"Starting server at http://{host}:{port}")
    app.run(host=host, port=port, debug=True)
