<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Template Management - SnowNavi Admin</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="assets/picture/snownavi_logo.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/picture/snownavi_logo.png">
    <link rel="shortcut icon" href="assets/picture/snownavi_logo.png">

    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
            --success-green: #28a745;
            --warning-yellow: #ffc107;
            --danger-red: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-light);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .header {
            background: var(--contrast-white);
            padding: 1rem 2rem;
            border-bottom: 3px solid var(--main-red);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-section img {
            width: 50px;
            height: 50px;
        }

        .logo-section h1 {
            color: var(--main-red);
            font-size: 1.8rem;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
        }

        .nav-link {
            color: var(--text-gray);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            background: var(--bg-light);
            color: var(--main-red);
        }

        /* Authentication styles */
        .auth-container {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        .auth-message {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            text-align: center;
            max-width: 400px;
        }
        .auth-message h2 {
            color: #E53512;
            margin-top: 0;
        }
        .auth-btn {
            background: #E53512;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            display: inline-block;
            margin-top: 1rem;
            cursor: pointer;
        }
        .auth-btn:hover {
            background: #c52e10;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .user-info img {
            width: 32px;
            height: 32px;
            border-radius: 50%;
        }
        .logout-btn {
            background: none;
            border: none;
            color: var(--main-red);
            cursor: pointer;
            text-decoration: underline;
            padding: 0.25rem 0.5rem;
        }
        .logout-btn:hover {
            background: var(--bg-light);
            border-radius: 4px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-title {
            text-align: center;
            margin-bottom: 2rem;
        }

        .page-title h2 {
            color: var(--main-red);
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .page-title p {
            color: var(--text-gray);
            font-size: 1.1rem;
        }

        .main-container {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .template-list {
            background: var(--contrast-white);
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }

        .template-list h3 {
            color: var(--main-red);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .template-item {
            padding: 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .template-item:hover {
            border-color: var(--main-red);
            background: var(--bg-light);
        }

        .template-item.active {
            border-color: var(--main-red);
            background: var(--bg-light);
        }

        .template-item h4 {
            color: var(--main-red);
            margin-bottom: 0.5rem;
        }

        .template-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 0.5rem;
        }

        .template-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 0.5rem;
        }

        .badge-feedback {
            background: #e3f2fd;
            color: #1976d2;
        }

        .badge-welcome {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .badge-reminder {
            background: #fff3e0;
            color: #f57c00;
        }

        .form-container {
            background: var(--contrast-white);
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .no-selection {
            text-align: center;
            color: var(--text-gray);
            padding: 3rem;
            font-size: 1.1rem;
        }

        .form-section {
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #e0e0e0;
        }

        .form-section:last-child {
            border-bottom: none;
        }

        .form-section h3 {
            color: var(--main-red);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 0.75rem;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--main-red);
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }

        .actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: var(--main-red);
            color: white;
        }

        .btn-primary:hover {
            background: #c52e10;
        }

        .btn-success {
            background: var(--success-green);
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-secondary {
            background: var(--text-gray);
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-danger {
            background: var(--danger-red);
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
        }

        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
        }

        .loading {
            text-align: center;
            color: var(--text-gray);
            padding: 2rem;
        }

        .trigger-section {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .trigger-section h4 {
            color: var(--main-red);
            margin-bottom: 1rem;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 0.5rem;
            transform: scale(1.2);
        }

        .preview-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .preview-container h4 {
            color: var(--main-red);
            margin-bottom: 1rem;
        }

        .preview-email {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 1rem;
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .main-container {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
                align-items: stretch;
            }
        }

        /* Toast notification styles */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: #333;
            font-weight: 500;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease-in-out;
            max-width: 300px;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.unsaved {
            background: #FFF3CD;
            border-left: 4px solid #FFC107;
        }

        .toast.saved {
            background: #D4EDDA;
            border-left: 4px solid #28A745;
        }

        .toast.saving {
            background: #E2E3E5;
            border-left: 4px solid #6C757D;
        }
    </style>
</head>
<body>
    <!-- Authentication overlay -->
    <div class="auth-container" id="auth-container">
        <div class="auth-message">
            <h2>Authentication Required</h2>
            <p>You need to be logged in to access this page.</p>
            <a href="login.html" class="auth-btn">Go to Login</a>
        </div>
    </div>

    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                <img src="assets/picture/snownavi_logo.png" alt="SnowNavi Logo">
                <h1>SnowNavi Admin</h1>
            </div>
            <nav class="nav-links">
                <a href="admin.html" class="nav-link">🏠 Dashboard</a>
                <a href="course_admin.html" class="nav-link">📚 Courses</a>
                <a href="member_admin.html" class="nav-link">👥 Members</a>
                <a href="activity_admin.html" class="nav-link">📅 Activities</a>
                <a href="checkin_admin.html" class="nav-link">✅ Check-ins</a>
                <a href="feedback_template_admin.html" class="nav-link">📝 Templates</a>
                <a href="email_template_admin.html" class="nav-link" style="color: var(--main-red);">📧 Email Templates</a>
                <a href="email_preview.html" class="nav-link">👁️ Email Preview</a>
            </nav>
            <div class="user-info" id="user-info">
                <!-- User info will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <div class="container">
        <div class="page-title">
            <h2>📧 Email Template Management</h2>
            <p>Create and manage email templates with automatic sending triggers</p>
        </div>

        <div class="main-container">
            <div class="template-list" id="template-list">
                <h3>📋 Available Email Templates</h3>
                <div class="loading">Loading email templates...</div>
            </div>
            
            <div class="form-container" id="form-container">
                <div class="no-selection">Select an email template from the left panel or create a new one</div>
            </div>
        </div>

        <div class="actions">
            <button class="btn btn-primary" onclick="addNewTemplate()">➕ Add New Template</button>
            <div style="display: flex; gap: 1rem;">
                <button class="btn btn-success" onclick="saveTemplates()">💾 Save Changes</button>
                <button class="btn btn-secondary" onclick="previewTemplate()">👁️ Preview</button>
                <button class="btn btn-danger" onclick="deleteTemplate()" id="delete-btn" style="display: none;">🗑️ Delete</button>
            </div>
        </div>
    </div>

    <!-- Toast notification -->
    <div id="toast" class="toast"></div>

    <script>
        let emailTemplates = {};
        let selectedTemplate = null;
        let hasUnsavedChanges = false;

        // Check authentication
        async function checkAuth() {
            try {
                // Fetch the authorized email from the server
                const response = await fetch('/api/config');
                if (!response.ok) {
                    throw new Error('Failed to fetch configuration');
                }

                const config = await response.json();

                // Get allowed emails from config
                let allowedEmails = [];
                if (config.allowedEmails && Array.isArray(config.allowedEmails)) {
                    allowedEmails = config.allowedEmails;
                } else if (config.authorizedEmail) {
                    allowedEmails = config.authorizedEmail.split(',').map(email => email.trim());
                }

                const auth = JSON.parse(localStorage.getItem('snownavi_auth') || '{}');
                const authContainer = document.getElementById('auth-container');
                const userInfoContainer = document.getElementById('user-info');

                // Check if auth exists, is not expired, and user is authorized
                const isAuthorized = allowedEmails.includes(auth.email);
                if (isAuthorized && auth.expiresAt && auth.expiresAt > Date.now()) {
                    // User is authenticated
                    authContainer.style.display = 'none';

                    // Display user info
                    userInfoContainer.innerHTML = `
                        <img src="${auth.picture}" alt="Profile">
                        <span>${auth.name}</span>
                        <button class="logout-btn" onclick="logout()">Logout</button>
                    `;

                    // Initialize the page
                    await init();
                } else {
                    // User is not authenticated, show auth container
                    authContainer.style.display = 'flex';
                    userInfoContainer.innerHTML = '';
                }
            } catch (error) {
                console.error('Error checking authentication:', error);
                document.getElementById('auth-container').style.display = 'flex';
                document.querySelector('.auth-message p').textContent = 'Error loading configuration. Please try again later.';
            }
        }

        // Logout function
        function logout() {
            localStorage.removeItem('snownavi_auth');
            window.location.href = 'login.html';
        }

        // Initialize the page (only called after authentication)
        async function init() {
            try {
                await loadEmailTemplates();
                renderTemplateList();
            } catch (error) {
                console.error('Error initializing page:', error);
                showError('Failed to load email templates. Please try again later.');
            }
        }

        // Load email templates from server
        async function loadEmailTemplates() {
            try {
                const response = await fetch('/api/email-templates');
                if (!response.ok) {
                    // If file doesn't exist, create default templates
                    emailTemplates = createDefaultTemplates();
                    await saveTemplates();
                } else {
                    emailTemplates = await response.json();
                }
                console.log('Loaded email templates:', emailTemplates);
            } catch (error) {
                console.error('Error loading email templates:', error);
                emailTemplates = createDefaultTemplates();
            }
        }

        // Create default email templates
        function createDefaultTemplates() {
            return {
                feedback_notification: {
                    id: 'feedback_notification',
                    name: 'Feedback Notification / 反馈通知',
                    description: 'Automatic bilingual email sent to students when feedback is completed / 反馈完成时自动发送给学生的双语邮件',
                    type: 'feedback',
                    subject: '🎿 SnowNavi Course Feedback / 课程反馈 - {{courseName}}',
                    htmlContent: `
                        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                            <div style="background: linear-gradient(135deg, #E53512 0%, #9ED4E7 100%); color: white; padding: 20px; text-align: center;">
                                <h1 style="margin: 0;">🎿 SnowNavi</h1>
                                <p style="margin: 10px 0 0 0;">Course Feedback Report / 滑雪课程反馈报告</p>
                            </div>
                            <div style="padding: 20px; background: white;">
                                <p>Dear {{studentName}} / 亲爱的 {{studentName}}，</p>
                                <p>Hello! Your skiing course feedback has been completed. Please check the details below.</p>
                                <p>您好！您的滑雪课程反馈已经完成，请查看以下详细信息：</p>

                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
                                    <h3 style="color: #E53512; margin-top: 0;">Course Information / 课程信息</h3>
                                    <p><strong>Course Name / 课程名称：</strong>{{courseName}}</p>
                                    <p><strong>Instructor / 教练：</strong>{{instructorName}}</p>
                                    <p><strong>Date / 日期：</strong>{{courseDate}}</p>
                                </div>

                                <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;">
                                    <h3 style="color: #28a745; margin-top: 0;">Learning Progress / 学习进度</h3>
                                    {{feedbackContent}}
                                </div>

                                <div style="text-align: center; margin: 30px 0;">
                                    <a href="{{feedbackUrl}}" style="background: #E53512; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                                        View Detailed Feedback / 查看详细反馈
                                    </a>
                                </div>

                                <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">

                                <div style="text-align: center; color: #666; font-size: 14px;">
                                    <p>Happy skiing! / 祝您滑雪愉快！🎿</p>
                                    <p><strong>SnowNavi Team / SnowNavi 团队</strong></p>
                                    <p style="font-size: 12px; margin-top: 20px;">
                                        This email is automatically sent by SnowNavi system, please do not reply directly.<br>
                                        此邮件由 SnowNavi 系统自动发送，请勿直接回复。
                                    </p>
                                </div>
                            </div>
                        </div>
                    `,
                    triggers: {
                        onFeedbackComplete: true,
                        onFeedbackUpdate: false,
                        manualOnly: false
                    },
                    active: true,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            };
        }

        // Render template list
        function renderTemplateList() {
            const templateList = document.getElementById('template-list');

            if (Object.keys(emailTemplates).length === 0) {
                templateList.innerHTML = `
                    <h3>📋 Available Email Templates</h3>
                    <div style="text-align: center; color: var(--text-gray); padding: 2rem;">
                        No email templates found. Create your first template!
                    </div>
                `;
                return;
            }

            let html = '<h3>📋 Available Email Templates</h3>';

            Object.entries(emailTemplates).forEach(([templateId, template]) => {
                const statusBadge = template.active ?
                    '<span class="template-badge" style="background: #d4edda; color: #155724;">Active</span>' :
                    '<span class="template-badge" style="background: #f8d7da; color: #721c24;">Inactive</span>';

                html += `
                    <div class="template-item ${selectedTemplate === templateId ? 'active' : ''}"
                         onclick="selectTemplate('${templateId}')" data-template-id="${templateId}">
                        <h4>${template.name}</h4>
                        <p style="font-size: 0.9rem; color: var(--text-gray); margin-bottom: 0.5rem;">
                            ${template.description.substring(0, 80)}${template.description.length > 80 ? '...' : ''}
                        </p>
                        <div class="template-meta">
                            <div>
                                <span class="template-badge badge-${template.type}">${template.type}</span>
                                ${statusBadge}
                            </div>
                            <div style="font-size: 0.8rem;">
                                ${Object.keys(template.triggers || {}).filter(key => template.triggers[key]).length} triggers
                            </div>
                        </div>
                    </div>
                `;
            });

            templateList.innerHTML = html;
        }

        // Select a template
        function selectTemplate(templateId) {
            selectedTemplate = templateId;
            renderTemplateList();
            renderTemplateForm(templateId);
            document.getElementById('delete-btn').style.display = 'inline-block';
        }

        // Render template form
        function renderTemplateForm(templateId) {
            const template = emailTemplates[templateId];
            const formContainer = document.getElementById('form-container');

            if (!template) {
                formContainer.innerHTML = '<div class="no-selection">Template not found</div>';
                return;
            }

            const html = `
                <div class="form-section">
                    <h3>📧 Email Template Information</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="template-id">Template ID</label>
                            <input type="text" id="template-id" value="${template.id}" readonly style="background: #f5f5f5;">
                        </div>
                        <div class="form-group">
                            <label for="template-type">Template Type</label>
                            <select id="template-type">
                                <option value="feedback" ${template.type === 'feedback' ? 'selected' : ''}>Feedback Notification</option>
                                <option value="welcome" ${template.type === 'welcome' ? 'selected' : ''}>Welcome Email</option>
                                <option value="reminder" ${template.type === 'reminder' ? 'selected' : ''}>Reminder Email</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="template-name">Template Name</label>
                            <input type="text" id="template-name" value="${template.name}" placeholder="Enter template name">
                        </div>
                        <div class="form-group">
                            <label for="template-active">Status</label>
                            <select id="template-active">
                                <option value="true" ${template.active ? 'selected' : ''}>Active</option>
                                <option value="false" ${!template.active ? 'selected' : ''}>Inactive</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="template-description">Description</label>
                        <textarea id="template-description" placeholder="Enter template description">${template.description}</textarea>
                    </div>
                </div>

                <div class="form-section">
                    <h3>📝 Email Content</h3>
                    <div class="form-group">
                        <label for="template-subject">Email Subject</label>
                        <input type="text" id="template-subject" value="${template.subject}" placeholder="Enter email subject (use {{variables}} for dynamic content)">
                        <small style="color: var(--text-gray); margin-top: 0.5rem; display: block;">
                            Available variables: {{studentName}}, {{courseName}}, {{instructorName}}, {{courseDate}}, {{feedbackContent}}
                        </small>
                    </div>
                    <div class="form-group">
                        <label for="template-content">HTML Content</label>
                        <textarea id="template-content" style="min-height: 300px;" placeholder="Enter HTML email content (use {{variables}} for dynamic content)">${template.htmlContent}</textarea>
                        <small style="color: var(--text-gray); margin-top: 0.5rem; display: block;">
                            Use HTML for formatting. Available variables: {{studentName}}, {{courseName}}, {{instructorName}}, {{courseDate}}, {{feedbackContent}}, {{feedbackUrl}}
                        </small>
                    </div>
                </div>

                <div class="form-section">
                    <h3>⚡ Trigger Conditions</h3>
                    <div class="trigger-section">
                        <h4>When should this email be sent automatically?</h4>
                        <div class="checkbox-group">
                            <input type="checkbox" id="trigger-feedback-complete" ${template.triggers?.onFeedbackComplete ? 'checked' : ''}>
                            <label for="trigger-feedback-complete">When feedback is completed by coach</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="trigger-feedback-update" ${template.triggers?.onFeedbackUpdate ? 'checked' : ''}>
                            <label for="trigger-feedback-update">When feedback is updated by coach</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="trigger-manual-only" ${template.triggers?.manualOnly ? 'checked' : ''}>
                            <label for="trigger-manual-only">Manual sending only (no automatic triggers)</label>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3>👁️ Email Preview</h3>
                    <div class="preview-container">
                        <h4>Preview with sample data:</h4>
                        <div class="preview-email" id="email-preview">
                            <!-- Preview will be generated here -->
                        </div>
                        <button type="button" class="btn btn-secondary" onclick="updatePreview()" style="margin-top: 1rem;">🔄 Update Preview</button>
                    </div>
                </div>
            `;

            formContainer.innerHTML = html;
            updatePreview();
        }

        // Update email preview
        function updatePreview() {
            if (!selectedTemplate) return;

            const subject = document.getElementById('template-subject')?.value || '';
            const content = document.getElementById('template-content')?.value || '';

            // Sample data for preview
            const sampleData = {
                studentName: 'Zhang San / 张三',
                courseName: 'Beginner Snowboarding Course / 单板滑雪初级课程',
                instructorName: 'Coach Li / 李教练',
                courseDate: 'Jan 15, 2024 / 2024年1月15日',
                feedbackContent: `
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #9ED4E7;">
                        <h3 style="color: #E53512; margin-top: 0; margin-bottom: 15px;">🎯 Your Learning Journey / 您的学习历程</h3>
                        <div style="margin-bottom: 15px; padding: 12px; background: white; border-radius: 6px; border-left: 3px solid #E53512;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <strong style="color: #E53512;">Session 2: Beginner Snowboarding / 第2次课程: 单板滑雪初级 (Current / 当前课程)</strong>
                                <span style="font-size: 0.9em; color: #717171;">Jan 15, 2024</span>
                            </div>
                            <div style="margin-bottom: 8px;">
                                <span style="background: #e3f2fd; color: #1976d2; padding: 2px 8px; border-radius: 12px; font-size: 0.85em; margin-right: 8px;">
                                    🆕 3 new skills / 新技能
                                </span>
                                <span style="background: #f3e5f5; color: #7b1fa2; padding: 2px 8px; border-radius: 12px; font-size: 0.85em;">
                                    📊 8 total skills / 累计技能
                                </span>
                            </div>
                            <div style="margin-top: 8px;">
                                <strong style="color: #E53512; font-size: 0.9em;">New skills mastered / 新掌握技能:</strong>
                                <ul style="margin: 5px 0 0 20px; padding: 0;">
                                    <li style='margin-bottom: 3px; font-size: 0.85em;'>双脚后刃推坡 (Both Feet Heel Side Push)</li>
                                    <li style='margin-bottom: 3px; font-size: 0.85em;'>双脚前刃推坡 (Both Feet Toe Side Push)</li>
                                    <li style='margin-bottom: 3px; font-size: 0.85em;'>双脚后刃落叶飘 (Both Feet Heel Side Falling Leaf)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                `,
                feedbackUrl: 'http://127.0.0.1:8899/student_feedback.html?memberId=123'
            };

            // Replace variables in content
            let previewContent = content;
            Object.keys(sampleData).forEach(key => {
                const regex = new RegExp(`{{${key}}}`, 'g');
                previewContent = previewContent.replace(regex, sampleData[key]);
            });

            // Handle conditional blocks (simple implementation)
            previewContent = previewContent.replace(/{{#if feedbackUrl}}([\s\S]*?){{\/if}}/g, '$1');

            const previewContainer = document.getElementById('email-preview');
            if (previewContainer) {
                previewContainer.innerHTML = `
                    <div style="border-bottom: 1px solid #ddd; padding-bottom: 1rem; margin-bottom: 1rem;">
                        <strong>Subject:</strong> ${subject.replace(/{{(\w+)}}/g, (match, key) => sampleData[key] || match)}
                    </div>
                    ${previewContent}
                `;
            }
        }

        // Add new template
        function addNewTemplate() {
            const templateId = `email_template_${Date.now()}`;
            const newTemplate = {
                id: templateId,
                name: 'New Email Template',
                description: 'New email template description',
                type: 'feedback',
                subject: '🎿 SnowNavi - {{courseName}}',
                htmlContent: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <div style="background: linear-gradient(135deg, #E53512 0%, #9ED4E7 100%); color: white; padding: 20px; text-align: center;">
                            <h1 style="margin: 0;">🎿 SnowNavi</h1>
                        </div>
                        <div style="padding: 20px; background: white;">
                            <p>Dear {{studentName}},</p>
                            <p>Your content here...</p>
                            <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">
                            <div style="text-align: center; color: #666; font-size: 14px;">
                                <p><strong>SnowNavi Team</strong></p>
                            </div>
                        </div>
                    </div>
                `,
                triggers: {
                    onFeedbackComplete: false,
                    onFeedbackUpdate: false,
                    manualOnly: true
                },
                active: false,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            emailTemplates[templateId] = newTemplate;
            selectedTemplate = templateId;
            renderTemplateList();
            renderTemplateForm(templateId);
            document.getElementById('delete-btn').style.display = 'inline-block';
        }

        // Save templates
        async function saveTemplates() {
            if (!selectedTemplate) {
                alert('Please select a template first.');
                return;
            }

            // Show saving status
            showToast('💾 Saving email templates...', 'saving');

            // Update template with form data
            updateTemplateFromForm();

            try {
                const response = await fetch('/api/email-templates', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(emailTemplates)
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to save email templates');
                }

                const result = await response.json();
                console.log('Email templates saved successfully:', result);

                // Mark as saved
                markAsSaved();

                // Reload templates to get updated data
                await loadEmailTemplates();
                renderTemplateList();

                // Re-select the template if it was updated
                if (selectedTemplate) {
                    selectTemplate(selectedTemplate);
                }

            } catch (error) {
                console.error('Error saving email templates:', error);
                showToast('❌ Failed to save email templates: ' + error.message, 'unsaved');
                showError('Failed to save email templates: ' + error.message);
            }
        }

        // Update template from form
        function updateTemplateFromForm() {
            if (!selectedTemplate) return;

            const template = emailTemplates[selectedTemplate];

            // Update basic info
            template.name = document.getElementById('template-name').value;
            template.description = document.getElementById('template-description').value;
            template.type = document.getElementById('template-type').value;
            template.active = document.getElementById('template-active').value === 'true';
            template.subject = document.getElementById('template-subject').value;
            template.htmlContent = document.getElementById('template-content').value;

            // Update triggers
            template.triggers = {
                onFeedbackComplete: document.getElementById('trigger-feedback-complete').checked,
                onFeedbackUpdate: document.getElementById('trigger-feedback-update').checked,
                manualOnly: document.getElementById('trigger-manual-only').checked
            };

            template.updatedAt = new Date().toISOString();
        }

        // Preview template
        function previewTemplate() {
            if (!selectedTemplate) {
                alert('Please select a template first.');
                return;
            }

            updateTemplateFromForm();
            updatePreview();

            // Scroll to preview section
            const previewSection = document.getElementById('email-preview');
            if (previewSection) {
                previewSection.scrollIntoView({ behavior: 'smooth' });
            }
        }

        // Delete template
        function deleteTemplate() {
            if (!selectedTemplate) {
                alert('Please select a template first.');
                return;
            }

            if (!confirm('Are you sure you want to delete this email template? This action cannot be undone.')) {
                return;
            }

            delete emailTemplates[selectedTemplate];
            selectedTemplate = null;
            document.getElementById('delete-btn').style.display = 'none';
            renderTemplateList();
            document.getElementById('form-container').innerHTML = '<div class="no-selection">Select an email template from the left panel or create a new one</div>';
        }

        // Show error message
        function showError(message) {
            const container = document.getElementById('form-container');
            const existingContent = container.innerHTML;
            container.innerHTML = `<div class="error">${message}</div>` + existingContent;
            setTimeout(() => {
                const errorDiv = container.querySelector('.error');
                if (errorDiv) errorDiv.remove();
            }, 5000);
        }

        // Show success message
        function showSuccess(message) {
            const container = document.getElementById('form-container');
            const existingContent = container.innerHTML;
            container.innerHTML = `<div class="success">${message}</div>` + existingContent;
            setTimeout(() => {
                const successDiv = container.querySelector('.success');
                if (successDiv) successDiv.remove();
            }, 3000);
        }

        // Toast notification functions
        function showToast(message, type = 'unsaved', duration = 0) {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.className = `toast ${type}`;

            // Show toast
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            // Auto hide for saved messages
            if (duration > 0) {
                setTimeout(() => {
                    hideToast();
                }, duration);
            }
        }

        function hideToast() {
            const toast = document.getElementById('toast');
            toast.classList.remove('show');
            hasUnsavedChanges = false;
        }

        function markAsUnsaved() {
            hasUnsavedChanges = true;
            showToast('📝 You have unsaved changes', 'unsaved');
        }

        function markAsSaved() {
            showToast('✅ Email templates saved successfully', 'saved', 5000);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', checkAuth);
    </script>
</body>
</html>
