<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Feedback Template Operations</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 Feedback Template Operations Test</h1>
    
    <div class="test-section">
        <h2>📋 Load Templates</h2>
        <button onclick="loadTemplates()">Load All Templates</button>
        <div id="load-result"></div>
    </div>
    
    <div class="test-section">
        <h2>➕ Create New Template</h2>
        <button onclick="createTestTemplate()">Create Test Template</button>
        <div id="create-result"></div>
    </div>
    
    <div class="test-section">
        <h2>✏️ Update Template</h2>
        <button onclick="updateTestTemplate()">Update Test Template</button>
        <div id="update-result"></div>
    </div>
    
    <div class="test-section">
        <h2>🗑️ Delete Template</h2>
        <button onclick="deleteTestTemplate()">Delete Test Template</button>
        <div id="delete-result"></div>
    </div>
    
    <div class="test-section">
        <h2>📊 Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="test-log" class="log"></div>
    </div>

    <script>
        let testTemplateId = `test_template_${Date.now()}`;
        
        function log(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('test-log').textContent = '';
        }
        
        function showResult(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${success ? 'success' : 'error'}">${message}</div>`;
        }
        
        async function loadTemplates() {
            log('开始加载模板...');
            try {
                const response = await fetch('/api/feedback-templates');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const templates = await response.json();
                const count = Object.keys(templates).length;
                log(`✅ 成功加载 ${count} 个模板`);
                showResult('load-result', true, `Successfully loaded ${count} templates`);
                
                // 显示模板列表
                let templateList = 'Templates:\n';
                Object.entries(templates).forEach(([id, template]) => {
                    templateList += `- ${id}: ${template.name.en}\n`;
                });
                log(templateList);
                
            } catch (error) {
                log(`❌ 加载模板失败: ${error.message}`);
                showResult('load-result', false, `Failed to load templates: ${error.message}`);
            }
        }
        
        async function createTestTemplate() {
            log(`开始创建测试模板: ${testTemplateId}`);
            
            const testTemplate = {
                id: testTemplateId,
                name: {
                    en: 'Frontend Test Template',
                    zh: '前端测试模板',
                    nl: 'Frontend Test Template'
                },
                description: {
                    en: 'Test template created from frontend',
                    zh: '从前端创建的测试模板',
                    nl: 'Test template gemaakt vanuit frontend'
                },
                targetLevel: 'beginner',
                sport: 'snowboard',
                sections: {
                    test_section: {
                        title: {
                            en: 'Test Section',
                            zh: '测试部分',
                            nl: 'Test Sectie'
                        },
                        order: 1,
                        skills: {
                            test_skill: {
                                name: {
                                    en: 'Test Skill',
                                    zh: '测试技能',
                                    nl: 'Test Vaardigheid'
                                },
                                description: {
                                    en: 'A test skill for validation',
                                    zh: '用于验证的测试技能',
                                    nl: 'Een test vaardigheid voor validatie'
                                },
                                order: 1
                            }
                        }
                    }
                },
                version: '1.0',
                createdBy: 'frontend_test',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            try {
                const response = await fetch('/api/feedback-template', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testTemplate)
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || `HTTP ${response.status}`);
                }
                
                const result = await response.json();
                log(`✅ 成功创建模板: ${testTemplateId}`);
                showResult('create-result', true, `Successfully created template: ${testTemplateId}`);
                
            } catch (error) {
                log(`❌ 创建模板失败: ${error.message}`);
                showResult('create-result', false, `Failed to create template: ${error.message}`);
            }
        }
        
        async function updateTestTemplate() {
            log(`开始更新测试模板: ${testTemplateId}`);
            
            const updatedTemplate = {
                id: testTemplateId,
                name: {
                    en: 'Updated Frontend Test Template',
                    zh: '更新的前端测试模板',
                    nl: 'Bijgewerkte Frontend Test Template'
                },
                description: {
                    en: 'Updated test template from frontend',
                    zh: '从前端更新的测试模板',
                    nl: 'Bijgewerkte test template vanuit frontend'
                },
                targetLevel: 'intermediate',
                sport: 'ski',
                sections: {},
                version: '2.0',
                createdBy: 'frontend_test',
                updatedAt: new Date().toISOString()
            };
            
            try {
                const response = await fetch(`/api/feedback-template/${testTemplateId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updatedTemplate)
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || `HTTP ${response.status}`);
                }
                
                const result = await response.json();
                log(`✅ 成功更新模板: ${testTemplateId}`);
                showResult('update-result', true, `Successfully updated template: ${testTemplateId}`);
                
            } catch (error) {
                log(`❌ 更新模板失败: ${error.message}`);
                showResult('update-result', false, `Failed to update template: ${error.message}`);
            }
        }
        
        async function deleteTestTemplate() {
            log(`开始删除测试模板: ${testTemplateId}`);
            
            try {
                const response = await fetch(`/api/feedback-template/${testTemplateId}`, {
                    method: 'DELETE'
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || `HTTP ${response.status}`);
                }
                
                log(`✅ 成功删除模板: ${testTemplateId}`);
                showResult('delete-result', true, `Successfully deleted template: ${testTemplateId}`);
                
                // 生成新的测试模板ID供下次使用
                testTemplateId = `test_template_${Date.now()}`;
                log(`新的测试模板ID: ${testTemplateId}`);
                
            } catch (error) {
                log(`❌ 删除模板失败: ${error.message}`);
                showResult('delete-result', false, `Failed to delete template: ${error.message}`);
            }
        }
        
        // 页面加载时自动加载模板
        window.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，开始测试...');
            loadTemplates();
        });
    </script>
</body>
</html>
