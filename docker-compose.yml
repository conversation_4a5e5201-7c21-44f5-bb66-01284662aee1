# 开发环境配置 - 本地使用
# 生产环境请使用 docker-compose.prod.yml
version: '3.8'

services:
  db:
    image: postgres:15
    container_name: snownavi-db
    environment:
      POSTGRES_DB: snownavi
      POSTGRES_USER: snownavi_user
      POSTGRES_PASSWORD: snownavi_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  migrate:
    build:
      context: .
      dockerfile: database/docker/Dockerfile
    container_name: snownavi_migrate
    depends_on:
      - db
    environment:
      DATABASE_URL: ****************************************************/snownavi
      PYTHONPATH: /app
    volumes:
      - .:/app
    working_dir: /app
    command: python database/migrate.py

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: snownavi-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - db
    restart: unless-stopped

volumes:
  postgres_data:
  pgadmin_data:
