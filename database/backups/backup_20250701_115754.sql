--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13 (Debian 15.13-1.pgdg120+1)
-- Dumped by pg_dump version 15.13 (Debian 15.13-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: snownavi_user
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_updated_at_column() OWNER TO snownavi_user;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: activities; Type: TABLE; Schema: public; Owner: snownavi_user
--

CREATE TABLE public.activities (
    id character varying(20) NOT NULL,
    name_en character varying(255),
    name_zh character varying(255),
    name_nl character varying(255),
    type character varying(50),
    date date,
    location_en character varying(255),
    location_zh character varying(255),
    location_nl character varying(255),
    description_en text,
    description_zh text,
    description_nl text,
    status character varying(50) DEFAULT 'active'::character varying,
    feedback_template_id character varying(50),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.activities OWNER TO snownavi_user;

--
-- Name: checkins; Type: TABLE; Schema: public; Owner: snownavi_user
--

CREATE TABLE public.checkins (
    id character varying(20) NOT NULL,
    activity_id character varying(20),
    member_id character varying(20),
    member_name character varying(255),
    member_type character varying(20),
    checkin_time timestamp with time zone,
    checkin_by character varying(255),
    checkin_by_email character varying(255),
    notes text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT checkins_member_type_check CHECK (((member_type)::text = ANY ((ARRAY['member'::character varying, 'coach'::character varying])::text[])))
);


ALTER TABLE public.checkins OWNER TO snownavi_user;

--
-- Name: courses; Type: TABLE; Schema: public; Owner: snownavi_user
--

CREATE TABLE public.courses (
    id character varying(50) NOT NULL,
    course_data jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.courses OWNER TO snownavi_user;

--
-- Name: email_templates; Type: TABLE; Schema: public; Owner: snownavi_user
--

CREATE TABLE public.email_templates (
    id character varying(50) NOT NULL,
    name character varying(255),
    description text,
    type character varying(50),
    subject character varying(500),
    html_content text,
    triggers jsonb,
    active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.email_templates OWNER TO snownavi_user;

--
-- Name: feedback_templates; Type: TABLE; Schema: public; Owner: snownavi_user
--

CREATE TABLE public.feedback_templates (
    id character varying(50) NOT NULL,
    name_en character varying(255),
    name_zh character varying(255),
    name_nl character varying(255),
    description_en text,
    description_zh text,
    description_nl text,
    target_level character varying(50),
    sport character varying(50),
    sections jsonb,
    version character varying(10) DEFAULT '1.0'::character varying,
    created_by character varying(255),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.feedback_templates OWNER TO snownavi_user;

--
-- Name: feedbacks; Type: TABLE; Schema: public; Owner: snownavi_user
--

CREATE TABLE public.feedbacks (
    id character varying(20) NOT NULL,
    checkin_id character varying(20),
    activity_id character varying(20),
    member_id character varying(20),
    member_name character varying(255),
    overall_feedback text,
    skill_assessment jsonb,
    feedback text,
    created_by character varying(255),
    created_by_email character varying(255),
    student_rating jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.feedbacks OWNER TO snownavi_user;

--
-- Name: members; Type: TABLE; Schema: public; Owner: snownavi_user
--

CREATE TABLE public.members (
    id character varying(20) NOT NULL,
    name character varying(255) NOT NULL,
    email character varying(255),
    is_active boolean DEFAULT true,
    validity_period_start date,
    validity_period_end date,
    password character varying(255),
    gender character varying(10),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.members OWNER TO snownavi_user;

--
-- Name: navigation_items; Type: TABLE; Schema: public; Owner: snownavi_user
--

CREATE TABLE public.navigation_items (
    id character varying(50) NOT NULL,
    url character varying(500),
    translation_en character varying(255),
    translation_zh character varying(255),
    translation_nl character varying(255),
    visible boolean DEFAULT true,
    order_index integer,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.navigation_items OWNER TO snownavi_user;

--
-- Name: student_ratings; Type: TABLE; Schema: public; Owner: snownavi_user
--

CREATE TABLE public.student_ratings (
    id character varying(50) NOT NULL,
    feedback_id character varying(20),
    student_email character varying(255),
    student_name character varying(255),
    member_id character varying(20),
    activity_id character varying(20),
    instructor_name character varying(255),
    rating integer,
    comment text,
    submitted_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT student_ratings_rating_check CHECK (((rating >= 1) AND (rating <= 5)))
);


ALTER TABLE public.student_ratings OWNER TO snownavi_user;

--
-- Data for Name: activities; Type: TABLE DATA; Schema: public; Owner: snownavi_user
--

COPY public.activities (id, name_en, name_zh, name_nl, type, date, location_en, location_zh, location_nl, description_en, description_zh, description_nl, status, feedback_template_id, created_at, updated_at) FROM stdin;
ACT20250001	Beginner Group Lesson	新手团课	Beginner Group Lesson	group	2025-06-14	SnowWorld Zoetermeer	SnowWorld Zoetermeer	SnowWorld Zoetermeer	Beginner Group Lesson session 1/2	新手团课 课时1/2	Beginner Group Lesson session 1/2	completed	snowboard_beginner	2025-01-15 10:00:00+00	2025-06-14 22:41:14.161+00
ACT20250002	Beginner Group Lesson	新手团课	Beginner Group Lesson	group	2025-06-21	SnowWorld Zoetermeer	SnowWorld Zoetermeer	SnowWorld Zoetermeer	Beginner Group Lesson session 2/2	新手团课 课时2/2	Beginner Group Lesson session 2/2	active	snowboard_beginner	2025-06-06 11:06:07.215+00	2025-06-08 11:06:37.598+00
ACT20250003	SnowWorld Express	滑雪直通车	SnowWorld Express	group	2025-06-14	SnowWorld Zoetermeer	SnowWorld Zoetermeer	SnowWorld Zoetermeer	SnowWorld Express	滑雪直通车	SnowWorld Express	completed	\N	2025-06-06 11:07:19.681+00	2025-06-14 22:40:09.859+00
ACT20250004	SnowWorld Express	滑雪直通车	SnowWorld Express	group	2025-06-21	SnowWorld Zoetermeer	SnowWorld Zoetermeer	SnowWorld Zoetermeer	SnowWorld Express	滑雪直通车	SnowWorld Express	active	\N	2025-06-06 11:08:20.516+00	2025-06-06 11:08:40.151+00
ACT20250005	Snowboard Beginner Lesson	单板新手课	Snowboard Beginner Lesson	group	2023-02-25	SnowWorld Landgraaf	SnowWorld Landgraaf	SnowWorld Landgraaf	Snowboard Beginner Lesson	Snowboard Beginner Lesson	Snowboard Beginner Lesson	completed	snowboard_beginner	2025-06-09 19:29:08.036+00	2025-06-09 19:34:44.519+00
ACT20250006	Beginner Group Lesson Example	新手团课样例	Beginner Group Lesson Example	group	2025-06-02	SnowWorld Zoetermeer	SnowWorld Zoetermeer	SnowWorld Zoetermeer	Beginner Group Lesson Example	Beginner Group Lesson Example	Beginner Group Lesson Example	completed	snowboard_beginner	2025-06-09 20:19:31.328+00	2025-06-09 23:45:40.212+00
ACT20250007	Beginner Group Lesson Example	新手团课样例	Beginner Group Lesson Example	group	2025-06-09	SnowWorld Zoetermeer	SnowWorld Zoetermeer	SnowWorld Zoetermeer	Beginner Group Lesson Example	Beginner Group Lesson Example	Beginner Group Lesson Example	completed	snowboard_beginner	2025-06-09 20:20:50.074+00	2025-06-09 23:45:30.537+00
\.


--
-- Data for Name: checkins; Type: TABLE DATA; Schema: public; Owner: snownavi_user
--

COPY public.checkins (id, activity_id, member_id, member_name, member_type, checkin_time, checkin_by, checkin_by_email, notes, created_at) FROM stdin;
CHK20250001	ACT20250005	SN20230001	Bihua Zhang	member	2025-06-09 19:33:20.329+00	okboy	<EMAIL>	Checked in via QR code scan as member by okboy	2025-06-09 19:33:20.329+00
CHK20250002	ACT20250006	SN99999999	SnowNavi Member	member	2025-06-09 20:22:54.337+00	okboy	<EMAIL>	Checked in via QR code scan as member by okboy	2025-06-09 20:22:54.337+00
CHK20250003	ACT20250007	SN99999999	SnowNavi Member	member	2025-06-09 20:23:22.576+00	okboy	<EMAIL>	Checked in via QR code scan as member by okboy	2025-06-09 20:23:22.576+00
CHK20250004	ACT20250003	SN20250009	Zhigang Zheng	member	2025-06-14 08:01:37.279+00	okboy	<EMAIL>	Checked in via QR code scan as member by okboy	2025-06-14 08:01:37.279+00
CHK20250005	ACT20250001	SN20250003	Kuanyi Li	member	2025-06-14 08:04:22.07+00	okboy	<EMAIL>	Checked in via QR code scan as member by okboy	2025-06-14 08:04:22.07+00
CHK20250006	ACT20250001	SN20250008	Yingjie Ge	member	2025-06-14 08:05:09.556+00	okboy	<EMAIL>	Checked in via QR code scan as member by okboy	2025-06-14 08:05:09.556+00
CHK20250007	ACT20250001	SN20250004	Mingqi Du	member	2025-06-14 08:05:22.754+00	okboy	<EMAIL>	Checked in via QR code scan as member by okboy	2025-06-14 08:05:22.754+00
CHK20250008	ACT20250001	SN20250007	Peiyi He	member	2025-06-14 08:06:44.839+00	okboy	<EMAIL>	Checked in via QR code scan as member by okboy	2025-06-14 08:06:44.839+00
CHK20250009	ACT20250001	SN20250001	Yunlong Cheng	member	2025-06-14 08:08:22.55+00	okboy	<EMAIL>	Checked in via QR code scan as member by okboy	2025-06-14 08:08:22.55+00
CHK20250010	ACT20250001	SN20250002	Han Zhang	member	2025-06-14 08:08:32.827+00	okboy	<EMAIL>	Checked in via QR code scan as member by okboy	2025-06-14 08:08:32.827+00
CHK20250011	ACT20250003	SN20250005	Wenqian Wang	member	2025-06-14 08:12:00.981+00	okboy	<EMAIL>	Checked in via QR code scan as member by okboy	2025-06-14 08:12:00.981+00
\.


--
-- Data for Name: courses; Type: TABLE DATA; Schema: public; Owner: snownavi_user
--

COPY public.courses (id, course_data, created_at, updated_at) FROM stdin;
basi	{"en": {"title": "BASI Instructor Course", "subcourses": [{"pdf": "", "desc": "A 4-day BASI instructor training course provided by our partner SnowboardCoach\\n\\n【24/25 Snow Season】BASI Course Schedule\\n\\n⚠️Tentative. More courses will be added based on demand.\\n\\nSnowboard Level 1 Weekday Course €1099\\n2025.09.08 - 2025.09.11 English Course Examiner: Ash/Lyndon\\n2025.10.20 - 2025.10.23 English Course Examiner: Rob\\n\\nSnowboard Level 1 Weekend Course €1249\\n2025.09.06 - 09.07 & 2025.09.13 - 09.14 English Course Examiner: Ash/Lyndon\\n2025.09.20 - 09.21 & 2025.09.27 - 09.28 English Course Examiner: Rob\\n2025.10.18 - 10.19 & 2025.10.25 - 10.26 English Course Examiner: Rob\\n\\nSki Level 1 Weekday Course €1099\\n2025.10.20 - 2025.10.23 English Course Examiner: To be confirmed\\n\\nSki Level 1 Weekend Course €1249\\n2025.10.18 - 10.19 & 2025.10.25 - 10.26 English Course Examiner: To be confirmed\\n\\nIncluded in the price:\\n1. First year BASI membership\\n2. BASI training course\\n3. Instructor certification and assessment\\n4. SnowWorld facility fees\\n5. Full-day lift tickets during assessment\\n6. Equipment rental (helmet, ski wear, skis/snowboard, boots) during assessment\\n\\nNot included:\\nTransportation, accommodation, meals, insurance, etc.\\n\\nModules required to obtain the full Level 1 certification:\\n\\t1.\\t4-day Level 1 Instructor Course\\n\\t2.\\t35 hours of ski school shadowing (shadowing opportunities are provided through our partner)\\n\\t3.\\tFirst Aid Training (2-day course, additional sessions available via SnowNavi)\\n\\t4.\\tCPD Child Protection Course (online course via BASI)\\n\\nSee BASI official site: https://www.basi.org.uk/BASI/Courses/BASI/Courses/Disciplines.aspx?hkey=26544a24-cde1-4939-a16b-76f39a4a4626", "time": "Sep/Oct 2025", "image": "/uploads/images/20250414050351_2149bd74.png", "price": "From €1099", "title": "BASI Snowboard/Ski Level 1", "location": "SnowWorld Landgraaf/Zoetermeer, Netherlands"}, {"pdf": "", "desc": "A 4-day BASI instructor training course provided by our partner SnowboardCoach\\n\\n【24/25 Snow Season】BASI Course Schedule\\n\\n⚠️Tentative. More courses will be added based on demand.\\n\\nSnowboard UK Level 2 Weekday Course €1099\\n2025.09.22 - 2025.09.25 English Course Examiner: Rob\\n\\nSki UK Level 2 Weekday Course €1099\\n2025.09.22 - 2025.09.25 English Course Examiner: To be confirmed\\n\\nIncluded in the price:\\n1. First year BASI membership\\n2. BASI training course\\n3. Instructor certification and assessment\\n4. SnowWorld facility fees\\n5. Full-day lift tickets during assessment\\n6. Equipment rental (helmet, ski wear, skis/snowboard, boots) during assessment\\n\\nNot included:\\nTransportation, accommodation, meals, insurance, etc.\\n\\nModules required to obtain the full Level 2 certification:\\n\\t1.\\t4-day UK Level 2 Instructor Course\\n\\t2.\\t4-day Outdoor Level 2 Instructor Course (available during the snow season through our partner SnowboardCoach)\\n\\t3.\\tAn additional 35 hours of ski school shadowing (shadowing opportunities are provided by our partner SnowboardCoach at their ski school in Andorra)\\n\\nSee BASI official site: https://www.basi.org.uk/BASI/Courses/BASI/Courses/Disciplines.aspx?hkey=26544a24-cde1-4939-a16b-76f39a4a4626", "time": "Sep/Oct 2025", "image": "/uploads/images/20250414050351_2149bd74.png", "price": "From €1099", "title": "BASI Snowboard/Ski UK Level 2", "location": "SnowWorld Landgraaf/Zoetermeer, Netherlands"}, {"pdf": "", "desc": "A 1-day riding improvement training course provided by our partner SnowboardCoach\\n\\nThis course is based on the content of the BASI Level 1 & 2 exams and combines both riding and freestyle park skills in a single day of training.\\n\\nThe course includes approximately 6 hours of instruction.\\n\\nRiding training content includes:\\n\\t1.\\tSkidded turns in both regular and switch stance\\n\\t2.\\tShort skidded turns in regular stance\\n\\t3.\\tFull edge carving in regular stance\\n\\t4.\\tBasic carving in switch stance\\n\\nFreestyle park training content includes:\\n\\t1.\\tOllie/Nollie/Nose & Tail press\\n\\t2.\\tEight different 180s\\n\\t3.\\tBox 50-50 and variations\\n\\t4.\\tStraight airs and variations on 3–5m kickers\\n\\n⚠️The above content is for reference only. Actual content will be adjusted based on the students’ level and slope conditions on the day of training.\\n\\nGroup size: 6–9 people, with a minimum of 6 participants to start.\\n\\nCourse fees (per person):\\n6 people: €229\\n7 people: €209\\n8 people: €189\\n9 people: €169\\n\\nThe course fee includes:\\n\\t1.\\tTraining fee\\n\\t2.\\tFull-day lift pass during the training\\n\\t3.\\tEquipment rental during the training (helmet / snowboard outfit / snowboard / boots)\\n\\nNot included:\\nTransportation, accommodation, meals, insurance, etc.", "time": "Sep/Oct 2025", "image": "/uploads/images/20250415204852_b67741a4.jpeg", "price": "From €169", "title": "BASI Snowboard training (riding + park freestyle)", "location": "SnowWorld Landgraaf/Zoetermeer, Netherlands"}], "description": "The BASI (British Association of Snowsport Instructors) system is one of the most influential ski instructor training systems in Europe and around the world, emphasizing comprehensive development in teaching methodology, technical skills, and safety awareness.\\n\\nIf you're considering a career as a ski or snowboard instructor, BASI is undoubtedly a top-tier pathway to the international stage. Certified BASI instructors can be found working across the globe, in destinations such as Austria, France, Switzerland, Andorra, China, Japan, and Australia.\\n\\nThanks to BASI’s global recognition and the all-season advantage of indoor snowdomes in the Netherlands, SnowNavi is partnering for the first time with official BASI partner SnowboardCoach to bring top BASI Level 4 examiners right to your doorstep. Even in the off-season, you can complete high-quality instructor training at SnowWorld in the Netherlands to build a solid foundation for your future.\\n\\nCourse schedule for the 2024–2025 season (we welcome candidates with certifications from other ski associations, such as CASI, CSIA, NZSIA, SBINZ, etc.):\\n▪ Level 1 Instructor Course: 4 days, small groups of 8 participants;\\n▪ UK Level 2 Instructor Course: 4-day indoor course, small groups of 8 participants. This course is the off-season component of the new “4+4” format. After completing it, you'll only need to finish a 4-day mountain course during the snow season to receive full BASI Level 2 certification.\\n\\nIn addition to instructor courses, we’ll also offer a variety of riding improvement and park freestyle training sessions led by experienced BASI Level 4 trainers, giving you more opportunities to develop your skills on snow!"}, "nl": {"title": "BASI Instructeurs cursus", "subcourses": [{"pdf": "", "desc": "Een 4-daagse BASI-instructeurstrainingscursus aangeboden door onze partner SnowboardCoach.\\n\\n【24/25 Sneeuwseizoen】BASI Cursusplanning\\n\\n⚠️Voorlopig. Meer cursussen worden toegevoegd op basis van de vraag.\\n\\nSnowboard Niveau 1 Weekdagcursus €1099\\n2025.09.08 - 2025.09.11 Engelstalige Cursus Examinator: Ash/Lyndon\\n2025.10.20 - 2025.10.23 Engelstalige Cursus Examinator: Rob\\n\\nSnowboard Niveau 1 Weekendcursus €1249\\n2025.09.06 - 09.07 & 2025.09.13 - 09.14 Engelstalige Cursus Examinator: Ash/Lyndon\\n2025.09.20 - 09.21 & 2025.09.27 - 09.28 Engelstalige Cursus Examinator: Rob\\n2025.10.18 - 10.19 & 2025.10.25 - 10.26 Engelstalige Cursus Examinator: Rob\\n\\nSki Niveau 1 Weekdagcursus €1099\\n2025.10.20 - 2025.10.23 Engelstalige Cursus Examinator: Wordt bevestigd\\n\\nSki Niveau 1 Weekendcursus €1249\\n2025.10.18 - 10.19 & 2025.10.25 - 10.26 Engelstalige Cursus Examinator: Wordt bevestigd\\n\\nInbegrepen in de prijs:\\n1. Eerste jaar BASI-lidmaatschap\\n2. BASI trainingscursus\\n3. Instructeurscertificering en examen\\n4. SnowWorld faciliteitenkosten\\n5. Dagelijkse skipas tijdens het examen\\n6. Materiaalhuur tijdens het examen (helm, skikleding, ski's/snowboard, schoenen)\\n\\nNiet inbegrepen:\\nVervoer, accommodatie, maaltijden, verzekering, etc.\\n\\nModules die nodig zijn om de volledige Niveau 1-certificering te behalen:\\n\\t1.\\t4-daagse Niveau 1 Instructeurscursus\\n\\t2.\\t35 uur ski school shadowing (shadowing-mogelijkheden worden aangeboden via onze partner)\\n\\t3.\\tEHBO-training (2-daagse cursus, extra sessies beschikbaar via SnowNavi)\\n\\t4.\\tCPD-cursus Kinderbescherming (online cursus via BASI)\\n\\nMeer info: https://www.basi.org.uk/BASI/Courses/BASI/Courses/Disciplines.aspx?hkey=26544a24-cde1-4939-a16b-76f39a4a4626", "time": "sep/okt 2025", "image": "/uploads/images/20250414050351_2149bd74.png", "price": "Vanaf €1099", "title": "BASI Snowboard/Ski Niveau 1", "location": "SnowWorld Landgraaf/Zoetermeer, Nederland"}, {"pdf": "", "desc": "Een 4-daagse BASI-instructeurstrainingscursus aangeboden door onze partner SnowboardCoach.\\n\\n【24/25 Sneeuwseizoen】BASI Cursusplanning\\n\\n⚠️Voorlopig. Meer cursussen worden toegevoegd op basis van de vraag.\\n\\nSnowboard UK Niveau 2 Weekdagcursus €1099\\n2025.09.22 - 2025.09.25 Engelstalige Cursus Examinator: Rob\\n\\nSki UK Niveau 2 Weekdagcursus €1099\\n2025.09.22 - 2025.09.25 Engelstalige Cursus Examinator: Wordt bevestigd\\n\\nInbegrepen in de prijs:\\n1. Eerste jaar BASI-lidmaatschap\\n2. BASI trainingscursus\\n3. Instructeurscertificering en examen\\n4. SnowWorld faciliteitenkosten\\n5. Dagelijkse skipas tijdens het examen\\n6. Materiaalhuur tijdens het examen (helm, skikleding, ski's/snowboard, schoenen)\\n\\nNiet inbegrepen:\\nVervoer, accommodatie, maaltijden, verzekering, etc.\\n\\nModules die nodig zijn om de volledige Niveau 2-certificering te behalen:\\n\\t1.\\t4-daagse UK Level 2 Instructeurscursus\\n\\t2.\\t4-daagse Outdoor Niveau 2 Instructeurscursus (beschikbaar tijdens het sneeuwseizoen via onze partner SnowboardCoach)\\n\\t3.\\tExtra 35 uur ski school shadowing (shadowing-mogelijkheden worden aangeboden door onze partner SnowboardCoach bij hun skischool in Andorra)\\n\\nMeer info: https://www.basi.org.uk/BASI/Courses/BASI/Courses/Disciplines.aspx?hkey=26544a24-cde1-4939-a16b-76f39a4a4626", "time": "sep/okt 2025", "image": "/uploads/images/20250414050351_2149bd74.png", "price": "Vanaf €1099", "title": "BASI Snowboard/Ski UK Niveau 2", "location": "SnowWorld Landgraaf/Zoetermeer, Nederland"}, {"pdf": "", "desc": "Een eendaagse training voor snowboardvaardigheden aangeboden door onze partner SnowboardCoach\\n\\nDeze cursus is gebaseerd op de BASI Niveau 1 & 2 examens en combineert zowel pistevaardigheden als freestyle parktechnieken in één dag training.\\n\\nDe cursus omvat ongeveer 6 uur les.\\n\\nCursusinhoud pistevaardigheden:\\n\\t1.\\tSkidded bochten in zowel regular als switch\\n\\t2.\\tKorte skidded bochten in regular\\n\\t3.\\tVolledig carve in regular\\n\\t4.\\tBasis carve in switch\\n\\nCursusinhoud freestyle park:\\n\\t1.\\tOllie/Nollie/Nose & Tail press\\n\\t2.\\tAcht verschillende 180’s\\n\\t3.\\tBox 50-50 en variaties\\n\\t4.\\tStraight air en variaties op 3–5 meter schansen\\n\\n⚠️Bovenstaande inhoud is ter referentie. De werkelijke inhoud wordt aangepast op basis van het niveau van de deelnemers en de pistecondities op de dag zelf.\\n\\nAantal deelnemers: 6–9 personen, minimaal 6 deelnemers vereist.\\n\\nCursusprijs (per persoon):\\n6 personen: €229\\n7 personen: €209\\n8 personen: €189\\n9 personen: €169\\n\\nCursusprijs is inclusief:\\n\\t1.\\tLeskosten\\n\\t2.\\tDagticket voor de skilift tijdens de training\\n\\t3.\\tMateriaalhuur tijdens de training (helm / snowboardkleding / snowboard / boots)\\n\\nNiet inbegrepen:\\nVervoer, accommodatie, maaltijden, verzekering, enz.\\n", "time": "sep/okt 2025", "image": "/uploads/images/20250415204852_b67741a4.jpeg", "price": "Vanaf €169", "title": "BASI Snowboardtraining (rijden + park freestyle)", "location": "SnowWorld Landgraaf/Zoetermeer, Nederland"}], "description": "Het BASI-systeem (British Association of Snowsport Instructors) is een van de meest invloedrijke opleidingssystemen voor ski- en snowboardinstructeurs in Europa en wereldwijd. Het richt zich op een evenwichtige ontwikkeling van lesmethodiek, technische vaardigheden en veiligheidsbewustzijn.\\n\\nAls je een carrière als ski- of snowboardinstructeur overweegt, is BASI zonder twijfel een eersteklas route naar het internationale podium. Instructeurs met een BASI-certificaat zijn werkzaam in landen zoals Oostenrijk, Frankrijk, Zwitserland, Andorra, China, Japan en Australië.\\n\\nDankzij de wereldwijde erkenning van BASI én het all-season voordeel van indoor sneeuwhallen in Nederland, werkt SnowNavi nu voor het eerst samen met officiële BASI-partner SnowboardCoach om top BASI Level 4-examinatoren naar Nederland te halen. Zo kun je zelfs buiten het sneeuwseizoen hoogwaardige instructeursopleidingen volgen bij SnowWorld en een sterke basis leggen voor je toekomstige carrière.\\n\\nCursusplanning voor het seizoen 2024–2025 (kandidaten met certificaten van andere ski-organisaties zijn welkom, zoals CASI, CSIA, NZSIA, SBINZ, enz.):\\n▪ Level 1 Instructeurscursus: 4 dagen, kleine groepjes van 8 deelnemers;\\n▪ UK Level 2 Instructeurscursus: 4-daagse indoorcursus, kleine groepjes van 8 deelnemers. Dit is het off-season deel van het nieuwe “4+4” model. Na deze cursus hoef je alleen nog een 4-daagse bergcursus in het sneeuwseizoen te voltooien om de volledige BASI Niveau 2-certificering te behalen.\\n\\nNaast de instructeursopleidingen bieden we ook diverse vaardigheidstrainingen in rijden en park freestyle aan, begeleid door ervaren BASI Level 4-trainers, zodat je je sneeuwskills verder kunt uitbreiden!"}, "zh": {"title": "BASI 指导员课程", "subcourses": [{"pdf": "", "desc": "由我们的合作伙伴SnowboardCoach提供为期4天的BASI指导员培训课程\\n\\n【24/25雪季】BASI课程计划安排\\n⚠️暂定，更多课程将视需求陆续增加\\n💬请在报名信息里告知我们在Landgraaf和Zoetermeer里是否有地点的偏好以帮助我们确定最终课程地点\\n\\n单板一级周中班  €1099\\n2025.09.08 - 2025.09.11 英文班 考官：Ash/Lyndon\\n2025.10.20 - 2025.10.23 英文班 考官：Rob\\n\\n单板一级周末班  €1249\\n2025.09.06 - 09.07 & 2025.09.13 - 09.14 英文班 考官：Ash/Lyndon\\n2025.09.20 - 09.21 & 2025.09.27 - 09.28 英文班 考官：Rob\\n2025.10.18 - 10.19 & 2025.10.25 - 10.26 英文班 考官：Rob\\n\\n双板一级周中班  €1099\\n2025.10.20 - 2025.10.23 英文班 考官：待定\\n\\n双板一级周末班  €1249\\n2025.10.18 - 10.19 & 2025.10.25 - 10.26 英文班 考官：待定\\n\\n课程费用包含：\\n1.首年BASI会员年费\\n2.BASI 课程培训费用\\n3.BASI 指导员认证及考试费用\\n4.SnowWorld场地使用费\\n5.考试期间全天雪票\\n6.考试期间雪具租赁（头盔/雪服/雪板/雪鞋）\\n\\n费用不包含：\\n交通，住宿，餐饮，保险等\\n\\n获得完整一级认证所需模块：\\n1.4天一级指导员课程\\n2.35小时滑雪学校实习(SnowNavi可提供雪季安道尔滑雪学校实习) \\n3.First Aid急救培训(2天培训，SnowNavi可提供额外课程)\\n4.CPD儿童保护课程(BASI网课)\\n\\n详情可见BASI官网：https://www.basi.org.uk/BASI/Courses/BASI/Courses/Disciplines.aspx?hkey=26544a24-cde1-4939-a16b-76f39a4a4626", "time": "2025年9月/10月", "image": "/uploads/images/20250414050351_2149bd74.png", "price": "€1099起", "title": "BASI 单板/双板 Level 1", "location": "荷兰 SnowWorld Landgraaf/Zoetermeer"}, {"pdf": "", "desc": "由我们的合作伙伴SnowboardCoach提供为期4天的BASI指导员培训课程\\n\\n【24/25雪季】BASI课程计划安排\\n⚠️暂定，更多课程将视需求陆续增加\\n💬请在报名信息里告知我们在Landgraaf和Zoetermeer里是否有地点的偏好以帮助我们确定最终课程地点\\n\\n单板室内二级周中班  €1099\\n2025.09.22 - 2025.09.25 英文班 考官：Rob\\n\\n双板室内二级周中班  €1099\\n2025.09.22 - 2025.09.25 英文班 考官：待定\\n\\n课程费用包含：\\n1.首年BASI会员年费\\n2.BASI 课程培训费用\\n3.BASI 指导员认证及考试费用\\n4.SnowWorld场地使用费\\n5.考试期间全天雪票\\n6.考试期间雪具租赁（头盔/雪服/雪板/雪鞋）\\n\\n费用不包含：\\n交通，住宿，餐饮，保险等\\n\\n\\n获得完整二级认证所需模块：\\n1. 4天室内二级指导员课程\\n2. 4天室外二级指导员课程(SnowNavi合作伙伴SnowboardCoach雪季可提供该课程)\\n3. 额外35小时滑雪学校实习(SnowNavi可提供雪季安道尔滑雪学校实习)\\n\\n详情可见BASI官网：https://www.basi.org.uk/BASI/Courses/BASI/Courses/Disciplines.aspx?hkey=26544a24-cde1-4939-a16b-76f39a4a4626", "time": "2025年9月/10月", "image": "/uploads/images/20250414050351_2149bd74.png", "price": "€1099起", "title": "BASI 单板/双板 UK Level 2", "location": "荷兰 SnowWorld Landgraaf/Zoetermeer"}, {"pdf": "", "desc": "由我们的合作伙伴SnowboardCoach提供为期1天的滑行提升培训课程\\n\\n该课程以BASI一级及二级考试内容为基础，在1天的培训中囊括滑行与公园自由式双重提升。\\n\\n1天的课程大致包含6小时教学时间。\\n\\n滑行课程内容包括：\\n1. 正反脚搓雪中回转\\n2. 正脚搓雪小回转\\n3. 正脚全程走刃刻滑\\n4. 反脚基础刻滑\\n\\n公园自由式课程内容包括：\\n1. Ollie/Nollie/板头板尾平衡\\n2. 8个不同的180\\n3. Box 50-50及变种\\n4. 3-5米跳台直飞及变种\\n\\n⚠️以上教学内容供参考，实际教学内容视学员水平和雪道条件教学当天确定。\\n\\n本课程人数为6-9人，需至少6人开班。\\n\\n课程费用（每人）：\\n6人: €229\\n7人: €209\\n8人: €189\\n9人: €169\\n\\n课程费用包含：\\n1.培训费用\\n2.培训期间全天雪票\\n3.培训期间雪具租赁（头盔/雪服/雪板/雪鞋）\\n\\n费用不包含：\\n交通，住宿，餐饮，保险等\\n", "time": "2025年9月/10月", "image": "/uploads/images/20250415204852_b67741a4.jpeg", "price": "€169起", "title": "BASI单板滑行提升培训（滑行+公园自由式）", "location": "荷兰 SnowWorld Landgraaf/Zoetermeer"}], "description": "BASI（英国雪上运动协会）体系是欧洲乃至全球最具影响力的滑雪教学体系之一，强调教学方法、技术能力与安全意识的全面提升。\\n\\n如果你正考虑走上滑雪教练之路，BASI无疑是通向国际舞台的黄金通道。目前，持有BASI证书的指导员遍布全球，如奥地利、法国、瑞士、安道尔、中国、日本和澳大利亚等地。\\n\\n借助BASI在全球的高度认可，加上荷兰室内雪场的全天候优势，SnowNavi首次联合BASI官方合作伙伴 SnowboardCoach，将顶级BASI 4级考官直接带到你身边。在非雪季就能轻松在荷兰SnowWorld完成高质量的室内培训课程，为未来打下扎实基础。\\n\\n24-25雪季课程安排如下（支持跨体系报考，如CASI, CSIA, NZSIA, SBINZ等）：\\n▪ 一级指导员课程：4天，限8人小班；\\n▪ 二级指导员 UK Level 2 课程：4天室内课程，限8人小班。该课程为“4+4”模式中的非雪季部分，通过后只需在雪季再完成4天雪山课程，即可获得BASI二级认证。\\n\\n此外，我们还将带来更多由BASI 4级大咖坐镇的滑行提升与公园自由式课程，帮助你探索更多滑雪的可能性！"}}	2025-06-20 09:11:18.957574+00	2025-06-20 09:11:18.957574+00
park	{"en": {"title": "Park Freestyle Lessons", "subcourses": [{"desc": "Led by BASI Level 4 instructors to help you advance in the terrain park.\\n\\nEach course is a 2-day group session with 8 participants.\\n\\nPlease inquire for schedule.", "time": "Please inquire", "image": "/uploads/images/20250413230356_e3c72f95.jpeg", "price": "From €250", "title": "Park Session for Beginner", "location": "SnowWorld, Netherlands"}]}, "nl": {"title": "Freestyle Park lessen", "subcourses": [{"desc": "Onder leiding van een BASI niveau 4-instructeur voor gevorderde freestyle tricks.\\n\\nElke cursus duurt 2 dagen in groepsvorm, minimaal 8 deelnemers.\\n\\nNeem contact op voor beschikbare data.", "time": "Informeer voor planning", "image": "/uploads/images/20250413230356_e3c72f95.jpeg", "price": "Vanaf €250", "title": "Beginners", "location": "SnowWorld, Nederland"}]}, "zh": {"title": "公园自由式课程", "subcourses": [{"desc": "由BASI 4级教练带领小伙伴们进阶公园。\\n\\n每次课程为团课形式为期2天，8人开团。\\n\\n开团时间可咨询", "time": "请咨询", "image": "/uploads/images/20250413230356_e3c72f95.jpeg", "price": "€250起", "title": "公园入门", "location": "荷兰 SnowWorld"}]}}	2025-06-20 09:11:18.957574+00	2025-06-20 09:11:18.957574+00
private	{"en": {"title": "Private Lessons", "subcourses": [{"desc": "Customized private sessions based on your needs.\\n\\nEach session is 2 hours.\\n\\nTaught by certified BASI or CASI instructors.\\n\\nUp to 4 participants per session.\\n\\nPlease inquire for details.", "time": "On demand", "image": "/uploads/images/20250413225816_0467358d.jpeg", "price": "From €130", "title": "1-on-1 Coaching", "location": "Flexible location"}]}, "nl": {"title": "Privé lessen", "subcourses": [{"desc": "Privélessen op maat op basis van jouw behoeften.\\n\\nElke sessie duurt 2 uur.\\n\\nGegeven door gecertificeerde BASI of CASI instructeurs.\\n\\nMaximaal 4 deelnemers per sessie.\\n\\nInformeer voor details.", "time": "Op aanvraag", "image": "/uploads/images/20250413225816_0467358d.jpeg", "price": "Vanaf €130", "title": "Individuele coaching", "location": "Locatie flexibel"}]}, "zh": {"title": "私教课程", "subcourses": [{"desc": "根据个人需求定制课程。\\n\\n课程为2小时，授课教练均为BASI或CASI体系教练\\n\\n课程人数至多为4人，详细请咨询", "time": "根据需求安排", "image": "/uploads/images/20250413225816_0467358d.jpeg", "price": "€130起", "title": "一对一教学", "location": "地点灵活"}]}}	2025-06-20 09:11:18.957574+00	2025-06-20 09:11:18.957574+00
group	{"en": {"title": "Group Lessons", "subcourses": [{"desc": "Number of participants: 8–10 people\\n\\nBeginner course for complete beginners. In each session, students will be divided into 2 groups for instruction.\\n\\nThe course includes 2 lessons. Each lesson includes:\\n1. 3 hours of instruction\\n2. 0.5 hour of video analysis\\n3. Free practice\\n\\nBeginner Snowboard Course – June €249\\n2025.06.14 & 2025.06.21\\nChinese instruction\\nInstructors: okboy (BASI Level 2 / CASI Level 2), Xiao Ning (CASI Level 2)\\n\\nFee includes:\\n1. Instruction and video analysis\\n2. Full-day lift pass\\n3. Full-day snowboard equipment rental\\n\\nFee does not include:\\nTransportation, meals, insurance, etc.\\n\\nCourse Registration:\\n1. Click the registration button or manually go to the link: https://snownavi.setmore.com/classes/ffc8ac8b-87bc-4f0c-843e-73e37ba61efa to register for the course.\\n2. Go to: https://www.ing.nl/payreq/m/?trxid=GKwwuhnBINn0aZxmLscxDYTL48oefNmU to complete the payment.\\n\\n", "time": "June 2025", "image": "/uploads/images/20250609050354_b7baa22b.jpg", "price": "€249", "title": "Beginner Group Course", "location": "SnowWorld, Netherlands"}]}, "nl": {"title": "Groeps lessen", "subcourses": [{"desc": "Aantal deelnemers: 8–10 personen\\n\\nBeginnerscursus voor mensen zonder enige ervaring. Tijdens elke les worden deelnemers verdeeld in 2 groepen voor instructie.\\n\\nDe cursus bevat 2 lessen. Elke les bevat:\\n1. 3 uur les\\n2. 0.5 uur video-analyse\\n3. Vrij oefenen\\n\\nSnowboard Beginnerscursus – juni €249\\n2025.06.14 & 2025.06.21\\nLes in het Chinees\\nInstructeurs: okboy (BASI Niveau 2 / CASI Niveau 2), Xiao Ning (CASI Niveau 2)\\n\\nKosten inclusief:\\n1. Les en video-analyse\\n2. Dagpas voor de piste\\n3. Huur van snowboarduitrusting voor de hele dag\\n\\nKosten exclusief:\\nVervoer, maaltijden, verzekering, enz.\\n\\nCursusinschrijving:\\n1. Klik op de knop \\"Inschrijven\\" of ga handmatig naar de inschrijflink: https://snownavi.setmore.com/classes/ffc8ac8b-87bc-4f0c-843e-73e37ba61efa om je aan te melden voor de cursus.\\n2. Ga naar: https://www.ing.nl/payreq/m/?trxid=GKwwuhnBINn0aZxmLscxDYTL48oefNmU om de betaling te voltooien.", "time": "June 2025", "image": "/uploads/images/20250609050354_b7baa22b.jpg", "price": "€249", "title": "Beginners groepscursus", "location": "SnowWorld, Nederland"}]}, "zh": {"title": "团体课程", "subcourses": [{"desc": "课程人数8人-10人\\n\\n0基础入门课程，每次课程学员会分成2组进行教学。\\n\\n课程包含2节课，每节课包含：\\n1. 3小时教学\\n2. 0.5小时视频分析\\n3. 自由练习\\n\\n单板6月新手班 €249\\n2025.06.14 & 2025.06.21 中文教学 指导员：okboy(BASI 2级/CASI 2级)/小凝(CASI 2级)\\n\\n费用包含：\\n1. 教学及视频分析\\n2. 全天雪票\\n3. 全天雪具租赁\\n\\n费用不包含：\\n交通，餐饮，保险等\\n\\n课程报名方式：\\n1. 点击报名按钮或手动前往报名链接：https://snownavi.setmore.com/classes/ffc8ac8b-87bc-4f0c-843e-73e37ba61efa 完成课程注册\\n2. 前往：https://www.ing.nl/payreq/m/?trxid=GKwwuhnBINn0aZxmLscxDYTL48oefNmU 完成付款", "time": "2025年6月", "image": "/uploads/images/20250609050354_b7baa22b.jpg", "price": "€249", "title": "0基础入门团课", "location": "荷兰 SnowWorld"}]}}	2025-06-20 09:11:18.957574+00	2025-06-20 09:11:18.957574+00
riding_week	{"en": {"title": "Happy Shredding", "subcourses": [{"pdf": "", "desc": "Group Skiing/Snowboarding at SnowWorld Zoetermeer!\\n\\nGreat deal for beginners with day ski pass with rental equipments!\\n\\nPrice includes:\\n1. Ski pass for whole day\\n2. Equipment rental for whole day\\n\\nNot included:\\ntransport, meals, insurance, etc.\\n\\nEvent Registration:\\n1. Click the registration button or manually go to the link: https://snownavi.setmore.com/classes/cad7389e-680b-441c-a2c4-10869a49c787 to register for the course.\\n2. Go to: https://www.ing.nl/payreq/m/?trxid=vPMGXB9hNA9tEQO9qEy3hnrBTKQ29uwx to complete the payment.", "time": "Every Saturday", "image": "/uploads/images/20250609050354_b7baa22b.jpg", "price": "€50", "title": "SnowWorld Express - 30% off normal price", "location": "SnowWorld Zoetermeer"}], "description": "Enjoy riding with no limits!"}, "nl": {"title": "Happy Shredding", "subcourses": [{"desc": "Group Skiing/Snowboarding at SnowWorld Zoetermeer!\\n\\nGreat deal for beginners with day ski pass with rental equipments!\\n\\nPrice includes:\\n1. Ski pass for whole day\\n2. Equipment rental for whole day\\n\\nNot included:\\ntransport, meals, insurance, etc.\\n\\nEvent Registration:\\n1. Click the registration button or manually go to the link: https://snownavi.setmore.com/classes/cad7389e-680b-441c-a2c4-10869a49c787 to register for the course.\\n2. Go to: https://www.ing.nl/payreq/m/?trxid=vPMGXB9hNA9tEQO9qEy3hnrBTKQ29uwx to complete the payment.", "time": "Every Saturday", "image": "/uploads/images/20250609050354_b7baa22b.jpg", "price": "€50", "title": "SnowWorld Express - 30% off normal price", "location": "SnowWorld Zoetermeer"}], "description": "Enjoy riding with no limits!"}, "zh": {"title": "滑雪活动", "subcourses": [{"desc": "周周7折！每周六一起和SnowNavi去滑雪！\\n\\n地点：SnowWorld Zoetermeer\\n\\n费用：50欧\\n费用包含：\\n1. SnowWorld 全天雪票 原价53.5欧\\n2. 全天雪具租赁（雪板/雪鞋/头盔）原价17.5欧\\n\\n费用不包含：\\n教学/交通/餐饮/保险等\\n\\n滑雪直通车每周最小成团人数10人！\\n如未报满则延期一周（或退款）\\n\\n活动报名方式:\\n1. 点击报名按钮或前往链接: https://snownavi.setmore.com/classes/cad7389e-680b-441c-a2c4-10869a49c787 \\n2. 前往: https://www.ing.nl/payreq/m/?trxid=vPMGXB9hNA9tEQO9qEy3hnrBTKQ29uwx 完成付款", "time": "每周六", "image": "/uploads/images/20250609050354_b7baa22b.jpg", "price": "€50", "title": "SnowWorld滑雪直通车 - 30%折扣", "location": "SnowWorld Zoetermeer"}, {"pdf": "", "desc": "SnowNavi一年一度的传统开板活动！\\n\\n我们会定好豪华公寓，畅滑全山！\\n\\n2024年我们的活动吸引了20个小伙伴，大家一起在全球最大的跨国雪场太阳门（法国/瑞士）度过了难忘的一周！\\n\\n今年我们提前开放预定，所有感兴趣的小伙伴都可以提前报名！\\n\\n此次预定将提前收取定金€100，最终费用在活动详情出来后确认，届时支付尾款完成最终报名！\\n", "time": "12月20日 - 12月27日", "image": "/uploads/images/20250413224234_601af1fe.jpg", "price": "€100", "title": "圣诞开板畅滑周 - 盲盒预定", "location": "某个大雪场"}], "description": "在这里没有教学，畅滑是唯一的目的！"}}	2025-06-20 09:11:18.957574+00	2025-06-20 09:11:18.957574+00
\.


--
-- Data for Name: email_templates; Type: TABLE DATA; Schema: public; Owner: snownavi_user
--

COPY public.email_templates (id, name, description, type, subject, html_content, triggers, active, created_at, updated_at) FROM stdin;
feedback_notification	Feedback Notification	Automatic email sent to students when feedback is completed	feedback	🎿 SnowNavi Course Feedback - {{courseName}}	<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">\n    <div style="background: linear-gradient(135deg, #E53512 0%, #9ED4E7 100%); color: white; padding: 20px; text-align: center;">\n        <h1 style="margin: 0;">🎿 SnowNavi</h1>\n        <p style="margin: 10px 0 0 0;">Course Feedback Report / 滑雪课程反馈报告</p>\n    </div>\n    <div style="padding: 20px; background: white;">\n        <!-- English Section -->\n        <div style="margin-bottom: 40px;">\n            <p>Dear {{studentName}},</p>\n            <p>Hello! Your course feedback has been completed. Please review the following details:</p>\n\n            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">\n                <h3 style="color: #E53512; margin-top: 0;">Course Information</h3>\n                <p><strong>Course Name:</strong> {{courseName}}</p>\n                <p><strong>Date:</strong> {{courseDate}}</p>\n            </div>\n\n            <div style="text-align: center; margin: 30px 0;">\n                <a href="{{feedbackUrl}}" style="background: #E53512; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">View Detailed Feedback</a>\n            </div>\n        </div>\n\n        <hr style="margin: 40px 0; border: none; border-top: 2px solid #ddd;">\n\n        <!-- Chinese Section -->\n        <div style="margin-bottom: 40px;">\n            <p>亲爱的 {{studentName}}，</p>\n            <p>您好！您的滑雪课程反馈已经完成，请查看以下详细信息：</p>\n\n            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">\n                <h3 style="color: #E53512; margin-top: 0;">课程信息</h3>\n                <p><strong>课程名称：</strong>{{courseName}}</p>\n                <p><strong>日期：</strong>{{courseDate}}</p>\n            </div>\n\n            <div style="text-align: center; margin: 30px 0;">\n                <a href="{{feedbackUrl}}" style="background: #E53512; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">查看详细反馈</a>\n            </div>\n        </div>\n\n        <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">\n\n        <div style="text-align: center; color: #666; font-size: 14px;">\n            <p>Happy shredding! / 祝您滑雪愉快！🎿</p>\n            <p><strong>SnowNavi Team / SnowNavi 团队</strong></p>\n            <p>📩 <EMAIL> 🌐 https://snownavi.ski</p>\n            <p style="font-size: 12px; margin-top: 20px;">This email is automatically sent by SnowNavi system, please do not reply directly. / 此邮件由 SnowNavi 系统自动发送，请勿直接回复。</p>\n        </div>\n    </div>\n</div>	{"manualOnly": false, "onFeedbackUpdate": true, "onFeedbackComplete": true}	t	2025-06-20 09:11:19.11504+00	2025-06-20 09:11:19.11504+00
\.


--
-- Data for Name: feedback_templates; Type: TABLE DATA; Schema: public; Owner: snownavi_user
--

COPY public.feedback_templates (id, name_en, name_zh, name_nl, description_en, description_zh, description_nl, target_level, sport, sections, version, created_by, created_at, updated_at) FROM stdin;
snowboard_beginner	Snowboard Beginner Template	单板初学者模板	Snowboard Beginner Template	Comprehensive skill assessment for snowboard beginners covering basic equipment introduction to first turns	单板初学者综合技能评估，涵盖基础装备介绍到初次转弯	Uitgebreide vaardigheidsbeoordeling voor snowboard beginners van basisuitrusting tot eerste bochten	beginner	snowboard	{"flow": {"order": 5, "title": {"en": "Flow", "nl": "Flow", "zh": "流畅性"}, "skills": {"scrub-360": {"name": {"en": "Skidded 360", "nl": "Skidded 360", "zh": "搓雪360"}, "order": 4, "description": {"en": "360-degree rotation while maintaining control", "nl": "360-graden rotatie terwijl controle behouden blijft", "zh": "保持控制的360度旋转"}}, "edge-change-traverse": {"name": {"en": "Edge Change with Slope Traverse", "nl": "Kantwissel met Helling Traverse", "zh": "换刃后增加横穿雪道"}, "order": 1, "description": {"en": "Adding traverse movement after edge changes", "nl": "Traverse beweging toevoegen na kantwisselingen", "zh": "换刃后增加横穿雪道的动作"}}, "continuous-edge-change": {"name": {"en": "Continuous Edge Changes", "nl": "Continue Kantwisselingen", "zh": "连续换刃"}, "order": 3, "description": {"en": "Smooth continuous transitions between edges", "nl": "Soepele continue overgangen tussen kanten", "zh": "流畅的连续换刃动作"}}, "traverse-body-movement": {"name": {"en": "Traverse with Body Movement", "nl": "Traverse met Lichaamsbeweging", "zh": "横穿雪道加入身体起伏"}, "order": 2, "description": {"en": "Adding body movement and rhythm during traverse", "nl": "Lichaamsbeweging en ritme toevoegen tijdens traverse", "zh": "横穿雪道时加入身体起伏和节奏"}}}}, "basic": {"order": 1, "title": {"en": "Basic Knowledge", "nl": "Basiskennis", "zh": "基础知识"}, "skills": {"equipment-intro": {"name": {"en": "Equipment Introduction", "nl": "Uitrusting Introductie", "zh": "滑雪装备介绍"}, "order": 1, "description": {"en": "Understanding snowboard equipment, bindings, and safety gear", "nl": "Begrip van snowboard uitrusting, bindingen en veiligheidsuitrusting", "zh": "了解单板装备、固定器和安全装备"}}, "single-foot-familiarity": {"name": {"en": "Single Foot Board Familiarity", "nl": "Eenvoets Board Vertrouwdheid", "zh": "单脚熟悉雪板"}, "order": 2, "description": {"en": "Getting comfortable with the snowboard using one foot", "nl": "Vertrouwd raken met het snowboard met één voet", "zh": "用单脚熟悉雪板的感觉"}}}}, "control": {"order": 3, "title": {"en": "Control", "nl": "Controle", "zh": "控制"}, "skills": {"static-gas-pedal": {"name": {"en": "Static Gas Pedal Practice", "nl": "Statische Gaspedaal Oefening", "zh": "静态踩油门练习"}, "order": 1, "description": {"en": "Learning how to control snowboard sideways while stationary", "nl": "Gewichtsverdeling leren terwijl stilstaand", "zh": "静态状态下学习重心分配"}}, "both-toe-side-push": {"name": {"en": "Both Feet Toe Side Slipping", "nl": "Beide Voeten Teenkant Duwen", "zh": "双脚前刃推坡"}, "order": 5, "description": {"en": "Both Feet Toe Side Slipping", "nl": "Bergop duwen met teenkant met beide voeten vastgemaakt", "zh": "双脚固定状态下用前刃推坡上行"}}, "both-heel-side-push": {"name": {"en": "Both Feet Heel Side Slipping", "nl": "Beide Voeten Hielkant Duwen", "zh": "双脚后刃推坡"}, "order": 4, "description": {"en": "Both Feet Heel Side Slipping", "nl": "Bergop duwen met hielkant met beide voeten vastgemaakt", "zh": "双脚固定状态下用后刃推坡上行"}}, "single-toe-side-push": {"name": {"en": "Single Foot Toe Side Slipping", "nl": "Eenvoets Teenkant Duwen", "zh": "单脚前刃推坡"}, "order": 3, "description": {"en": "Single Foot Toe Side Slipping", "nl": "Bergop duwen met teenkant met één voet", "zh": "用单脚前刃推坡上行"}}, "both-toe-falling-leaf": {"name": {"en": "Both Feet Toe Side Falling Leaf", "nl": "Beide Voeten Teenkant Vallend Blad", "zh": "双脚前刃落叶飘"}, "order": 7, "description": {"en": "Side-to-side movement on toe edge resembling falling leaf", "nl": "Zijwaartse beweging op teenkant zoals vallend blad", "zh": "双脚前刃左右摆动如落叶飘"}}, "single-heel-side-push": {"name": {"en": "Single Foot Heel Side Slipping", "nl": "Eenvoets Hielkant Duwen", "zh": "单脚后刃推坡"}, "order": 2, "description": {"en": "Single Foot Heel Side Slipping", "nl": "Bergop duwen met hielkant met één voet", "zh": "用单脚后刃推坡上行"}}, "both-heel-falling-leaf": {"name": {"en": "Both Feet Heel Side Falling Leaf", "nl": "Beide Voeten Hielkant Vallend Blad", "zh": "双脚后刃落叶飘"}, "order": 6, "description": {"en": "Side-to-side movement on heel edge resembling falling leaf", "nl": "Zijwaartse beweging op hielkant zoals vallend blad", "zh": "双脚后刃左右摆动如落叶飘"}}, "both-toe-power-falling-leaf": {"name": {"en": "Both Feet Toe Side Power Falling Leaf", "nl": "Beide Voeten Teenkant Krachtig Vallend Blad", "zh": "双脚前刃强力落叶飘"}, "order": 9, "description": {"en": "More aggressive side-to-side movement with more rotation on toe edge", "nl": "Meer agressieve zijwaartse beweging op teenkant", "zh": "双脚前刃更有力的左右摆动"}}, "both-heel-power-falling-leaf": {"name": {"en": "Both Feet Heel Side Power Falling Leaf", "nl": "Beide Voeten Hielkant Krachtig Vallend Blad", "zh": "双脚后刃强力落叶飘"}, "order": 8, "description": {"en": "More aggressive side-to-side movement with more rotation on heel edge", "nl": "Meer agressieve zijwaartse beweging op hielkant", "zh": "双脚后刃更有力的左右摆动"}}}}, "sliding": {"order": 2, "title": {"en": "Sliding", "nl": "Glijden", "zh": "滑动"}, "skills": {"single-foot-j-turn": {"name": {"en": "Single Foot J-Turn", "nl": "Eenvoets J-Bocht", "zh": "单脚J弯"}, "order": 5, "description": {"en": "Making J-shaped turns with one foot strapped in", "nl": "J-vormige bochten maken met één voet vastgemaakt", "zh": "单脚固定状态下做J形转弯"}}, "single-foot-sliding": {"name": {"en": "Single Foot Skateboard Sliding", "nl": "Eenvoets Skateboard Glijden", "zh": "单脚滑板式滑动"}, "order": 1, "description": {"en": "Moving forward using skateboard-style pushing with one foot", "nl": "Vooruit bewegen met skateboard-stijl duwen met één voet", "zh": "用单脚滑板式推进前进"}}, "single-foot-climbing": {"name": {"en": "Single Foot Climbing", "nl": "Eenvoets Klimmen", "zh": "单脚爬坡"}, "order": 2, "description": {"en": "Climbing uphill with one foot strapped in", "nl": "Bergop klimmen met één voet vastgemaakt", "zh": "单脚固定状态下爬坡"}}, "single-foot-straight": {"name": {"en": "Single Foot Straight Descent", "nl": "Eenvoets Rechte Afdaling", "zh": "单脚直滑降"}, "order": 3, "description": {"en": "Sliding straight down with one foot strapped in", "nl": "Recht naar beneden glijden met één voet vastgemaakt", "zh": "单脚固定状态下直线滑降"}}, "single-foot-heel-brake": {"name": {"en": "Single Foot Heel Braking", "nl": "Eenvoets Hiel Remmen", "zh": "单脚脚后跟减速"}, "order": 4, "description": {"en": "Learning to brake using heel edge with one foot", "nl": "Leren remmen met hielkant met één voet", "zh": "学习用单脚后刃减速"}}}}, "turning": {"order": 4, "title": {"en": "Turning", "nl": "Bochten", "zh": "转弯"}, "skills": {"j-turns": {"name": {"en": "J-Turns", "nl": "J-Bochten", "zh": "J弯"}, "order": 3, "description": {"en": "Making J-shaped turns with both feet strapped in", "nl": "J-vormige bochten maken met beide voeten vastgemaakt", "zh": "双脚固定状态下做J形转弯"}}, "step-turns": {"name": {"en": "Step Turns", "nl": "Stap Bochten", "zh": "阶梯转弯"}, "order": 2, "description": {"en": "Pointing the snowboard down hill bit by bit", "nl": "Bochten maken door achtervoet te stappen", "zh": "通过后脚踩踏做转弯"}}, "beginner-turns": {"name": {"en": "Beginner Turns", "nl": "Beginner Bochten", "zh": "新手转弯"}, "order": 5, "description": {"en": "First complete turns linking heel and toe edges. A full stop is needed after completing one edge change", "nl": "Eerste complete bochten die hiel- en teenkanten verbinden", "zh": "第一次完整的连接后刃和前刃的转弯"}}, "static-rotation": {"name": {"en": "Static Rotation Practice", "nl": "Statische Rotatie Oefening", "zh": "静态旋转练习"}, "order": 1, "description": {"en": "Learning rotation movement while stationary", "nl": "Rotatiebeweging leren terwijl stilstaand", "zh": "静态状态下学习旋转动作"}}, "walking-edge-change": {"name": {"en": "Walking Edge Change Simulation", "nl": "Lopende Kant Wissel Simulatie", "zh": "走步模拟换刃"}, "order": 4, "description": {"en": "Simulating edge changes through walking motion", "nl": "Kantenwisselingen simuleren door loopbeweging", "zh": "通过走步动作模拟换刃"}}}}}	1.0	system	2025-01-27 10:00:00+00	2025-06-08 09:11:20.314+00
snowboard_intermediate	Snowboard Intermediate Template	单板中级模板	Snowboard Intermediate Template	Skill assessment for intermediate snowboarders focusing on advanced turns and freestyle basics	单板中级学员技能评估，专注于高级转弯和自由式基础	Vaardigheidsbeoordeling voor gevorderde snowboarders gericht op geavanceerde bochten en freestyle basics	intermediate	snowboard	{"advanced_turning": {"order": 1, "title": {"en": "Advanced Turning", "nl": "Geavanceerde Bochten", "zh": "高级转弯"}, "skills": {"carved-turns": {"name": {"en": "Carved Turns", "nl": "Gesneden Bochten", "zh": "刻滑转弯"}, "order": 1, "description": {"en": "Clean carved turns without skidding", "nl": "Schone gesneden bochten zonder slippen", "zh": "无滑移的干净刻滑转弯"}}, "dynamic-turns": {"name": {"en": "Dynamic Turns", "nl": "Dynamische Bochten", "zh": "动态转弯"}, "order": 2, "description": {"en": "Turns with varying radius and speed", "nl": "Bochten met variërende radius en snelheid", "zh": "变化半径和速度的转弯"}}}}, "freestyle_basics": {"order": 2, "title": {"en": "Freestyle Basics", "nl": "Freestyle Basics", "zh": "自由式基础"}, "skills": {"180s": {"name": {"en": "180° Spins", "nl": "180° Spins", "zh": "180度旋转"}, "order": 2, "description": {"en": "Half rotation spins in both directions", "nl": "Halve rotatie spins in beide richtingen", "zh": "双向半圈旋转"}}, "ollies": {"name": {"en": "Ollies", "nl": "Ollies", "zh": "豚跳"}, "order": 1, "description": {"en": "Basic jumping technique using board flex", "nl": "Basis sprongtechniek met board flex", "zh": "利用板子弹性的基础跳跃技术"}}}}}	1.0	system	2025-01-27 10:00:00+00	2025-01-27 10:00:00+00
ski_beginner	Ski Beginner Template	双板初学者模板	Ski Beginner Template	Comprehensive skill assessment for ski beginners covering basic equipment to parallel turns	双板初学者综合技能评估，涵盖基础装备到平行转弯	Uitgebreide vaardigheidsbeoordeling voor ski beginners van basisuitrusting tot parallelle bochten	beginner	ski	{"basic": {"order": 1, "title": {"en": "Basic Knowledge", "nl": "Basiskennis", "zh": "基础知识"}, "skills": {"walking-skis": {"name": {"en": "Walking with Skis", "nl": "Lopen met Ski's", "zh": "穿雪板行走"}, "order": 2, "description": {"en": "Basic movement and balance with skis on flat terrain", "nl": "Basis beweging en balans met ski's op vlak terrein", "zh": "平地穿雪板的基本移动和平衡"}}, "equipment-intro": {"name": {"en": "Ski Equipment Introduction", "nl": "Ski Uitrusting Introductie", "zh": "滑雪装备介绍"}, "order": 1, "description": {"en": "Understanding ski equipment, bindings, and safety gear", "nl": "Begrip van ski uitrusting, bindingen en veiligheidsuitrusting", "zh": "了解滑雪装备、固定器和安全装备"}}}}, "gliding": {"order": 2, "title": {"en": "Gliding", "nl": "Glijden", "zh": "滑行"}, "skills": {"snowplow": {"name": {"en": "Snowplow", "nl": "Sneeuwploeg", "zh": "犁式滑行"}, "order": 2, "description": {"en": "Basic braking and speed control technique", "nl": "Basis rem- en snelheidscontroletechniek", "zh": "基础制动和速度控制技术"}}, "straight-gliding": {"name": {"en": "Straight Gliding", "nl": "Recht Glijden", "zh": "直线滑行"}, "order": 1, "description": {"en": "Gliding straight down gentle slopes", "nl": "Recht naar beneden glijden op zachte hellingen", "zh": "在缓坡上直线滑行"}}}}, "turning": {"order": 3, "title": {"en": "Turning", "nl": "Bochten", "zh": "转弯"}, "skills": {"parallel-turns": {"name": {"en": "Parallel Turns", "nl": "Parallelle Bochten", "zh": "平行转弯"}, "order": 2, "description": {"en": "Advanced turns with skis parallel", "nl": "Geavanceerde bochten met ski's parallel", "zh": "雪板平行的高级转弯"}}, "snowplow-turns": {"name": {"en": "Snowplow Turns", "nl": "Sneeuwploeg Bochten", "zh": "犁式转弯"}, "order": 1, "description": {"en": "Basic turns using snowplow technique", "nl": "Basis bochten met sneeuwploeg techniek", "zh": "使用犁式技术的基础转弯"}}}}}	1.0	system	2025-01-27 10:00:00+00	2025-01-27 10:00:00+00
\.


--
-- Data for Name: feedbacks; Type: TABLE DATA; Schema: public; Owner: snownavi_user
--

COPY public.feedbacks (id, checkin_id, activity_id, member_id, member_name, overall_feedback, skill_assessment, feedback, created_by, created_by_email, student_rating, created_at, updated_at) FROM stdin;
FB20250001	CHK20250001	ACT20250005	SN20230001	Bihua Zhang	大哥专属，接下来教练证该安排了	{"lastUpdated": "2025-06-10T19:54:07.322Z", "completedSkills": ["equipment-intro", "single-foot-familiarity", "single-foot-sliding", "single-foot-climbing", "single-foot-straight", "single-foot-heel-brake", "single-foot-j-turn", "static-gas-pedal", "single-heel-side-push", "single-toe-side-push", "both-heel-side-push", "both-toe-side-push", "both-heel-falling-leaf", "both-toe-falling-leaf", "both-heel-power-falling-leaf", "both-toe-power-falling-leaf", "static-rotation", "step-turns", "j-turns", "walking-edge-change", "beginner-turns", "edge-change-traverse", "traverse-body-movement", "continuous-edge-change", "scrub-360"], "sectionFeedbacks": {}}	大哥专属，接下来教练证该安排了	okboy	<EMAIL>	\N	2025-06-09 19:34:03.938+00	2025-06-10 19:54:07.322+00
FB20250002	CHK20250002	ACT20250006	SN99999999	SnowNavi Member		{"lastUpdated": "2025-06-09T20:24:32.158Z", "completedSkills": ["equipment-intro", "single-foot-familiarity"], "sectionFeedbacks": {"basic": "继续保持"}}		okboy	<EMAIL>	\N	2025-06-09 20:24:32.158+00	2025-06-09 20:24:32.158+00
FB20250003	CHK20250003	ACT20250007	SN99999999	SnowNavi Member		{"lastUpdated": "2025-06-09T20:26:22.542Z", "completedSkills": ["single-foot-sliding", "single-foot-climbing", "single-foot-straight", "single-foot-heel-brake", "single-foot-j-turn"], "sectionFeedbacks": {"sliding": "非常好👍"}}		okboy	<EMAIL>	{"rating": 5, "comment": "超棒！", "memberId": "SN99999999", "studentName": "SnowNavi Member", "submittedAt": "2025-06-09T20:27:51.251Z", "studentEmail": "<EMAIL>"}	2025-06-09 20:26:22.543+00	2025-06-09 20:26:22.543+00
FB20250004	CHK20250010	ACT20250001	SN20250002	Han Zhang	领悟超级快、细心胆大！ \n拖牵部分下节课会多练习，前刃推坡部分可以先自学，参考demo（老王or黄嘉蓝）并和小白讨论，下周咱们提前练习：）	{"lastUpdated": "2025-06-14T20:59:10.208Z", "completedSkills": ["equipment-intro", "single-foot-familiarity", "single-foot-sliding", "single-foot-climbing", "single-foot-straight", "single-foot-heel-brake", "static-gas-pedal", "single-heel-side-push", "both-heel-side-push"], "sectionFeedbacks": {"basic": "很好，已经了解雪板知识+单脚控制雪板。\\n可以尝试后续单脚提升练习：蹬2-3步以后，雪板保持速度的同时把后脚放在板上随雪板一起前进。", "control": "继续保持较缓的坡后刃的推坡的感觉（上半身+视线），后续可以稍微陡的坡继续练习，大概是一种即将坐到椅子上的感觉， 可以看demo上根据坡度调整腿的弯曲程度。", "sliding": "今天已经可以从一定高度上稳定直滑降，也可以做到不摔跤，且可以主动用脚后跟刹停。\\n后续：可以慢慢尝试从更高的地方进行直滑降并主动刹停，以适应从吊椅上下缆车。\\n注意：上半身保持住避免过度弯腰、视线看向行进方向。"}}	领悟超级快、细心胆大！ \n拖牵部分下节课会多练习，前刃推坡部分可以先自学，参考demo（老王or黄嘉蓝）并和小白讨论，下周咱们提前练习：）	Ran Xiao	<EMAIL>	\N	2025-06-14 20:59:10.208+00	2025-06-14 20:59:10.208+00
FB20250005	CHK20250007	ACT20250001	SN20250004	Mingqi Du	今天有很多次都是第一个做练习且完成的效果都非常好！下次记得穿好护具会胆子更大哦～～～不要担心拖牵，下周我们会攻克它✌️	{"lastUpdated": "2025-06-14T21:12:18.755Z", "completedSkills": ["equipment-intro", "single-foot-familiarity", "single-foot-sliding", "single-foot-climbing", "single-foot-straight", "single-foot-heel-brake", "static-gas-pedal", "single-heel-side-push", "both-heel-side-push", "both-toe-side-push"], "sectionFeedbacks": {"basic": "很好，已经了解雪板知识+单脚控制雪板。 可以尝试后续单脚提升练习：蹬2-3步以后，雪板保持速度的同时把后脚放在板上随雪板一起前进。", "control": "非常好，后刃推坡堪称完美；前刃推坡保持最后自我练习的几趟状态！重心再降低一点就更好了。", "sliding": "完成情况很优秀！！！💯\\n后续：尝试从更高的地方进行直滑降并主动刹停，以适应从吊椅上下缆车。 \\n小tips：视线看向行进方向，我俩不该有对视哦🐶"}}	今天有很多次都是第一个做练习且完成的效果都非常好！下次记得穿好护具会胆子更大哦～～～不要担心拖牵，下周我们会攻克它✌️	Ran Xiao	<EMAIL>	\N	2025-06-14 21:12:18.755+00	2025-06-14 21:12:18.755+00
FB20250006	CHK20250009	ACT20250001	SN20250001	Yunlong Cheng	今天唯一一个上高级道的“小白”，学会了拖牵前空翻 - 一项我们都不会的技能！保持在高级道最后几趟前后刃推坡的感觉，下周换刃没问题的！p.s 新鞋可以多穿穿适应一下，在家也可以静态模拟一下前后刃的动作	{"lastUpdated": "2025-06-14T21:21:05.098Z", "completedSkills": ["equipment-intro", "single-foot-familiarity", "single-foot-sliding", "single-foot-climbing", "single-foot-straight", "single-foot-heel-brake", "static-gas-pedal", "single-heel-side-push", "both-heel-side-push", "both-toe-side-push"], "sectionFeedbacks": {"basic": "Goed！", "control": "后刃推坡没问题，平时深蹲的动作和后刃推坡很适合。前刃需要把膝盖压出去，小腿顶住鞋舌（和深蹲不同）", "sliding": "爬得最高滑得最远"}}	今天唯一一个上高级道的“小白”，学会了拖牵前空翻 - 一项我们都不会的技能！保持在高级道最后几趟前后刃推坡的感觉，下周换刃没问题的！p.s 新鞋可以多穿穿适应一下，在家也可以静态模拟一下前后刃的动作	okboy	<EMAIL>	\N	2025-06-14 21:21:05.099+00	2025-06-14 21:21:05.099+00
FB20250007	CHK20250008	ACT20250001	SN20250007	Peiyi He	自学成才小能手！学得非常快，动作也很标准！下一节课很期待你在高级道的表现	{"lastUpdated": "2025-06-14T21:30:31.535Z", "completedSkills": ["equipment-intro", "single-foot-familiarity", "single-foot-sliding", "single-foot-climbing", "single-foot-straight", "single-foot-heel-brake", "static-gas-pedal", "single-heel-side-push", "both-heel-side-push", "both-toe-side-push"], "sectionFeedbacks": {"basic": "Goed！", "control": "前刃推坡动作很标准，也能稳定控制速度！后刃推坡刹停的时候可以稍稍再多坐下去一点就更完美了", "sliding": "可以尝试后续单脚提升练习：蹬2-3步以后，雪板保持速度的同时把后脚放在板上随雪板一起前进。"}}	自学成才小能手！学得非常快，动作也很标准！下一节课很期待你在高级道的表现	okboy	<EMAIL>	\N	2025-06-14 21:30:31.535+00	2025-06-14 21:30:31.535+00
FB20250008	CHK20250005	ACT20250001	SN20250003	Kuanyi Li	学得非常快！练习也很稳！下一次课上可以更大胆地做动作，没问题的！	{"lastUpdated": "2025-06-14T21:36:53.306Z", "completedSkills": ["equipment-intro", "single-foot-familiarity", "single-foot-sliding", "single-foot-climbing", "single-foot-straight", "single-foot-heel-brake", "static-gas-pedal", "single-heel-side-push", "both-heel-side-push", "both-toe-side-push"], "sectionFeedbacks": {"basic": "Goed！", "control": "后刃动作已经很标准了！前刃可以将视线保持看向山上，同时胯可以多往前刃方向送出去，小腿找到顶住鞋舌的感觉", "sliding": "可以尝试后续单脚提升练习：1. 蹬2-3步以后，雪板保持速度的同时把后脚放在板上随雪板一起前进 2. 可以尝试更高的地方进行直滑降并主动减速刹停"}}	学得非常快！练习也很稳！下一次课上可以更大胆地做动作，没问题的！	okboy	<EMAIL>	{"rating": 5, "comment": "這次上課真的非常開心，兩位教練都非常有耐心，整堂課的情緒價值給得特別足，讓我第一次滑雪就覺得很好玩，也有運動到的感覺。課程內容很充實，但教練們還是能照顧到每位學員的狀況，這點特別讓人安心。而且課後的影片也非常有幫助，因為當下其實沒意識到自己動作那麼大，看影片之後才更清楚自己的問題，非常感謝！下次會嘗試更大膽的做動作的💪", "memberId": "SN20250003", "studentName": "Kuanyi Li", "submittedAt": "2025-06-15T15:38:08.077Z", "studentEmail": "<EMAIL>"}	2025-06-14 21:36:53.308+00	2025-06-14 21:36:53.308+00
FB20250009	CHK20250006	ACT20250001	SN20250008	Yingjie Ge	在高级道已经可以稳定换刃，后续可以慢慢加强刃感，同时慢慢将换刃时机提前可以让滑行更稳定	{"lastUpdated": "2025-06-14T21:42:00.156Z", "completedSkills": ["beginner-turns", "edge-change-traverse", "continuous-edge-change"], "sectionFeedbacks": {"flow": "保持最后2趟滑行的感觉，后续可以尝试心中数数的方式让弯型更对称", "turning": "后换前，保持把胯送出去；前换后更有耐心，让雪板慢慢旋转使得弯型更圆润"}}	在高级道已经可以稳定换刃，后续可以慢慢加强刃感，同时慢慢将换刃时机提前可以让滑行更稳定	okboy	<EMAIL>	\N	2025-06-14 21:42:00.156+00	2025-06-14 21:42:00.156+00
\.


--
-- Data for Name: members; Type: TABLE DATA; Schema: public; Owner: snownavi_user
--

COPY public.members (id, name, email, is_active, validity_period_start, validity_period_end, password, gender, created_at, updated_at) FROM stdin;
SN20210001	Yujie Wang	<EMAIL>	t	2025-01-01	2025-12-31	a8ee4742	m	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20210002	Ran Xiao	<EMAIL>	t	2025-01-01	2025-12-31	8b433740	f	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20210003	Yizhou Ye	<EMAIL>	t	2025-01-01	2025-12-31	a8279247	m	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20230001	Bihua Zhang	<EMAIL>	t	2025-05-23	2026-05-23	85087a0a	m	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20230002	Jiawen Wen	\N	t	2025-05-23	2026-05-23	8bc894bd	m	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20230003	Chunjiang Lin	<EMAIL>	t	2025-05-23	2026-05-23	036d93fe	m	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20230004	Zhaoheng He	\N	t	2025-05-23	2026-05-23	2553b12b	m	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20230005	Jiahe Hou	\N	t	2025-05-23	2026-05-23	33c3f930	m	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20240001	Xiaomeng GAO	<EMAIL>	t	2025-05-23	2026-05-23	11041851	f	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20230006	Yuting He	<EMAIL>	t	2025-05-23	2026-05-23	d3531647	f	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20250001	Yunlong Cheng	<EMAIL>	t	2025-05-23	2026-05-23	0ecd35a6	m	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20250002	Han Zhang	<EMAIL>	t	2025-05-23	2026-05-23	728f8196	f	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20250003	Kuanyi Li	<EMAIL>	t	2025-05-24	2026-05-24	41d74294	f	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20250004	Mingqi Du	<EMAIL>	t	2025-05-24	2026-05-24	ead3e5f5	f	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20250005	Wenqian Wang	<EMAIL>	f	\N	\N	7843c00e	m	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20250006	Hanwei Zhang	<EMAIL>	t	2025-05-29	2026-05-29	48f85b3a	f	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20250007	Peiyi He	<EMAIL>	t	2025-06-06	2026-06-06	4de5d25d	f	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20250008	Yingjie Ge	<EMAIL>	t	2025-06-06	2026-06-06	\N	m	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN20250009	Zhigang Zheng	<EMAIL>	f	\N	\N	\N	m	2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
SN99999999	SnowNavi Member	<EMAIL>	f	\N	\N	28c6153b		2025-06-20 09:11:18.57487+00	2025-06-20 09:11:18.57487+00
\.


--
-- Data for Name: navigation_items; Type: TABLE DATA; Schema: public; Owner: snownavi_user
--

COPY public.navigation_items (id, url, translation_en, translation_zh, translation_nl, visible, order_index, created_at, updated_at) FROM stdin;
courses	index.html#courses	Courses	课程	Cursussen	t	1	2025-06-20 09:11:19.031588+00	2025-06-20 09:11:19.031588+00
map	index.html#map	Interactive Ski Map	在线滑雪地图	Interactieve Skikaart	t	2	2025-06-20 09:11:19.031588+00	2025-06-20 09:11:19.031588+00
story	index.html#story	Our Story	我们的故事	Ons Verhaal	t	3	2025-06-20 09:11:19.031588+00	2025-06-20 09:11:19.031588+00
partners	index.html#partners	Partners	合作伙伴	Partners	t	4	2025-06-20 09:11:19.031588+00	2025-06-20 09:11:19.031588+00
contact	index.html#contact	Contact	联系我们	Contact	t	5	2025-06-20 09:11:19.031588+00	2025-06-20 09:11:19.031588+00
membership	membership.html	Membership	会员资格	Lidmaatschap	t	6	2025-06-20 09:11:19.031588+00	2025-06-20 09:11:19.031588+00
member	member.html	Member Card	会员卡	Lidkaart	f	7	2025-06-20 09:11:19.031588+00	2025-06-20 09:11:19.031588+00
\.


--
-- Data for Name: student_ratings; Type: TABLE DATA; Schema: public; Owner: snownavi_user
--

COPY public.student_ratings (id, feedback_id, student_email, student_name, member_id, activity_id, instructor_name, rating, comment, submitted_at) FROM stdin;
rating_1749500871954	FB20250003	<EMAIL>	SnowNavi Member	SN99999999	ACT20250007	okboy	5	超棒！	2025-06-09 20:27:51.251+00
rating_1750001888783	FB20250008	<EMAIL>	Kuanyi Li	SN20250003	ACT20250001	okboy	5	這次上課真的非常開心，兩位教練都非常有耐心，整堂課的情緒價值給得特別足，讓我第一次滑雪就覺得很好玩，也有運動到的感覺。課程內容很充實，但教練們還是能照顧到每位學員的狀況，這點特別讓人安心。而且課後的影片也非常有幫助，因為當下其實沒意識到自己動作那麼大，看影片之後才更清楚自己的問題，非常感謝！下次會嘗試更大膽的做動作的💪	2025-06-15 15:38:08.077+00
\.


--
-- Name: activities activities_pkey; Type: CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_pkey PRIMARY KEY (id);


--
-- Name: checkins checkins_pkey; Type: CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.checkins
    ADD CONSTRAINT checkins_pkey PRIMARY KEY (id);


--
-- Name: courses courses_pkey; Type: CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.courses
    ADD CONSTRAINT courses_pkey PRIMARY KEY (id);


--
-- Name: email_templates email_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.email_templates
    ADD CONSTRAINT email_templates_pkey PRIMARY KEY (id);


--
-- Name: feedback_templates feedback_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.feedback_templates
    ADD CONSTRAINT feedback_templates_pkey PRIMARY KEY (id);


--
-- Name: feedbacks feedbacks_pkey; Type: CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.feedbacks
    ADD CONSTRAINT feedbacks_pkey PRIMARY KEY (id);


--
-- Name: members members_email_key; Type: CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.members
    ADD CONSTRAINT members_email_key UNIQUE (email);


--
-- Name: members members_pkey; Type: CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.members
    ADD CONSTRAINT members_pkey PRIMARY KEY (id);


--
-- Name: navigation_items navigation_items_pkey; Type: CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.navigation_items
    ADD CONSTRAINT navigation_items_pkey PRIMARY KEY (id);


--
-- Name: student_ratings student_ratings_pkey; Type: CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.student_ratings
    ADD CONSTRAINT student_ratings_pkey PRIMARY KEY (id);


--
-- Name: idx_activities_date; Type: INDEX; Schema: public; Owner: snownavi_user
--

CREATE INDEX idx_activities_date ON public.activities USING btree (date);


--
-- Name: idx_checkins_activity_id; Type: INDEX; Schema: public; Owner: snownavi_user
--

CREATE INDEX idx_checkins_activity_id ON public.checkins USING btree (activity_id);


--
-- Name: idx_checkins_member_id; Type: INDEX; Schema: public; Owner: snownavi_user
--

CREATE INDEX idx_checkins_member_id ON public.checkins USING btree (member_id);


--
-- Name: idx_feedbacks_activity_id; Type: INDEX; Schema: public; Owner: snownavi_user
--

CREATE INDEX idx_feedbacks_activity_id ON public.feedbacks USING btree (activity_id);


--
-- Name: idx_feedbacks_member_id; Type: INDEX; Schema: public; Owner: snownavi_user
--

CREATE INDEX idx_feedbacks_member_id ON public.feedbacks USING btree (member_id);


--
-- Name: idx_members_email; Type: INDEX; Schema: public; Owner: snownavi_user
--

CREATE INDEX idx_members_email ON public.members USING btree (email);


--
-- Name: idx_student_ratings_feedback_id; Type: INDEX; Schema: public; Owner: snownavi_user
--

CREATE INDEX idx_student_ratings_feedback_id ON public.student_ratings USING btree (feedback_id);


--
-- Name: activities update_activities_updated_at; Type: TRIGGER; Schema: public; Owner: snownavi_user
--

CREATE TRIGGER update_activities_updated_at BEFORE UPDATE ON public.activities FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: courses update_courses_updated_at; Type: TRIGGER; Schema: public; Owner: snownavi_user
--

CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON public.courses FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: email_templates update_email_templates_updated_at; Type: TRIGGER; Schema: public; Owner: snownavi_user
--

CREATE TRIGGER update_email_templates_updated_at BEFORE UPDATE ON public.email_templates FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: feedback_templates update_feedback_templates_updated_at; Type: TRIGGER; Schema: public; Owner: snownavi_user
--

CREATE TRIGGER update_feedback_templates_updated_at BEFORE UPDATE ON public.feedback_templates FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: feedbacks update_feedbacks_updated_at; Type: TRIGGER; Schema: public; Owner: snownavi_user
--

CREATE TRIGGER update_feedbacks_updated_at BEFORE UPDATE ON public.feedbacks FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: members update_members_updated_at; Type: TRIGGER; Schema: public; Owner: snownavi_user
--

CREATE TRIGGER update_members_updated_at BEFORE UPDATE ON public.members FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: navigation_items update_navigation_items_updated_at; Type: TRIGGER; Schema: public; Owner: snownavi_user
--

CREATE TRIGGER update_navigation_items_updated_at BEFORE UPDATE ON public.navigation_items FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: checkins checkins_activity_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.checkins
    ADD CONSTRAINT checkins_activity_id_fkey FOREIGN KEY (activity_id) REFERENCES public.activities(id);


--
-- Name: checkins checkins_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.checkins
    ADD CONSTRAINT checkins_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.members(id);


--
-- Name: feedbacks feedbacks_activity_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.feedbacks
    ADD CONSTRAINT feedbacks_activity_id_fkey FOREIGN KEY (activity_id) REFERENCES public.activities(id);


--
-- Name: feedbacks feedbacks_checkin_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.feedbacks
    ADD CONSTRAINT feedbacks_checkin_id_fkey FOREIGN KEY (checkin_id) REFERENCES public.checkins(id);


--
-- Name: feedbacks feedbacks_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.feedbacks
    ADD CONSTRAINT feedbacks_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.members(id);


--
-- Name: student_ratings student_ratings_activity_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.student_ratings
    ADD CONSTRAINT student_ratings_activity_id_fkey FOREIGN KEY (activity_id) REFERENCES public.activities(id);


--
-- Name: student_ratings student_ratings_feedback_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.student_ratings
    ADD CONSTRAINT student_ratings_feedback_id_fkey FOREIGN KEY (feedback_id) REFERENCES public.feedbacks(id);


--
-- Name: student_ratings student_ratings_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: snownavi_user
--

ALTER TABLE ONLY public.student_ratings
    ADD CONSTRAINT student_ratings_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.members(id);


--
-- PostgreSQL database dump complete
--

