# SnowNavi Database Migration Guide

This directory contains the database migration tools and scripts for migrating SnowNavi from JSON file storage to PostgreSQL database storage.

## Overview

The migration process supports three data storage modes:
- **JSON Mode**: Uses only JSON files (current implementation)
- **Database Mode**: Uses only PostgreSQL database
- **Hybrid Mode**: Uses database with JSON fallback (recommended for transition)

## Prerequisites

1. <PERSON>er and <PERSON>er Compose installed
2. Python 3.11+ with virtual environment
3. PostgreSQL client tools (optional, for manual database access)

## Migration Steps

### Step 1: Environment Setup

1. Copy the environment configuration:
```bash
cp backend/.env.example backend/.env
```

2. Edit `backend/.env` and configure your settings:
```bash
# Set data mode for transition
DATA_MODE=hybrid

# Database connection (default works with Docker)
DATABASE_URL=postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi
```

### Step 2: Start Database

1. Start PostgreSQL database with Docker:
```bash
docker compose up -d db
```

2. Wait for database to be ready (check logs):
```bash
docker compose logs -f db
```

### Step 3: Create Database Schema

The database schema will be automatically created when the PostgreSQL container starts using the init script.

### Step 4: Test Database Connection

```bash
# Create Python virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r backend/requirements.txt

# Test database connection
python database/test_connection.py
```

### Step 5: Run Data Migration

```bash
# Option 1: Using Docker (recommended)
docker compose run --rm migrate

# Option 2: Using local Python
python database/migrate.py
```

### Step 6: Verify Migration

After migration, verify that data has been correctly transferred:

```bash
# Check database tables
python database/test_connection.py

# Compare JSON and database data (script to be created)
python database/verify_migration.py
```

### Step 7: Switch to Database Mode

Once migration is verified:

1. Update `backend/.env`:
```bash
DATA_MODE=database
```

2. Restart the Flask server:
```bash
# If using systemd
sudo systemctl restart snownavi-flask

# If running manually
cd backend && python server.py
```

## Database Schema

The database schema includes the following tables:

- `members` - User/member information
- `activities` - Course activities
- `checkins` - Activity check-ins
- `feedbacks` - Student feedback data
- `feedback_templates` - Feedback form templates
- `email_templates` - Email notification templates
- `navigation_items` - Website navigation configuration
- `student_ratings` - Student ratings and reviews
- `courses` - Static course information (stored as JSONB)

## Rollback Plan

If you need to rollback to JSON mode:

1. Update `backend/.env`:
```bash
DATA_MODE=json
```

2. Restart the Flask server

The JSON files are preserved during migration, so rollback is immediate.

## Troubleshooting

### Database Connection Issues

1. Check if PostgreSQL is running:
```bash
docker compose ps
```

2. Check database logs:
```bash
docker compose logs db
```

3. Test connection manually:
```bash
psql postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi
```

### Migration Issues

1. Check migration logs:
```bash
docker compose logs migrate
```

2. Run migration with verbose logging:
```bash
python database/migrate.py
```

### Data Inconsistency

1. Use hybrid mode to compare data sources
2. Run verification scripts
3. Check application logs for errors

## Development

### Adding New Tables

1. Update `database/schema.sql`
2. Add migration logic to `database/migrate.py`
3. Update data access layer in `backend/data_access.py`

### Testing

Run the test suite to ensure migration doesn't break existing functionality:

```bash
# Run API tests (to be created)
python database/test_api.py
```

## Production Deployment

For production deployment:

1. Use environment-specific `.env` files
2. Set up database backups
3. Use `DATA_MODE=hybrid` initially for safety
4. Monitor application logs during transition
5. Switch to `DATA_MODE=database` after verification

## Support

For issues or questions about the migration process, check:

1. Application logs
2. Database logs  
3. Migration script output
4. This documentation
