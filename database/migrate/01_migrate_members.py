#!/usr/bin/env python3
"""
Members数据迁移脚本
从data/members.json迁移数据到PostgreSQL数据库
"""

import os
import sys
import json
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MembersMigrator:
    """Members数据迁移器"""
    
    def __init__(self, database_url=None):
        self.database_url = database_url or os.getenv('DATABASE_URL', 
            'postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi')
        self.data_file = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'members.json')
        self.conn = None
        
    def connect_database(self):
        """连接数据库"""
        max_retries = 10
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                self.conn = psycopg2.connect(self.database_url, cursor_factory=RealDictCursor)
                logger.info("数据库连接成功")
                return True
            except psycopg2.OperationalError as e:
                retry_count += 1
                logger.warning(f"数据库连接尝试 {retry_count}/{max_retries} 失败: {e}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    logger.error("数据库连接失败")
                    return False
        return False
    
    def load_members_data(self):
        """加载members JSON数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"成功加载 {len(data)} 个成员记录")
                return data
            else:
                logger.error(f"文件不存在: {self.data_file}")
                return {}
        except Exception as e:
            logger.error(f"加载JSON文件失败: {e}")
            return {}
    
    def parse_validity_period(self, validity_period):
        """解析有效期字符串"""
        if not validity_period or validity_period.strip() == "":
            return None, None
            
        try:
            # 解析 "01/01/2025 - 31/12/2025" 格式
            if ' - ' in validity_period:
                start_str, end_str = validity_period.split(' - ')
                start_date = datetime.strptime(start_str.strip(), '%d/%m/%Y').date()
                end_date = datetime.strptime(end_str.strip(), '%d/%m/%Y').date()
                return start_date, end_date
        except ValueError as e:
            logger.warning(f"无法解析有效期: {validity_period}, 错误: {e}")
        
        return None, None
    
    def migrate_members(self):
        """迁移members数据"""
        logger.info("开始迁移members数据...")
        
        members_data = self.load_members_data()
        if not members_data:
            logger.info("没有members数据需要迁移")
            return True
        
        cursor = self.conn.cursor()
        
        # 清空现有数据
        cursor.execute("DELETE FROM members")
        logger.info("清空现有members数据")
        
        success_count = 0
        error_count = 0
        
        for member_id, member in members_data.items():
            try:
                # 解析有效期
                validity_start, validity_end = self.parse_validity_period(
                    member.get('validityPeriod', '')
                )
                
                # 插入数据
                cursor.execute("""
                    INSERT INTO members (
                        id, name, email, is_active, 
                        validity_period_start, validity_period_end, password, gender
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    member_id,
                    member.get('name'),
                    member.get('email'),
                    member.get('isActive', True),
                    validity_start,
                    validity_end,
                    member.get('password'),
                    member.get('gender')
                ))
                
                success_count += 1
                logger.info(f"✅ 成功迁移成员: {member_id} - {member.get('name')}")
                
            except Exception as e:
                error_count += 1
                logger.error(f"❌ 迁移成员失败 {member_id}: {e}")
        
        self.conn.commit()
        logger.info(f"Members迁移完成: 成功 {success_count}, 失败 {error_count}")
        return error_count == 0

    def verify_migration(self):
        """验证迁移结果 - 逐列验证"""
        logger.info("验证members迁移结果...")

        cursor = self.conn.cursor()

        # 检查总数
        cursor.execute("SELECT COUNT(*) as count FROM members")
        db_count = cursor.fetchone()['count']

        # 加载原始数据计数
        original_data = self.load_members_data()
        original_count = len(original_data)

        logger.info(f"原始数据: {original_count} 条, 数据库: {db_count} 条")

        if db_count != original_count:
            logger.error("❌ 数据数量不匹配")
            return False

        # 验证具体字段
        cursor.execute("""
            SELECT id, name, email, is_active, validity_period_start,
                   validity_period_end, password, gender
            FROM members
            ORDER BY id
        """)

        db_members = cursor.fetchall()
        verification_passed = 0
        verification_failed = 0

        for db_member in db_members:
            member_id = db_member['id']
            if member_id in original_data:
                original = original_data[member_id]

                # 验证基本字段
                checks = [
                    (db_member['name'] == original.get('name'), 'name'),
                    (db_member['email'] == original.get('email'), 'email'),
                    (db_member['is_active'] == original.get('isActive', True), 'is_active'),
                    (db_member['password'] == original.get('password'), 'password'),
                    (db_member['gender'] == original.get('gender'), 'gender')
                ]

                # 验证有效期
                expected_start, expected_end = self.parse_validity_period(
                    original.get('validityPeriod', '')
                )
                checks.extend([
                    (db_member['validity_period_start'] == expected_start, 'validity_period_start'),
                    (db_member['validity_period_end'] == expected_end, 'validity_period_end')
                ])

                all_passed = all(check[0] for check in checks)
                if all_passed:
                    verification_passed += 1
                    logger.info(f"✅ 验证通过: {member_id} - {db_member['name']}")
                else:
                    verification_failed += 1
                    failed_fields = [check[1] for check in checks if not check[0]]
                    logger.error(f"❌ 验证失败: {member_id}, 字段: {failed_fields}")

                    # 详细显示失败的字段值
                    for check, field_name in checks:
                        if not check:
                            db_val = db_member[field_name]
                            if field_name.startswith('validity_period'):
                                if field_name == 'validity_period_start':
                                    orig_val = self.parse_validity_period(original.get('validityPeriod', ''))[0]
                                else:
                                    orig_val = self.parse_validity_period(original.get('validityPeriod', ''))[1]
                            elif field_name == 'is_active':
                                orig_val = original.get('isActive', True)
                            else:
                                orig_val = original.get(field_name)
                            logger.error(f"   {field_name}: DB='{db_val}' vs JSON='{orig_val}'")
            else:
                verification_failed += 1
                logger.error(f"❌ 数据库中存在原始数据中没有的记录: {member_id}")

        logger.info(f"字段验证结果: 通过 {verification_passed}, 失败 {verification_failed}")
        return verification_failed == 0

    def run_migration(self):
        """运行完整迁移流程"""
        logger.info("🚀 开始Members数据迁移")
        logger.info("=" * 60)

        if not self.connect_database():
            return False

        try:
            # 执行迁移
            if not self.migrate_members():
                logger.error("❌ 数据迁移失败")
                return False

            # 验证迁移
            if not self.verify_migration():
                logger.error("❌ 迁移验证失败")
                return False

            logger.info("🎉 Members数据迁移完全成功！")
            return True

        except Exception as e:
            logger.error(f"❌ 迁移过程中发生错误: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False
        finally:
            if self.conn:
                self.conn.close()

if __name__ == '__main__':
    migrator = MembersMigrator()
    success = migrator.run_migration()
    sys.exit(0 if success else 1)
