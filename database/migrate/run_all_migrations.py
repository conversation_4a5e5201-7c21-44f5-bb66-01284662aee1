#!/usr/bin/env python3
"""
运行所有数据迁移脚本
按顺序执行所有JSON文件的迁移
"""

import os
import sys
import subprocess
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MasterMigrator:
    """主迁移器"""
    
    def __init__(self):
        self.script_dir = os.path.dirname(__file__)
        self.migration_scripts = [
            '00_cleanup_database.py',
            '00_create_tables.py',
            '01_migrate_members.py',
            '02_migrate_activities.py',
            '03_migrate_checkins.py',
            '04_migrate_feedback_templates.py',
            '05_migrate_feedbacks.py',
            '06_migrate_courses.py',
            '07_migrate_navigation.py',
            '08_migrate_email_templates.py',
            '09_migrate_student_ratings.py'
        ]
        
    def run_migration_script(self, script_name):
        """运行单个迁移脚本"""
        script_path = os.path.join(self.script_dir, script_name)
        
        if not os.path.exists(script_path):
            logger.warning(f"⚠️  迁移脚本不存在: {script_name}")
            return False
            
        logger.info(f"🔄 运行迁移脚本: {script_name}")
        
        try:
            result = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                logger.info(f"✅ {script_name} 迁移成功")
                return True
            else:
                logger.error(f"❌ {script_name} 迁移失败")
                logger.error(f"错误输出: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ {script_name} 迁移超时")
            return False
        except Exception as e:
            logger.error(f"💥 运行 {script_name} 时发生异常: {e}")
            return False
    
    def run_all_migrations(self):
        """运行所有迁移脚本"""
        logger.info("🚀 开始运行所有数据迁移")
        logger.info("=" * 80)
        
        total_scripts = len(self.migration_scripts)
        successful_migrations = 0
        failed_migrations = 0
        
        start_time = time.time()
        
        for i, script_name in enumerate(self.migration_scripts, 1):
            logger.info(f"\n📋 进度: {i}/{total_scripts} - {script_name}")
            logger.info("-" * 60)
            
            if self.run_migration_script(script_name):
                successful_migrations += 1
            else:
                failed_migrations += 1
                logger.error(f"❌ 迁移脚本 {script_name} 失败，继续下一个...")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 输出总结
        logger.info("\n" + "=" * 80)
        logger.info("📊 迁移总结")
        logger.info("=" * 80)
        logger.info(f"总迁移脚本数: {total_scripts}")
        logger.info(f"成功迁移: {successful_migrations}")
        logger.info(f"失败迁移: {failed_migrations}")
        logger.info(f"总耗时: {duration:.2f} 秒")
        
        if failed_migrations == 0:
            logger.info("🎉 所有数据迁移完全成功！")
            return True
        else:
            logger.error(f"⚠️  有 {failed_migrations} 个迁移失败")
            return False
    
    def check_database_connection(self):
        """检查数据库连接"""
        logger.info("🔍 检查数据库连接...")
        
        try:
            import psycopg2
            database_url = os.getenv('DATABASE_URL', 
                'postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi')
            
            conn = psycopg2.connect(database_url)
            conn.close()
            logger.info("✅ 数据库连接正常")
            return True
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def show_available_scripts(self):
        """显示可用的迁移脚本"""
        logger.info("📋 可用的迁移脚本:")
        
        for i, script_name in enumerate(self.migration_scripts, 1):
            script_path = os.path.join(self.script_dir, script_name)
            status = "✅ 存在" if os.path.exists(script_path) else "❌ 缺失"
            logger.info(f"   {i:2d}. {script_name:<30} {status}")

def main():
    """主函数"""
    migrator = MasterMigrator()
    
    logger.info("🗄️ SnowNavi 数据库迁移工具")
    logger.info("=" * 80)
    
    # 检查数据库连接
    if not migrator.check_database_connection():
        logger.error("❌ 无法连接数据库，请检查数据库服务是否运行")
        return False
    
    # 显示可用脚本
    migrator.show_available_scripts()
    
    # 运行所有迁移
    success = migrator.run_all_migrations()
    
    if success:
        logger.info("\n🎊 所有数据迁移任务完成！")
        logger.info("💡 提示: 可以使用 pgAdmin 查看迁移结果")
    else:
        logger.error("\n💥 部分迁移任务失败，请检查错误日志")
    
    return success

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\n⏹️  迁移被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n💥 迁移过程中发生未预期的错误: {e}")
        sys.exit(1)
