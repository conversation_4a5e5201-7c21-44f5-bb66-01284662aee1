#!/usr/bin/env python3
"""
Activities数据迁移脚本
从data/activities.json迁移数据到PostgreSQL数据库
"""

import os
import sys
import json
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ActivitiesMigrator:
    """Activities数据迁移器"""
    
    def __init__(self, database_url=None):
        self.database_url = database_url or os.getenv('DATABASE_URL', 
            'postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi')
        self.data_file = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'activities.json')
        self.conn = None
        
    def connect_database(self):
        """连接数据库"""
        max_retries = 10
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                self.conn = psycopg2.connect(self.database_url, cursor_factory=RealDictCursor)
                logger.info("数据库连接成功")
                return True
            except psycopg2.OperationalError as e:
                retry_count += 1
                logger.warning(f"数据库连接尝试 {retry_count}/{max_retries} 失败: {e}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    logger.error("数据库连接失败")
                    return False
        return False
    
    def load_activities_data(self):
        """加载activities JSON数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"成功加载 {len(data)} 个活动记录")
                return data
            else:
                logger.error(f"文件不存在: {self.data_file}")
                return {}
        except Exception as e:
            logger.error(f"加载JSON文件失败: {e}")
            return {}
    
    def parse_datetime(self, datetime_str):
        """解析ISO格式的日期时间字符串"""
        if not datetime_str:
            return None
        try:
            # 处理Z结尾的UTC时间
            if datetime_str.endswith('Z'):
                datetime_str = datetime_str[:-1] + '+00:00'
            return datetime.fromisoformat(datetime_str)
        except ValueError as e:
            logger.warning(f"无法解析日期时间: {datetime_str}, 错误: {e}")
            return None
    
    def parse_date(self, date_str):
        """解析日期字符串"""
        if not date_str:
            return None
        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError as e:
            logger.warning(f"无法解析日期: {date_str}, 错误: {e}")
            return None
    
    def migrate_activities(self):
        """迁移activities数据"""
        logger.info("开始迁移activities数据...")
        
        activities_data = self.load_activities_data()
        if not activities_data:
            logger.info("没有activities数据需要迁移")
            return True
        
        cursor = self.conn.cursor()
        
        # 清空现有数据
        cursor.execute("DELETE FROM activities")
        logger.info("清空现有activities数据")
        
        success_count = 0
        error_count = 0
        
        for activity_id, activity in activities_data.items():
            try:
                # 解析日期和时间
                activity_date = self.parse_date(activity.get('date'))
                created_at = self.parse_datetime(activity.get('createdAt'))
                updated_at = self.parse_datetime(activity.get('updatedAt'))
                
                # 提取多语言字段
                name = activity.get('name', {})
                location = activity.get('location', {})
                description = activity.get('description', {})
                
                # 插入数据
                cursor.execute("""
                    INSERT INTO activities (
                        id, name_en, name_zh, name_nl, type, date,
                        location_en, location_zh, location_nl,
                        description_en, description_zh, description_nl,
                        status, feedback_template_id, created_at, updated_at
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    activity_id,
                    name.get('en'),
                    name.get('zh'),
                    name.get('nl'),
                    activity.get('type'),
                    activity_date,
                    location.get('en'),
                    location.get('zh'),
                    location.get('nl'),
                    description.get('en'),
                    description.get('zh'),
                    description.get('nl'),
                    activity.get('status', 'active'),
                    activity.get('feedbackTemplateId'),
                    created_at,
                    updated_at
                ))
                
                success_count += 1
                logger.info(f"✅ 成功迁移活动: {activity_id} - {name.get('en', 'N/A')}")
                
            except Exception as e:
                error_count += 1
                logger.error(f"❌ 迁移活动失败 {activity_id}: {e}")
        
        self.conn.commit()
        logger.info(f"Activities迁移完成: 成功 {success_count}, 失败 {error_count}")
        return error_count == 0

    def verify_migration(self):
        """验证迁移结果 - 逐列验证"""
        logger.info("验证activities迁移结果...")

        cursor = self.conn.cursor()

        # 检查总数
        cursor.execute("SELECT COUNT(*) as count FROM activities")
        db_count = cursor.fetchone()['count']

        # 加载原始数据计数
        original_data = self.load_activities_data()
        original_count = len(original_data)

        logger.info(f"原始数据: {original_count} 条, 数据库: {db_count} 条")

        if db_count != original_count:
            logger.error("❌ 数据数量不匹配")
            return False

        # 验证具体字段
        cursor.execute("""
            SELECT id, name_en, name_zh, name_nl, type, date,
                   location_en, location_zh, location_nl,
                   description_en, description_zh, description_nl,
                   status, feedback_template_id, created_at, updated_at
            FROM activities
            ORDER BY id
        """)

        db_activities = cursor.fetchall()
        verification_passed = 0
        verification_failed = 0

        for db_activity in db_activities:
            activity_id = db_activity['id']
            if activity_id in original_data:
                original = original_data[activity_id]

                # 验证基本字段
                name = original.get('name', {})
                location = original.get('location', {})
                description = original.get('description', {})

                checks = [
                    (db_activity['name_en'] == name.get('en'), 'name_en'),
                    (db_activity['name_zh'] == name.get('zh'), 'name_zh'),
                    (db_activity['name_nl'] == name.get('nl'), 'name_nl'),
                    (db_activity['type'] == original.get('type'), 'type'),
                    (db_activity['location_en'] == location.get('en'), 'location_en'),
                    (db_activity['location_zh'] == location.get('zh'), 'location_zh'),
                    (db_activity['location_nl'] == location.get('nl'), 'location_nl'),
                    (db_activity['description_en'] == description.get('en'), 'description_en'),
                    (db_activity['description_zh'] == description.get('zh'), 'description_zh'),
                    (db_activity['description_nl'] == description.get('nl'), 'description_nl'),
                    (db_activity['status'] == original.get('status', 'active'), 'status'),
                    (db_activity['feedback_template_id'] == original.get('feedbackTemplateId'), 'feedback_template_id')
                ]

                # 验证日期和时间
                expected_date = self.parse_date(original.get('date'))
                expected_created_at = self.parse_datetime(original.get('createdAt'))
                expected_updated_at = self.parse_datetime(original.get('updatedAt'))

                checks.extend([
                    (db_activity['date'] == expected_date, 'date'),
                    (db_activity['created_at'] == expected_created_at, 'created_at'),
                    (db_activity['updated_at'] == expected_updated_at, 'updated_at')
                ])

                all_passed = all(check[0] for check in checks)
                if all_passed:
                    verification_passed += 1
                    logger.info(f"✅ 验证通过: {activity_id} - {db_activity['name_en']}")
                else:
                    verification_failed += 1
                    failed_fields = [check[1] for check in checks if not check[0]]
                    logger.error(f"❌ 验证失败: {activity_id}, 字段: {failed_fields}")

                    # 详细显示失败的字段值
                    for check, field_name in checks:
                        if not check:
                            db_val = db_activity[field_name]
                            if field_name == 'date':
                                orig_val = expected_date
                            elif field_name == 'created_at':
                                orig_val = expected_created_at
                            elif field_name == 'updated_at':
                                orig_val = expected_updated_at
                            elif field_name.startswith('name_'):
                                lang = field_name.split('_')[1]
                                orig_val = name.get(lang)
                            elif field_name.startswith('location_'):
                                lang = field_name.split('_')[1]
                                orig_val = location.get(lang)
                            elif field_name.startswith('description_'):
                                lang = field_name.split('_')[1]
                                orig_val = description.get(lang)
                            elif field_name == 'feedback_template_id':
                                orig_val = original.get('feedbackTemplateId')
                            else:
                                orig_val = original.get(field_name)
                            logger.error(f"   {field_name}: DB='{db_val}' vs JSON='{orig_val}'")
            else:
                verification_failed += 1
                logger.error(f"❌ 数据库中存在原始数据中没有的记录: {activity_id}")

        logger.info(f"字段验证结果: 通过 {verification_passed}, 失败 {verification_failed}")
        return verification_failed == 0

    def run_migration(self):
        """运行完整迁移流程"""
        logger.info("🚀 开始Activities数据迁移")
        logger.info("=" * 60)

        if not self.connect_database():
            return False

        try:
            # 执行迁移
            if not self.migrate_activities():
                logger.error("❌ 数据迁移失败")
                return False

            # 验证迁移
            if not self.verify_migration():
                logger.error("❌ 迁移验证失败")
                return False

            logger.info("🎉 Activities数据迁移完全成功！")
            return True

        except Exception as e:
            logger.error(f"❌ 迁移过程中发生错误: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False
        finally:
            if self.conn:
                self.conn.close()

if __name__ == '__main__':
    migrator = ActivitiesMigrator()
    success = migrator.run_migration()
    sys.exit(0 if success else 1)
