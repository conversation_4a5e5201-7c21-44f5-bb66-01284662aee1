#!/usr/bin/env python3
"""
Checkins数据迁移脚本
从data/checkins.json迁移数据到PostgreSQL数据库
"""

import os
import sys
import json
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CheckinsMigrator:
    """Checkins数据迁移器"""
    
    def __init__(self, database_url=None):
        self.database_url = database_url or os.getenv('DATABASE_URL', 
            'postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi')
        self.data_file = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'checkins.json')
        self.conn = None
        
    def connect_database(self):
        """连接数据库"""
        max_retries = 10
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                self.conn = psycopg2.connect(self.database_url, cursor_factory=RealDictCursor)
                logger.info("数据库连接成功")
                return True
            except psycopg2.OperationalError as e:
                retry_count += 1
                logger.warning(f"数据库连接尝试 {retry_count}/{max_retries} 失败: {e}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    logger.error("数据库连接失败")
                    return False
        return False
    
    def load_checkins_data(self):
        """加载checkins JSON数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"成功加载 {len(data)} 个签到记录")
                return data
            else:
                logger.error(f"文件不存在: {self.data_file}")
                return {}
        except Exception as e:
            logger.error(f"加载JSON文件失败: {e}")
            return {}
    
    def parse_datetime(self, datetime_str):
        """解析ISO格式的日期时间字符串"""
        if not datetime_str:
            return None
        try:
            # 处理Z结尾的UTC时间
            if datetime_str.endswith('Z'):
                datetime_str = datetime_str[:-1] + '+00:00'
            return datetime.fromisoformat(datetime_str)
        except ValueError as e:
            logger.warning(f"无法解析日期时间: {datetime_str}, 错误: {e}")
            return None
    
    def migrate_checkins(self):
        """迁移checkins数据"""
        logger.info("开始迁移checkins数据...")
        
        checkins_data = self.load_checkins_data()
        if not checkins_data:
            logger.info("没有checkins数据需要迁移")
            return True
        
        cursor = self.conn.cursor()
        
        # 清空现有数据
        cursor.execute("DELETE FROM checkins")
        logger.info("清空现有checkins数据")
        
        success_count = 0
        error_count = 0
        
        for checkin_id, checkin in checkins_data.items():
            try:
                # 解析时间
                checkin_time = self.parse_datetime(checkin.get('checkinTime'))
                created_at = self.parse_datetime(checkin.get('createdAt'))
                
                # 插入数据
                cursor.execute("""
                    INSERT INTO checkins (
                        id, activity_id, member_id, member_name, member_type,
                        checkin_time, checkin_by, checkin_by_email, notes, created_at
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    checkin_id,
                    checkin.get('activityId'),
                    checkin.get('memberId'),
                    checkin.get('memberName'),
                    checkin.get('memberType'),
                    checkin_time,
                    checkin.get('checkinBy'),
                    checkin.get('checkinByEmail'),
                    checkin.get('notes'),
                    created_at
                ))
                
                success_count += 1
                logger.info(f"✅ 成功迁移签到: {checkin_id} - {checkin.get('memberName')}")
                
            except Exception as e:
                error_count += 1
                logger.error(f"❌ 迁移签到失败 {checkin_id}: {e}")
        
        self.conn.commit()
        logger.info(f"Checkins迁移完成: 成功 {success_count}, 失败 {error_count}")
        return error_count == 0
    
    def verify_migration(self):
        """验证迁移结果 - 逐列验证"""
        logger.info("验证checkins迁移结果...")
        
        cursor = self.conn.cursor()
        
        # 检查总数
        cursor.execute("SELECT COUNT(*) as count FROM checkins")
        db_count = cursor.fetchone()['count']
        
        # 加载原始数据计数
        original_data = self.load_checkins_data()
        original_count = len(original_data)
        
        logger.info(f"原始数据: {original_count} 条, 数据库: {db_count} 条")
        
        if db_count != original_count:
            logger.error("❌ 数据数量不匹配")
            return False
        
        # 验证具体字段
        cursor.execute("""
            SELECT id, activity_id, member_id, member_name, member_type,
                   checkin_time, checkin_by, checkin_by_email, notes, created_at
            FROM checkins 
            ORDER BY id
        """)
        
        db_checkins = cursor.fetchall()
        verification_passed = 0
        verification_failed = 0
        
        for db_checkin in db_checkins:
            checkin_id = db_checkin['id']
            if checkin_id in original_data:
                original = original_data[checkin_id]
                
                # 验证字段
                expected_checkin_time = self.parse_datetime(original.get('checkinTime'))
                expected_created_at = self.parse_datetime(original.get('createdAt'))
                
                checks = [
                    (db_checkin['activity_id'] == original.get('activityId'), 'activity_id'),
                    (db_checkin['member_id'] == original.get('memberId'), 'member_id'),
                    (db_checkin['member_name'] == original.get('memberName'), 'member_name'),
                    (db_checkin['member_type'] == original.get('memberType'), 'member_type'),
                    (db_checkin['checkin_by'] == original.get('checkinBy'), 'checkin_by'),
                    (db_checkin['checkin_by_email'] == original.get('checkinByEmail'), 'checkin_by_email'),
                    (db_checkin['notes'] == original.get('notes'), 'notes'),
                    (db_checkin['checkin_time'] == expected_checkin_time, 'checkin_time'),
                    (db_checkin['created_at'] == expected_created_at, 'created_at')
                ]
                
                all_passed = all(check[0] for check in checks)
                if all_passed:
                    verification_passed += 1
                    logger.info(f"✅ 验证通过: {checkin_id} - {db_checkin['member_name']}")
                else:
                    verification_failed += 1
                    failed_fields = [check[1] for check in checks if not check[0]]
                    logger.error(f"❌ 验证失败: {checkin_id}, 字段: {failed_fields}")
            else:
                verification_failed += 1
                logger.error(f"❌ 数据库中存在原始数据中没有的记录: {checkin_id}")
        
        logger.info(f"字段验证结果: 通过 {verification_passed}, 失败 {verification_failed}")
        return verification_failed == 0
    
    def run_migration(self):
        """运行完整迁移流程"""
        logger.info("🚀 开始Checkins数据迁移")
        logger.info("=" * 60)
        
        if not self.connect_database():
            return False
        
        try:
            # 执行迁移
            if not self.migrate_checkins():
                logger.error("❌ 数据迁移失败")
                return False
            
            # 验证迁移
            if not self.verify_migration():
                logger.error("❌ 迁移验证失败")
                return False
            
            logger.info("🎉 Checkins数据迁移完全成功！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 迁移过程中发生错误: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False
        finally:
            if self.conn:
                self.conn.close()

if __name__ == '__main__':
    migrator = CheckinsMigrator()
    success = migrator.run_migration()
    sys.exit(0 if success else 1)
