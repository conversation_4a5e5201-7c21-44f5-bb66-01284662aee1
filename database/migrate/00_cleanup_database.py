#!/usr/bin/env python3
"""
数据库清理脚本
按正确顺序清理所有表数据，避免外键约束冲突
"""

import os
import sys
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseCleaner:
    """数据库清理器"""
    
    def __init__(self, database_url=None):
        self.database_url = database_url or os.getenv('DATABASE_URL', 
            'postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi')
        self.conn = None
        
        # 按依赖关系排序的表清理顺序（子表在前，父表在后）
        self.cleanup_order = [
            'student_ratings',    # 依赖 feedbacks
            'feedbacks',         # 依赖 checkins, activities, members
            'checkins',          # 依赖 activities, members
            'activities',        # 依赖 feedback_templates
            'feedback_templates', # 独立表
            'members',           # 独立表
            'email_templates',   # 独立表
            'navigation_items',  # 独立表
            'courses'           # 独立表
        ]
        
    def connect_database(self):
        """连接数据库"""
        max_retries = 10
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                self.conn = psycopg2.connect(self.database_url, cursor_factory=RealDictCursor)
                logger.info("数据库连接成功")
                return True
            except psycopg2.OperationalError as e:
                retry_count += 1
                logger.warning(f"数据库连接尝试 {retry_count}/{max_retries} 失败: {e}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    logger.error("数据库连接失败")
                    return False
        return False
    
    def get_table_count(self, table_name):
        """获取表中的记录数"""
        try:
            cursor = self.conn.cursor()
            cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
            return cursor.fetchone()['count']
        except Exception as e:
            logger.warning(f"无法获取表 {table_name} 的记录数: {e}")
            return 0
    
    def cleanup_table(self, table_name):
        """清理单个表"""
        try:
            cursor = self.conn.cursor()
            
            # 获取清理前的记录数
            before_count = self.get_table_count(table_name)
            
            # 清理表数据
            cursor.execute(f"DELETE FROM {table_name}")
            
            # 获取清理后的记录数
            after_count = self.get_table_count(table_name)
            
            logger.info(f"✅ 清理表 {table_name}: {before_count} → {after_count} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"❌ 清理表 {table_name} 失败: {e}")
            return False
    
    def cleanup_all_tables(self):
        """按正确顺序清理所有表"""
        logger.info("开始清理所有表数据...")
        
        cursor = self.conn.cursor()
        
        success_count = 0
        error_count = 0
        
        for table_name in self.cleanup_order:
            logger.info(f"🔄 清理表: {table_name}")
            
            if self.cleanup_table(table_name):
                success_count += 1
            else:
                error_count += 1
        
        # 提交事务
        self.conn.commit()
        
        logger.info(f"表清理完成: 成功 {success_count}, 失败 {error_count}")
        return error_count == 0
    
    def reset_sequences(self):
        """重置序列（如果有的话）"""
        logger.info("重置数据库序列...")
        
        try:
            cursor = self.conn.cursor()
            
            # 获取所有序列
            cursor.execute("""
                SELECT sequence_name 
                FROM information_schema.sequences 
                WHERE sequence_schema = 'public'
            """)
            
            sequences = cursor.fetchall()
            
            for seq in sequences:
                seq_name = seq['sequence_name']
                cursor.execute(f"ALTER SEQUENCE {seq_name} RESTART WITH 1")
                logger.info(f"✅ 重置序列: {seq_name}")
            
            self.conn.commit()
            logger.info("序列重置完成")
            return True
            
        except Exception as e:
            logger.warning(f"重置序列时出现警告: {e}")
            return True  # 序列重置失败不影响主流程
    
    def verify_cleanup(self):
        """验证清理结果"""
        logger.info("验证清理结果...")
        
        cursor = self.conn.cursor()
        total_records = 0
        
        for table_name in self.cleanup_order:
            count = self.get_table_count(table_name)
            total_records += count
            
            if count == 0:
                logger.info(f"✅ {table_name}: 0 条记录")
            else:
                logger.warning(f"⚠️  {table_name}: {count} 条记录（未完全清理）")
        
        if total_records == 0:
            logger.info("🎉 所有表已完全清理！")
            return True
        else:
            logger.warning(f"⚠️  仍有 {total_records} 条记录未清理")
            return False
    
    def run_cleanup(self):
        """运行完整清理流程"""
        logger.info("🚀 开始数据库清理")
        logger.info("=" * 60)
        
        if not self.connect_database():
            return False
        
        try:
            # 执行清理
            if not self.cleanup_all_tables():
                logger.error("❌ 表清理失败")
                return False
            
            # 重置序列
            self.reset_sequences()
            
            # 验证清理
            if not self.verify_cleanup():
                logger.warning("⚠️  清理验证有警告，但继续执行")
            
            logger.info("🎉 数据库清理完全成功！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 清理过程中发生错误: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False
        finally:
            if self.conn:
                self.conn.close()

if __name__ == '__main__':
    cleaner = DatabaseCleaner()
    success = cleaner.run_cleanup()
    sys.exit(0 if success else 1)
