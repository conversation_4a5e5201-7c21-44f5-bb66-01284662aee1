#!/usr/bin/env python3
"""
Courses数据迁移脚本
从data/courses.json迁移数据到PostgreSQL数据库
"""

import os
import sys
import json
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CoursesMigrator:
    """Courses数据迁移器"""
    
    def __init__(self, database_url=None):
        self.database_url = database_url or os.getenv('DATABASE_URL', 
            'postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi')
        self.data_file = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'courses.json')
        self.conn = None
        
    def connect_database(self):
        """连接数据库"""
        max_retries = 10
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                self.conn = psycopg2.connect(self.database_url, cursor_factory=RealDictCursor)
                logger.info("数据库连接成功")
                return True
            except psycopg2.OperationalError as e:
                retry_count += 1
                logger.warning(f"数据库连接尝试 {retry_count}/{max_retries} 失败: {e}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    logger.error("数据库连接失败")
                    return False
        return False
    
    def load_data(self):
        """加载JSON数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"成功加载 {len(data)} 个courses记录")
                return data
            else:
                logger.error(f"文件不存在: {self.data_file}")
                return {}
        except Exception as e:
            logger.error(f"加载JSON文件失败: {e}")
            return {}
    
    def migrate_data(self):
        """迁移数据"""
        logger.info("开始迁移courses数据...")
        
        data = self.load_data()
        if not data:
            logger.info("没有courses数据需要迁移")
            return True
        
        cursor = self.conn.cursor()
        
        # 清空现有数据
        cursor.execute("DELETE FROM courses")
        logger.info("清空现有courses数据")
        
        success_count = 0
        error_count = 0
        
        # 迁移courses数据 - 每个课程类别作为单独记录
        for course_id, course_data in data.items():
            try:
                cursor.execute("""
                    INSERT INTO courses (id, course_data, created_at, updated_at)
                    VALUES (%s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """, (course_id, json.dumps(course_data)))

                success_count += 1
                logger.info(f"✅ 成功迁移课程: {course_id}")

            except Exception as e:
                error_count += 1
                logger.error(f"❌ 迁移课程失败 {course_id}: {e}")
        
        self.conn.commit()
        logger.info(f"Courses迁移完成: 成功 {success_count}, 失败 {error_count}")
        return error_count == 0
    
    def verify_migration(self):
        """验证迁移结果"""
        logger.info("验证courses迁移结果...")
        
        cursor = self.conn.cursor()
        
        # 检查总数
        cursor.execute("SELECT COUNT(*) as count FROM courses")
        db_count = cursor.fetchone()['count']
        
        # 加载原始数据计数
        original_data = self.load_data()
        original_count = len(original_data)
        
        logger.info(f"原始数据: {original_count} 条, 数据库: {db_count} 条")
        
        if db_count != original_count:
            logger.error("❌ 数据数量不匹配")
            return False
        
        logger.info("✅ 数据数量验证通过")
        return True
    
    def run_migration(self):
        """运行完整迁移流程"""
        logger.info("🚀 开始Courses数据迁移")
        logger.info("=" * 60)
        
        if not self.connect_database():
            return False
        
        try:
            # 执行迁移
            if not self.migrate_data():
                logger.error("❌ 数据迁移失败")
                return False
            
            # 验证迁移
            if not self.verify_migration():
                logger.error("❌ 迁移验证失败")
                return False
            
            logger.info("🎉 Courses数据迁移完全成功！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 迁移过程中发生错误: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False
        finally:
            if self.conn:
                self.conn.close()

if __name__ == '__main__':
    migrator = CoursesMigrator()
    success = migrator.run_migration()
    sys.exit(0 if success else 1)
