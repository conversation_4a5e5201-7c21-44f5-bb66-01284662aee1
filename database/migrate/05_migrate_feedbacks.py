#!/usr/bin/env python3
"""
Feedbacks数据迁移脚本
从data/feedbacks.json迁移数据到PostgreSQL数据库
"""

import os
import sys
import json
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FeedbacksMigrator:
    """Feedbacks数据迁移器"""
    
    def __init__(self, database_url=None):
        self.database_url = database_url or os.getenv('DATABASE_URL', 
            'postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi')
        self.data_file = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'feedbacks.json')
        self.conn = None
        
    def connect_database(self):
        """连接数据库"""
        max_retries = 10
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                self.conn = psycopg2.connect(self.database_url, cursor_factory=RealDictCursor)
                logger.info("数据库连接成功")
                return True
            except psycopg2.OperationalError as e:
                retry_count += 1
                logger.warning(f"数据库连接尝试 {retry_count}/{max_retries} 失败: {e}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    logger.error("数据库连接失败")
                    return False
        return False
    
    def load_data(self):
        """加载JSON数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"成功加载 {len(data)} 个feedbacks记录")
                return data
            else:
                logger.error(f"文件不存在: {self.data_file}")
                return {}
        except Exception as e:
            logger.error(f"加载JSON文件失败: {e}")
            return {}

    def parse_datetime(self, datetime_str):
        """解析ISO格式的日期时间字符串"""
        if not datetime_str:
            return None
        try:
            if datetime_str.endswith('Z'):
                datetime_str = datetime_str[:-1] + '+00:00'
            from datetime import datetime
            return datetime.fromisoformat(datetime_str)
        except ValueError as e:
            logger.warning(f"无法解析日期时间: {datetime_str}, 错误: {e}")
            return None
    
    def migrate_data(self):
        """迁移数据"""
        logger.info("开始迁移feedbacks数据...")
        
        data = self.load_data()
        if not data:
            logger.info("没有feedbacks数据需要迁移")
            return True
        
        cursor = self.conn.cursor()
        
        # 清空现有数据
        cursor.execute("DELETE FROM feedbacks")
        logger.info("清空现有feedbacks数据")
        
        success_count = 0
        error_count = 0
        
        # 根据数据结构决定插入方式
        if isinstance(data, dict):
            # 如果是字典格式 (key-value pairs)
            for item_id, item_data in data.items():
                try:
                    # 将整个数据作为JSONB存储
                    if 'feedbacks' == 'courses':
                        cursor.execute("""
                            INSERT INTO feedbacks (id, course_data)
                            VALUES (%s, %s)
                        """, (item_id, json.dumps(item_data)))
                    elif 'feedbacks' == 'navigation_items':
                        cursor.execute("""
                            INSERT INTO feedbacks (id, url, translation_en, translation_zh, translation_nl, visible, order_index)
                            VALUES (%s, %s, %s, %s, %s, %s, %s)
                        """, (
                            item_data.get('id', item_id),
                            item_data.get('url'),
                            item_data.get('translation', {}).get('en'),
                            item_data.get('translation', {}).get('zh'),
                            item_data.get('translation', {}).get('nl'),
                            item_data.get('visible', True),
                            item_data.get('order', 0)
                        ))
                    elif 'feedbacks' == 'email_templates':
                        cursor.execute("""
                            INSERT INTO feedbacks (id, name, description, type, subject, html_content, triggers, active)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            item_data.get('id', item_id),
                            item_data.get('name'),
                            item_data.get('description'),
                            item_data.get('type'),
                            item_data.get('subject'),
                            item_data.get('htmlContent'),
                            json.dumps(item_data.get('triggers', {})),
                            item_data.get('active', True)
                        ))
                    else:
                        # Feedbacks表的具体字段处理
                        cursor.execute("""
                            INSERT INTO feedbacks (
                                id, checkin_id, activity_id, member_id, member_name,
                                overall_feedback, skill_assessment, feedback,
                                created_by, created_by_email, student_rating, created_at, updated_at
                            )
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            item_data.get('id', item_id),
                            item_data.get('checkinId'),
                            item_data.get('activityId'),
                            item_data.get('memberId'),
                            item_data.get('memberName'),
                            item_data.get('overallFeedback'),
                            json.dumps(item_data.get('skillAssessment', {})),
                            item_data.get('feedback'),
                            item_data.get('createdBy'),
                            item_data.get('createdByEmail'),
                            json.dumps(item_data.get('studentRating', {})) if item_data.get('studentRating') else None,
                            self.parse_datetime(item_data.get('createdAt')),
                            self.parse_datetime(item_data.get('updatedAt'))
                        ))
                    
                    success_count += 1
                    logger.info(f"✅ 成功迁移: {item_id}")
                    
                except Exception as e:
                    error_count += 1
                    logger.error(f"❌ 迁移失败 {item_id}: {e}")
        else:
            # 如果是列表格式
            for i, item_data in enumerate(data):
                try:
                    item_id = item_data.get('id', f'item_{i}')
                    cursor.execute(f"""
                        INSERT INTO feedbacks (id, data)
                        VALUES (%s, %s)
                    """, (item_id, json.dumps(item_data)))
                    
                    success_count += 1
                    logger.info(f"✅ 成功迁移: {item_id}")
                    
                except Exception as e:
                    error_count += 1
                    logger.error(f"❌ 迁移失败 {i}: {e}")
        
        self.conn.commit()
        logger.info(f"Feedbacks迁移完成: 成功 {success_count}, 失败 {error_count}")
        return error_count == 0
    
    def verify_migration(self):
        """验证迁移结果"""
        logger.info("验证feedbacks迁移结果...")
        
        cursor = self.conn.cursor()
        
        # 检查总数
        cursor.execute("SELECT COUNT(*) as count FROM feedbacks")
        db_count = cursor.fetchone()['count']
        
        # 加载原始数据计数
        original_data = self.load_data()
        original_count = len(original_data)
        
        logger.info(f"原始数据: {original_count} 条, 数据库: {db_count} 条")
        
        if db_count != original_count:
            logger.error("❌ 数据数量不匹配")
            return False
        
        logger.info("✅ 数据数量验证通过")
        return True
    
    def run_migration(self):
        """运行完整迁移流程"""
        logger.info("🚀 开始Feedbacks数据迁移")
        logger.info("=" * 60)
        
        if not self.connect_database():
            return False
        
        try:
            # 执行迁移
            if not self.migrate_data():
                logger.error("❌ 数据迁移失败")
                return False
            
            # 验证迁移
            if not self.verify_migration():
                logger.error("❌ 迁移验证失败")
                return False
            
            logger.info("🎉 Feedbacks数据迁移完全成功！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 迁移过程中发生错误: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False
        finally:
            if self.conn:
                self.conn.close()

if __name__ == '__main__':
    migrator = FeedbacksMigrator()
    success = migrator.run_migration()
    sys.exit(0 if success else 1)
