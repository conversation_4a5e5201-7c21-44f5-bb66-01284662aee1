# SnowNavi 数据库迁移指南

## 📋 概述

本目录包含了 SnowNavi 项目从 JSON 文件存储迁移到 PostgreSQL 数据库的完整迁移脚本和工具。

## 🗂️ 文件结构

```
database/migrate/
├── README.md                           # 本说明文档
├── run_all_migrations.py              # 主迁移工具（推荐使用）
├── 00_cleanup_database.py             # 数据库清理脚本
├── 00_create_tables.py                # 数据库表创建脚本
├── 01_migrate_members.py              # 成员数据迁移
├── 02_migrate_activities.py           # 活动数据迁移
├── 03_migrate_checkins.py             # 签到数据迁移
├── 04_migrate_feedback_templates.py   # 反馈模板迁移
├── 05_migrate_feedbacks.py            # 反馈数据迁移
├── 06_migrate_courses.py              # 课程数据迁移
├── 07_migrate_navigation.py           # 导航数据迁移
├── 08_migrate_email_templates.py      # 邮件模板迁移
└── 09_migrate_student_ratings.py      # 学生评分迁移
```

## 🐳 Docker 环境管理

### Docker 前置条件

确保您的系统已安装：
- **Docker**: 版本 20.0+
- **Docker Compose**: 版本 2.0+

验证安装：
```bash
docker --version
docker compose version
```

### 🚀 启动数据库服务

#### 1. 启动 PostgreSQL 数据库
```bash
# 启动数据库服务（后台运行）
docker compose up -d db

# 查看服务状态
docker compose ps

# 查看数据库日志
docker compose logs db

# 实时查看日志
docker compose logs -f db
```

#### 2. 验证数据库连接
```bash
# 方法1: 使用 docker compose 连接
docker compose exec db psql -U snownavi_user -d snownavi

# 方法2: 使用本地 psql 客户端
psql postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi

# 方法3: 测试连接脚本
python -c "
import psycopg2
try:
    conn = psycopg2.connect('postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi')
    print('✅ 数据库连接成功')
    conn.close()
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
"
```

### 🛑 停止和管理服务

#### 停止服务
```bash
# 停止所有服务
docker compose stop

# 仅停止数据库服务
docker compose stop db

# 停止并删除容器（保留数据卷）
docker compose down

# 停止并删除容器和数据卷（⚠️ 会删除所有数据）
docker compose down -v
```

#### 重启服务
```bash
# 重启所有服务
docker compose restart

# 仅重启数据库
docker compose restart db

# 强制重新创建容器
docker compose up -d --force-recreate db
```

### 🗑️ 清理和重置

#### 清理数据库数据（保留容器）
```bash
# 方法1: 使用迁移脚本清理
python database/migrate/00_cleanup_database.py

# 方法2: 手动清理
docker compose exec db psql -U snownavi_user -d snownavi -c "
DROP SCHEMA public CASCADE;
CREATE SCHEMA public;
GRANT ALL ON SCHEMA public TO snownavi_user;
"
```

#### 完全重置环境
```bash
# 1. 停止并删除所有容器和数据
docker compose down -v

# 2. 删除相关镜像（可选）
docker image rm postgres:15

# 3. 清理未使用的资源
docker system prune -f

# 4. 重新启动
docker compose up -d db
```

#### 数据备份和恢复
```bash
# 备份数据库
docker compose exec db pg_dump -U snownavi_user snownavi > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
docker compose exec -T db psql -U snownavi_user snownavi < backup_20231220_143000.sql
```

### 🔧 Docker 故障排除

#### 常见问题和解决方案

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :5432
   netstat -tulpn | grep 5432

   # 修改端口（在 docker-compose.yml 中）
   ports:
     - "5433:5432"  # 使用不同的本地端口
   ```

2. **容器启动失败**
   ```bash
   # 查看详细错误日志
   docker compose logs db

   # 检查容器状态
   docker compose ps -a

   # 进入容器调试
   docker compose exec db bash
   ```

3. **数据持久化问题**
   ```bash
   # 检查数据卷
   docker volume ls
   docker volume inspect snownavi_website_postgres_data

   # 查看数据卷挂载
   docker compose exec db df -h
   ```

4. **权限问题**
   ```bash
   # 检查数据库用户权限
   docker compose exec db psql -U snownavi_user -d snownavi -c "\du"

   # 重置用户权限
   docker compose exec db psql -U postgres -c "
   ALTER USER snownavi_user CREATEDB;
   GRANT ALL PRIVILEGES ON DATABASE snownavi TO snownavi_user;
   "
   ```

### 📊 监控和维护

#### 性能监控
```bash
# 查看容器资源使用
docker stats

# 查看数据库连接数
docker compose exec db psql -U snownavi_user -d snownavi -c "
SELECT count(*) as connections FROM pg_stat_activity;
"

# 查看数据库大小
docker compose exec db psql -U snownavi_user -d snownavi -c "
SELECT pg_size_pretty(pg_database_size('snownavi')) as database_size;
"
```

#### 日志管理
```bash
# 查看最近的日志
docker compose logs --tail=100 db

# 清理日志（重启容器）
docker compose restart db

# 设置日志轮转（在 docker-compose.yml 中）
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

## 🚀 快速开始

### 环境准备

1. **启动 PostgreSQL 数据库**
   ```bash
   docker compose up -d db
   ```

2. **环境变量配置**
   ```bash
   export DATABASE_URL="postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
   export DATA_MODE="database"
   ```

3. **Python 依赖安装**
   ```bash
   pip install psycopg2-binary
   ```

### 一键迁移（推荐）

```bash
# 运行完整迁移流程
python database/migrate/run_all_migrations.py
```

这个命令会：
1. ✅ 清理现有数据库数据
2. ✅ 创建所有必要的数据库表
3. ✅ 按正确顺序迁移所有数据
4. ✅ 验证迁移结果

## 📊 迁移内容

### 数据迁移统计
- **Members**: 20 个成员记录
- **Activities**: 7 个活动记录
- **Checkins**: 11 个签到记录
- **Feedback Templates**: 3 个反馈模板
- **Feedbacks**: 9 个反馈记录
- **Courses**: 5 个课程类别
- **Navigation**: 8 个导航项目
- **Email Templates**: 1 个邮件模板
- **Student Ratings**: 3 个学生评分

### 数据源文件
```
data/
├── members.json              → members 表
├── activities.json           → activities 表
├── checkins.json            → checkins 表
├── feedback_templates.json  → feedback_templates 表
├── feedbacks.json           → feedbacks 表
├── courses.json             → courses 表
├── navigation.json          → navigation_items 表
├── email_templates.json     → email_templates 表
└── student_ratings.json     → student_ratings 表
```

## �️ pgAdmin 数据库管理

### 启动 pgAdmin
```bash
# 启动 pgAdmin 服务
docker compose up -d pgadmin

# 查看 pgAdmin 状态
docker compose ps pgadmin

# 访问 pgAdmin Web 界面
# URL: http://localhost:8080
# 用户名: <EMAIL>
# 密码: admin123
```

### 配置数据库连接
在 pgAdmin 中添加服务器连接：
```
服务器名称: SnowNavi Database
主机: snownavi-db (容器内网络) 或 localhost (本地访问)
端口: 5432
数据库: snownavi
用户名: snownavi_user
密码: snownavi_password
```

### pgAdmin 常用操作
```bash
# 重启 pgAdmin
docker compose restart pgadmin

# 查看 pgAdmin 日志
docker compose logs pgadmin

# 停止 pgAdmin
docker compose stop pgadmin

# 清理 pgAdmin 数据
docker compose down -v
# 注意：这会删除 pgAdmin 的配置数据
```

## 🔄 完整的 Docker 工作流程

### 开发环境启动
```bash
# 1. 启动所有服务
docker compose up -d

# 2. 验证服务状态
docker compose ps

# 3. 查看服务日志
docker compose logs

# 4. 运行数据迁移
python database/migrate/run_all_migrations.py
```

### 日常开发操作
```bash
# 查看实时日志
docker compose logs -f db

# 进入数据库容器
docker compose exec db bash

# 执行 SQL 命令
docker compose exec db psql -U snownavi_user -d snownavi

# 重启特定服务
docker compose restart db
```

### 生产环境部署
```bash
# 1. 拉取最新镜像
docker compose pull

# 2. 启动服务（生产模式）
docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 3. 运行迁移
python database/migrate/run_all_migrations.py

# 4. 验证部署
curl -f http://localhost:8899/health || echo "Health check failed"
```

### 维护和更新
```bash
# 更新镜像
docker compose pull
docker compose up -d --force-recreate

# 清理未使用的资源
docker system prune -f

# 查看磁盘使用
docker system df
```

## �🔧 高级用法

### Docker Compose 配置说明

#### 服务配置
```yaml
# docker-compose.yml 主要配置
services:
  db:
    image: postgres:15
    container_name: snownavi-db
    environment:
      POSTGRES_DB: snownavi
      POSTGRES_USER: snownavi_user
      POSTGRES_PASSWORD: snownavi_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: snownavi-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - db
```

#### 数据卷管理
```bash
# 查看所有数据卷
docker volume ls

# 检查数据卷详情
docker volume inspect snownavi_website_postgres_data
docker volume inspect snownavi_website_pgadmin_data

# 备份数据卷
docker run --rm -v snownavi_website_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz -C /data .

# 恢复数据卷
docker run --rm -v snownavi_website_postgres_data:/data -v $(pwd):/backup alpine tar xzf /backup/postgres_backup.tar.gz -C /data
```

### 单独运行迁移脚本

```bash
# 清理数据库
python database/migrate/00_cleanup_database.py

# 创建表结构
python database/migrate/00_create_tables.py

# 迁移特定数据类型
python database/migrate/06_migrate_courses.py
```

### 容器内执行迁移
```bash
# 使用 Docker 容器运行迁移
docker compose run --rm migrate

# 自定义迁移命令
docker compose run --rm migrate python database/migrate/06_migrate_courses.py
```

### 验证迁移结果

```bash
# 检查课程数据
curl -s http://127.0.0.1:8899/data/courses.json | python -c "
import sys, json
data = json.load(sys.stdin)
print(f'Courses loaded: {len(data)}')
[print(f'- {k}: {v[\"en\"][\"title\"]}') for k, v in data.items()]
"

# 运行集成测试
python database/test/test_course_admin_integration.py
```

## 🛡️ 安全特性

### 外键约束处理
迁移脚本正确处理了数据库外键约束：
1. **清理顺序**: 先删除依赖表，再删除被依赖表
2. **迁移顺序**: 先迁移被依赖表，再迁移依赖表
3. **事务保护**: 每个迁移脚本都使用事务确保数据一致性

### 错误处理
- ✅ 数据库连接重试机制
- ✅ 详细的错误日志记录
- ✅ 迁移失败时的回滚机制
- ✅ 数据验证和完整性检查

## 📈 性能优化

### 批量操作
- 使用批量插入减少数据库往返
- 事务批处理提高性能
- 索引优化查询速度

### 内存管理
- 流式处理大文件
- 及时释放数据库连接
- 优化JSON解析性能

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   ```
   解决方案: 确保 PostgreSQL 服务运行，检查连接字符串
   ```

2. **外键约束错误**
   ```
   解决方案: 使用 run_all_migrations.py，它会正确处理依赖关系
   ```

3. **JSON 文件不存在**
   ```
   解决方案: 确保 data/ 目录下有所有必要的 JSON 文件
   ```

### 日志分析
迁移脚本提供详细的日志输出：
- 🔄 进度指示器
- ✅ 成功操作确认
- ❌ 错误详情和堆栈跟踪
- 📊 迁移统计摘要

## 🎯 生产环境部署

### 部署前检查清单
- [ ] 数据库备份已完成
- [ ] 环境变量正确配置
- [ ] 所有依赖已安装
- [ ] 网络连接正常
- [ ] 磁盘空间充足

### 部署步骤
1. **停止应用服务**
2. **备份现有数据**
3. **运行迁移脚本**
4. **验证迁移结果**
5. **启动应用服务**
6. **功能测试验证**

### 回滚计划
如果迁移失败：
1. 恢复数据库备份
2. 切换回 JSON 模式
3. 分析失败原因
4. 修复问题后重新迁移

## 📞 支持

如果在迁移过程中遇到问题：
1. 查看详细的错误日志
2. 检查数据库连接和权限
3. 验证 JSON 数据文件完整性
4. 运行单独的测试脚本进行诊断

## 🎉 迁移完成后

迁移成功后，您可以：
- ✅ 使用 Course Admin 页面管理课程
- ✅ 所有 CRUD 操作都通过数据库
- ✅ 享受更好的数据一致性和性能
- ✅ 使用 pgAdmin 等工具管理数据库

---

**注意**: 在生产环境中运行迁移前，请务必在测试环境中完整验证迁移流程！
