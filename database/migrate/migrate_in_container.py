#!/usr/bin/env python3
"""
Migration script to run inside the database container
"""

import json
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime

# JSON data (embedded for simplicity)
MEMBERS_DATA = {
    "SN20210001": {
        "name": "<PERSON><PERSON><PERSON>",
        "id": "SN20210001",
        "isActive": True,
        "validityPeriod": "01/01/2025 - 31/12/2025",
        "email": "<EMAIL>"
    },
    "SN20210002": {
        "name": "<PERSON><PERSON> <PERSON>",
        "id": "SN20210002",
        "isActive": True,
        "validityPeriod": "01/01/2025 - 31/12/2025",
        "email": "<EMAIL>"
    },
    "SN20210003": {
        "name": "Yizhou Ye",
        "id": "SN20210003",
        "isActive": True,
        "validityPeriod": "01/01/2025 - 31/12/2025",
        "email": "<EMAIL>"
    }
}

ACTIVITIES_DATA = {
    "ACT20250001": {
        "id": "ACT20250001",
        "name": {
            "en": "Beginner Group Lesson",
            "zh": "新手团课",
            "nl": "Beginner Group Lesson"
        },
        "type": "group",
        "date": "2025-06-14",
        "location": {
            "en": "SnowWorld Zoetermeer",
            "zh": "SnowWorld Zoetermeer",
            "nl": "SnowWorld Zoetermeer"
        },
        "description": {
            "en": "Beginner Group Lesson session 1/2",
            "zh": "新手团课 课时1/2",
            "nl": "Beginner Group Lesson session 1/2"
        },
        "status": "active",
        "createdAt": "2025-01-15T10:00:00Z",
        "updatedAt": "2025-06-11T20:33:47.749Z",
        "feedbackTemplateId": "snowboard_beginner"
    }
}

CHECKINS_DATA = {
    "CHK20250001": {
        "id": "CHK20250001",
        "activityId": "ACT20250001",
        "memberId": "SN20210001",
        "memberName": "Yujie Wang",
        "memberType": "coach",
        "checkinTime": "2025-06-11T20:33:16.125Z",
        "checkinBy": "okboy",
        "checkinByEmail": "<EMAIL>",
        "notes": "Checked in via QR code scan as coach by okboy",
        "createdAt": "2025-06-11T20:33:16.125Z"
    },
    "CHK20250002": {
        "id": "CHK20250002",
        "activityId": "ACT20250001",
        "memberId": "SN20210003",
        "memberName": "Yizhou Ye",
        "memberType": "member",
        "checkinTime": "2025-06-11T20:33:25.445Z",
        "checkinBy": "okboy",
        "checkinByEmail": "<EMAIL>",
        "notes": "Checked in via QR code scan as member by okboy",
        "createdAt": "2025-06-11T20:33:25.445Z"
    }
}

def migrate_data():
    """Migrate JSON data to database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="snownavi",
            user="snownavi",
            password="SnowNavi2025!",
            cursor_factory=RealDictCursor
        )
        cursor = conn.cursor()
        
        print("✅ Connected to database")
        
        # Migrate members
        print("📥 Migrating members...")
        for member_id, member in MEMBERS_DATA.items():
            cursor.execute("""
                INSERT INTO members (id, name, email, is_active, created_at, updated_at)
                VALUES (%s, %s, %s, %s, NOW(), NOW())
                ON CONFLICT (id) DO UPDATE SET
                    name = EXCLUDED.name,
                    email = EXCLUDED.email,
                    is_active = EXCLUDED.is_active,
                    updated_at = NOW()
            """, (
                member_id,
                member.get('name'),
                member.get('email'),
                member.get('isActive', True)
            ))
            print(f"  ✅ Migrated member: {member_id}")
        
        # Migrate activities
        print("📥 Migrating activities...")
        for activity_id, activity in ACTIVITIES_DATA.items():
            activity_date = datetime.strptime(activity['date'], '%Y-%m-%d').date()
            cursor.execute("""
                INSERT INTO activities (id, type, date, status, created_at, updated_at)
                VALUES (%s, %s, %s, %s, NOW(), NOW())
                ON CONFLICT (id) DO UPDATE SET
                    type = EXCLUDED.type,
                    date = EXCLUDED.date,
                    status = EXCLUDED.status,
                    updated_at = NOW()
            """, (
                activity_id,
                activity.get('type'),
                activity_date,
                activity.get('status', 'active')
            ))
            print(f"  ✅ Migrated activity: {activity_id}")
        
        # Migrate checkins
        print("📥 Migrating checkins...")
        for checkin_id, checkin in CHECKINS_DATA.items():
            checkin_time = datetime.fromisoformat(checkin['checkinTime'].replace('Z', '+00:00'))
            cursor.execute("""
                INSERT INTO checkins (id, activity_id, member_id, member_name, member_type, checkin_time, checkin_by, notes, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW())
                ON CONFLICT (id) DO UPDATE SET
                    activity_id = EXCLUDED.activity_id,
                    member_id = EXCLUDED.member_id,
                    member_name = EXCLUDED.member_name,
                    member_type = EXCLUDED.member_type,
                    checkin_time = EXCLUDED.checkin_time,
                    checkin_by = EXCLUDED.checkin_by,
                    notes = EXCLUDED.notes
            """, (
                checkin_id,
                checkin.get('activityId'),
                checkin.get('memberId'),
                checkin.get('memberName'),
                checkin.get('memberType'),
                checkin_time,
                checkin.get('checkinBy'),
                checkin.get('notes')
            ))
            print(f"  ✅ Migrated checkin: {checkin_id}")
        
        # Commit changes
        conn.commit()
        print("\n✅ All migrations completed successfully!")
        
        # Verify data
        print("\n📊 Verification:")
        tables = ['members', 'activities', 'checkins']
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
            count = cursor.fetchone()
            print(f"  - {table}: {count['count']} records")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Starting Container Migration")
    print("=" * 40)
    success = migrate_data()
    if success:
        print("\n🎉 Migration completed successfully!")
    else:
        print("\n💥 Migration failed!")
