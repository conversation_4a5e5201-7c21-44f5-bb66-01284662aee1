#!/usr/bin/env python3
"""
数据库表创建脚本
从schema.sql创建所有必要的数据库表
"""

import os
import sys
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TableCreator:
    """数据库表创建器"""
    
    def __init__(self, database_url=None):
        self.database_url = database_url or os.getenv('DATABASE_URL', 
            'postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi')
        self.schema_file = os.path.join(os.path.dirname(__file__), '..', 'sql', 'schema.sql')
        self.conn = None
        
    def connect_database(self):
        """连接数据库"""
        max_retries = 10
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                self.conn = psycopg2.connect(self.database_url, cursor_factory=RealDictCursor)
                logger.info("数据库连接成功")
                return True
            except psycopg2.OperationalError as e:
                retry_count += 1
                logger.warning(f"数据库连接尝试 {retry_count}/{max_retries} 失败: {e}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    logger.error("数据库连接失败")
                    return False
        return False
    
    def load_schema(self):
        """加载schema.sql文件"""
        try:
            if os.path.exists(self.schema_file):
                with open(self.schema_file, 'r', encoding='utf-8') as f:
                    schema_sql = f.read()
                logger.info(f"成功加载schema文件: {self.schema_file}")
                return schema_sql
            else:
                logger.error(f"Schema文件不存在: {self.schema_file}")
                return None
        except Exception as e:
            logger.error(f"加载schema文件失败: {e}")
            return None
    
    def create_tables(self):
        """创建数据库表"""
        logger.info("开始创建数据库表...")
        
        schema_sql = self.load_schema()
        if not schema_sql:
            logger.error("无法加载schema文件")
            return False
        
        try:
            cursor = self.conn.cursor()
            
            # 执行schema SQL
            cursor.execute(schema_sql)
            self.conn.commit()
            
            logger.info("✅ 数据库表创建成功")
            return True
            
        except Exception as e:
            # 如果表已存在，回滚事务并继续
            self.conn.rollback()
            if "already exists" in str(e).lower():
                logger.warning(f"表可能已存在: {e}")
                return True
            else:
                logger.error(f"❌ 创建表失败: {e}")
                return False
    
    def verify_tables(self):
        """验证表是否创建成功"""
        logger.info("验证表创建结果...")
        
        expected_tables = [
            'members',
            'activities', 
            'checkins',
            'feedback_templates',
            'feedbacks',
            'courses',
            'navigation_items',
            'email_templates',
            'student_ratings'
        ]
        
        try:
            cursor = self.conn.cursor()
            
            # 获取所有表名
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_type = 'BASE TABLE'
            """)
            
            existing_tables = [row['table_name'] for row in cursor.fetchall()]
            
            missing_tables = []
            for table in expected_tables:
                if table in existing_tables:
                    logger.info(f"✅ 表存在: {table}")
                else:
                    logger.error(f"❌ 表缺失: {table}")
                    missing_tables.append(table)
            
            if missing_tables:
                logger.error(f"缺失表: {missing_tables}")
                return False
            else:
                logger.info("🎉 所有必要的表都已创建！")
                return True
                
        except Exception as e:
            logger.error(f"验证表时发生错误: {e}")
            return False
    
    def run_creation(self):
        """运行完整表创建流程"""
        logger.info("🚀 开始数据库表创建")
        logger.info("=" * 60)
        
        if not self.connect_database():
            return False
        
        try:
            # 创建表
            if not self.create_tables():
                logger.error("❌ 表创建失败")
                return False
            
            # 验证表
            if not self.verify_tables():
                logger.error("❌ 表验证失败")
                return False
            
            logger.info("🎉 数据库表创建完全成功！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 表创建过程中发生错误: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False
        finally:
            if self.conn:
                self.conn.close()

if __name__ == '__main__':
    creator = TableCreator()
    success = creator.run_creation()
    sys.exit(0 if success else 1)
