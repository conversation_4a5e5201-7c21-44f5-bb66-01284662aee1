#!/usr/bin/env python3
"""
Corrected migration script based on actual database schema
"""

import json
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime

# JSON data (embedded for simplicity)
MEMBERS_DATA = {
    "SN20210001": {
        "name": "<PERSON><PERSON><PERSON>",
        "id": "SN20210001",
        "isActive": True,
        "validityPeriod": "01/01/2025 - 31/12/2025",
        "email": "<EMAIL>"
    },
    "SN20210002": {
        "name": "Ran <PERSON>",
        "id": "SN20210002",
        "isActive": True,
        "validityPeriod": "01/01/2025 - 31/12/2025",
        "email": "<EMAIL>"
    },
    "SN20210003": {
        "name": "Yizhou Ye",
        "id": "SN20210003",
        "isActive": True,
        "validityPeriod": "01/01/2025 - 31/12/2025",
        "email": "<EMAIL>"
    }
}

ACTIVITIES_DATA = {
    "ACT20250001": {
        "id": "ACT20250001",
        "name": {
            "en": "Beginner Group Lesson",
            "zh": "新手团课",
            "nl": "Beginner Group Lesson"
        },
        "type": "group",
        "date": "2025-06-14",
        "location": {
            "en": "SnowWorld Zoetermeer",
            "zh": "SnowWorld Zoetermeer",
            "nl": "SnowWorld Zoetermeer"
        },
        "description": {
            "en": "Beginner Group Lesson session 1/2",
            "zh": "新手团课 课时1/2",
            "nl": "Beginner Group Lesson session 1/2"
        },
        "status": "active",
        "createdAt": "2025-01-15T10:00:00Z",
        "updatedAt": "2025-06-11T20:33:47.749Z",
        "feedbackTemplateId": "snowboard_beginner"
    }
}

CHECKINS_DATA = {
    "CHK20250001": {
        "id": "CHK20250001",
        "activityId": "ACT20250001",
        "memberId": "SN20210001",
        "memberName": "Yujie Wang",
        "memberType": "coach",
        "checkinTime": "2025-06-11T20:33:16.125Z",
        "checkinBy": "okboy",
        "checkinByEmail": "<EMAIL>",
        "notes": "Checked in via QR code scan as coach by okboy",
        "createdAt": "2025-06-11T20:33:16.125Z"
    },
    "CHK20250002": {
        "id": "CHK20250002",
        "activityId": "ACT20250001",
        "memberId": "SN20210003",
        "memberName": "Yizhou Ye",
        "memberType": "member",
        "checkinTime": "2025-06-11T20:33:25.445Z",
        "checkinBy": "okboy",
        "checkinByEmail": "<EMAIL>",
        "notes": "Checked in via QR code scan as member by okboy",
        "createdAt": "2025-06-11T20:33:25.445Z"
    }
}

def migrate_data():
    """Migrate JSON data to database using correct schema"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="snownavi",
            user="snownavi",
            password="SnowNavi2025!",
            cursor_factory=RealDictCursor
        )
        cursor = conn.cursor()
        
        print("✅ Connected to database")
        
        # Migrate members
        print("📥 Migrating members...")
        for member_id, member in MEMBERS_DATA.items():
            # Parse validity period
            validity_start = None
            validity_end = None
            if 'validityPeriod' in member and ' - ' in member['validityPeriod']:
                start_str, end_str = member['validityPeriod'].split(' - ')
                try:
                    validity_start = datetime.strptime(start_str.strip(), '%d/%m/%Y').date()
                    validity_end = datetime.strptime(end_str.strip(), '%d/%m/%Y').date()
                except ValueError:
                    pass
            
            cursor.execute("""
                INSERT INTO members (id, name, email, is_active, validity_start, validity_end)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    name = EXCLUDED.name,
                    email = EXCLUDED.email,
                    is_active = EXCLUDED.is_active,
                    validity_start = EXCLUDED.validity_start,
                    validity_end = EXCLUDED.validity_end,
                    updated_at = NOW()
            """, (
                member_id,
                member.get('name'),
                member.get('email'),
                member.get('isActive', True),
                validity_start,
                validity_end
            ))
            print(f"  ✅ Migrated member: {member_id}")
        
        # Migrate activities
        print("📥 Migrating activities...")
        for activity_id, activity in ACTIVITIES_DATA.items():
            activity_date = datetime.strptime(activity['date'], '%Y-%m-%d').date()
            cursor.execute("""
                INSERT INTO activities (id, type, date, status, feedback_template_id)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    type = EXCLUDED.type,
                    date = EXCLUDED.date,
                    status = EXCLUDED.status,
                    feedback_template_id = EXCLUDED.feedback_template_id,
                    updated_at = NOW()
            """, (
                activity_id,
                activity.get('type'),
                activity_date,
                activity.get('status', 'active'),
                activity.get('feedbackTemplateId')
            ))
            print(f"  ✅ Migrated activity: {activity_id}")
        
        # Migrate checkins (using correct column names)
        print("📥 Migrating checkins...")
        for checkin_id, checkin in CHECKINS_DATA.items():
            checkin_time = datetime.fromisoformat(checkin['checkinTime'].replace('Z', '+00:00'))
            cursor.execute("""
                INSERT INTO checkins (id, activity_id, member_id, member_type, checkin_time, checked_by, checked_by_email, notes)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    activity_id = EXCLUDED.activity_id,
                    member_id = EXCLUDED.member_id,
                    member_type = EXCLUDED.member_type,
                    checkin_time = EXCLUDED.checkin_time,
                    checked_by = EXCLUDED.checked_by,
                    checked_by_email = EXCLUDED.checked_by_email,
                    notes = EXCLUDED.notes
            """, (
                checkin_id,
                checkin.get('activityId'),
                checkin.get('memberId'),
                checkin.get('memberType'),
                checkin_time,
                checkin.get('checkinBy'),
                checkin.get('checkinByEmail'),
                checkin.get('notes')
            ))
            print(f"  ✅ Migrated checkin: {checkin_id}")
        
        # Commit changes
        conn.commit()
        print("\n✅ All migrations completed successfully!")
        
        # Verify data
        print("\n📊 Verification:")
        tables = ['members', 'activities', 'checkins']
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
            count = cursor.fetchone()
            print(f"  - {table}: {count['count']} records")
        
        # Show some sample data
        print("\n📋 Sample data:")
        cursor.execute("SELECT id, name, email FROM members LIMIT 3")
        members = cursor.fetchall()
        for member in members:
            print(f"  Member: {member['id']} - {member['name']} ({member['email']})")
        
        cursor.execute("SELECT id, type, date FROM activities LIMIT 3")
        activities = cursor.fetchall()
        for activity in activities:
            print(f"  Activity: {activity['id']} - {activity['type']} on {activity['date']}")
        
        cursor.execute("SELECT id, member_id, member_type FROM checkins LIMIT 3")
        checkins = cursor.fetchall()
        for checkin in checkins:
            print(f"  Checkin: {checkin['id']} - {checkin['member_id']} as {checkin['member_type']}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Starting Corrected Migration")
    print("=" * 40)
    success = migrate_data()
    if success:
        print("\n🎉 Migration completed successfully!")
        print("Database now contains the migrated JSON data!")
    else:
        print("\n💥 Migration failed!")
