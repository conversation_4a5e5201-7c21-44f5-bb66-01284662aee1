#!/usr/bin/env python3
"""
Members数据迁移脚本
从data/members.json迁移数据到PostgreSQL数据库
"""

import os
import sys
import json
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MembersMigrator:
    """Members数据迁移器"""
    
    def __init__(self, database_url=None):
        self.database_url = database_url or os.getenv('DATABASE_URL', 
            'postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi')
        self.data_file = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'members.json')
        self.conn = None
        
    def connect_database(self):
        """连接数据库"""
        max_retries = 10
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                self.conn = psycopg2.connect(self.database_url, cursor_factory=RealDictCursor)
                logger.info("数据库连接成功")
                return True
            except psycopg2.OperationalError as e:
                retry_count += 1
                logger.warning(f"数据库连接尝试 {retry_count}/{max_retries} 失败: {e}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    logger.error("数据库连接失败")
                    return False
        return False
    
    def load_members_data(self):
        """加载members JSON数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"成功加载 {len(data)} 个成员记录")
                return data
            else:
                logger.error(f"文件不存在: {self.data_file}")
                return {}
        except Exception as e:
            logger.error(f"加载JSON文件失败: {e}")
            return {}
    
    def parse_validity_period(self, validity_period):
        """解析有效期字符串"""
        if not validity_period or validity_period.strip() == "":
            return None, None
            
        try:
            # 解析 "01/01/2025 - 31/12/2025" 格式
            if ' - ' in validity_period:
                start_str, end_str = validity_period.split(' - ')
                start_date = datetime.strptime(start_str.strip(), '%d/%m/%Y').date()
                end_date = datetime.strptime(end_str.strip(), '%d/%m/%Y').date()
                return start_date, end_date
        except ValueError as e:
            logger.warning(f"无法解析有效期: {validity_period}, 错误: {e}")
        
        return None, None
    
    def migrate_members(self):
        """迁移members数据"""
        logger.info("开始迁移members数据...")
        
        members_data = self.load_members_data()
        if not members_data:
            logger.info("没有members数据需要迁移")
            return True
        
        cursor = self.conn.cursor()
        
        # 清空现有数据
        cursor.execute("DELETE FROM members")
        logger.info("清空现有members数据")
        
        success_count = 0
        error_count = 0
        
        for member_id, member in members_data.items():
            try:
                # 解析有效期
                validity_start, validity_end = self.parse_validity_period(
                    member.get('validityPeriod', '')
                )
                
                # 插入数据
                cursor.execute("""
                    INSERT INTO members (
                        id, name, email, is_active, 
                        validity_period_start, validity_period_end, password
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (
                    member_id,
                    member.get('name'),
                    member.get('email'),
                    member.get('isActive', True),
                    validity_start,
                    validity_end,
                    member.get('password')
                ))
                
                success_count += 1
                logger.info(f"✅ 成功迁移成员: {member_id} - {member.get('name')}")
                
            except Exception as e:
                error_count += 1
                logger.error(f"❌ 迁移成员失败 {member_id}: {e}")
        
        self.conn.commit()
        logger.info(f"Members迁移完成: 成功 {success_count}, 失败 {error_count}")
        return error_count == 0
