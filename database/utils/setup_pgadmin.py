#!/usr/bin/env python3
"""
pgAdmin设置和连接测试脚本
"""

import requests
import time
import subprocess
import sys

def check_pgadmin_status():
    """检查pgAdmin是否运行"""
    try:
        response = requests.get('http://localhost:8080', timeout=5)
        return response.status_code == 200
    except:
        return False

def check_database_status():
    """检查数据库是否运行"""
    try:
        result = subprocess.run(
            ['docker', 'exec', 'snownavi-db', 'pg_isready', '-U', 'snownavi_user'],
            capture_output=True, text=True, timeout=10
        )
        return result.returncode == 0
    except:
        return False

def start_services():
    """启动所需服务"""
    print("🚀 启动数据库和pgAdmin服务...")
    
    try:
        # 启动数据库和pgAdmin
        result = subprocess.run(
            ['docker', 'compose', 'up', '-d', 'db', 'pgadmin'],
            capture_output=True, text=True, timeout=60
        )
        
        if result.returncode == 0:
            print("✅ 服务启动成功")
            return True
        else:
            print(f"❌ 服务启动失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 启动服务时出错: {e}")
        return False

def wait_for_services():
    """等待服务就绪"""
    print("⏳ 等待服务启动...")
    
    # 等待数据库
    db_ready = False
    for i in range(30):
        if check_database_status():
            print("✅ 数据库已就绪")
            db_ready = True
            break
        time.sleep(1)
        print(f"   等待数据库... ({i+1}/30)")
    
    if not db_ready:
        print("❌ 数据库启动超时")
        return False
    
    # 等待pgAdmin
    pgadmin_ready = False
    for i in range(60):
        if check_pgadmin_status():
            print("✅ pgAdmin已就绪")
            pgadmin_ready = True
            break
        time.sleep(1)
        print(f"   等待pgAdmin... ({i+1}/60)")
    
    if not pgadmin_ready:
        print("❌ pgAdmin启动超时")
        return False
    
    return True

def show_connection_info():
    """显示连接信息"""
    print("\n" + "="*60)
    print("🖥️  pgAdmin GUI管理界面已就绪")
    print("="*60)
    
    print("\n📋 连接信息:")
    print("   🌐 pgAdmin URL: http://localhost:8080")
    print("   👤 用户名: <EMAIL>")
    print("   🔑 密码: admin123")
    
    print("\n🗄️  数据库连接配置:")
    print("   📊 服务器名称: SnowNavi Database")
    print("   🏠 主机地址: snownavi-db")
    print("   🚪 端口: 5432")
    print("   💾 数据库: snownavi")
    print("   👤 用户名: snownavi_user")
    print("   🔑 密码: snownavi_password")
    
    print("\n📖 使用步骤:")
    print("   1. 在浏览器中打开 http://localhost:8080")
    print("   2. 使用 <EMAIL> / admin123 登录")
    print("   3. 右键点击 'Servers' → 'Register' → 'Server'")
    print("   4. 输入上述数据库连接配置")
    print("   5. 点击 'Save' 完成连接")
    print("   6. 展开服务器查看数据库表和数据")

def test_database_data():
    """测试数据库数据"""
    print("\n🧪 测试数据库数据...")
    
    try:
        # 检查表数量
        result = subprocess.run([
            'docker', 'exec', 'snownavi-db', 'psql', '-U', 'snownavi_user', '-d', 'snownavi',
            '-t', '-c', "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='public';"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            table_count = result.stdout.strip()
            print(f"✅ 数据库表数量: {table_count}")
        else:
            print("⚠️  无法获取表数量")
        
        # 检查成员数据
        result = subprocess.run([
            'docker', 'exec', 'snownavi-db', 'psql', '-U', 'snownavi_user', '-d', 'snownavi',
            '-t', '-c', "SELECT COUNT(*) FROM members;"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            member_count = result.stdout.strip()
            print(f"✅ 成员数据: {member_count} 条记录")
        else:
            print("⚠️  无法获取成员数据")
            
        # 检查活动数据
        result = subprocess.run([
            'docker', 'exec', 'snownavi-db', 'psql', '-U', 'snownavi_user', '-d', 'snownavi',
            '-t', '-c', "SELECT COUNT(*) FROM activities;"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            activity_count = result.stdout.strip()
            print(f"✅ 活动数据: {activity_count} 条记录")
        else:
            print("⚠️  无法获取活动数据")
            
    except Exception as e:
        print(f"⚠️  数据测试出错: {e}")

def main():
    """主函数"""
    print("🗄️ SnowNavi PostgreSQL GUI管理界面设置")
    print("=" * 60)
    
    # 检查当前状态
    db_running = check_database_status()
    pgadmin_running = check_pgadmin_status()
    
    print(f"📊 数据库状态: {'✅ 运行中' if db_running else '❌ 未运行'}")
    print(f"🖥️  pgAdmin状态: {'✅ 运行中' if pgadmin_running else '❌ 未运行'}")
    
    # 如果服务未运行，启动它们
    if not db_running or not pgadmin_running:
        if not start_services():
            print("❌ 无法启动服务")
            sys.exit(1)
        
        if not wait_for_services():
            print("❌ 服务启动超时")
            sys.exit(1)
    else:
        print("✅ 所有服务已在运行")
    
    # 测试数据库数据
    test_database_data()
    
    # 显示连接信息
    show_connection_info()
    
    print("\n🎉 pgAdmin GUI管理界面设置完成！")
    print("💡 提示: 使用 'docker compose stop pgadmin' 停止GUI界面")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  设置被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 设置过程中发生错误: {e}")
        sys.exit(1)
