#!/usr/bin/env python3
"""
快速打开pgAdmin GUI界面
"""

import webbrowser
import requests
import subprocess
import time
import sys

def check_pgadmin_status():
    """检查pgAdmin是否运行"""
    try:
        response = requests.get('http://localhost:8080', timeout=3)
        return response.status_code == 200
    except:
        return False

def start_pgadmin():
    """启动pgAdmin"""
    print("🚀 启动pgAdmin...")
    try:
        result = subprocess.run(
            ['docker', 'compose', 'up', '-d', 'pgadmin'],
            capture_output=True, text=True, timeout=30
        )
        return result.returncode == 0
    except:
        return False

def wait_for_pgadmin():
    """等待pgAdmin就绪"""
    print("⏳ 等待pgAdmin启动...")
    for i in range(30):
        if check_pgadmin_status():
            return True
        time.sleep(1)
        print(f"   等待中... ({i+1}/30)")
    return False

def main():
    """主函数"""
    print("🖥️ 打开SnowNavi数据库GUI管理界面")
    print("=" * 50)
    
    # 检查pgAdmin状态
    if check_pgadmin_status():
        print("✅ pgAdmin已在运行")
    else:
        print("❌ pgAdmin未运行，正在启动...")
        if not start_pgadmin():
            print("❌ 启动pgAdmin失败")
            sys.exit(1)
        
        if not wait_for_pgadmin():
            print("❌ pgAdmin启动超时")
            sys.exit(1)
        
        print("✅ pgAdmin启动成功")
    
    # 打开浏览器
    print("🌐 正在打开浏览器...")
    webbrowser.open('http://localhost:8080')
    
    print("\n📋 登录信息:")
    print("   👤 用户名: <EMAIL>")
    print("   🔑 密码: admin123")
    
    print("\n🗄️ 数据库连接信息:")
    print("   📊 服务器名称: SnowNavi Database")
    print("   🏠 主机地址: snownavi-db")
    print("   🚪 端口: 5432")
    print("   💾 数据库: snownavi")
    print("   👤 用户名: snownavi_user")
    print("   🔑 密码: snownavi_password")
    
    print("\n✨ pgAdmin GUI界面已打开！")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 发生错误: {e}")
        sys.exit(1)
