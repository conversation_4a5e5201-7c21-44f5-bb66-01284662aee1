#!/bin/bash
# 数据库自动备份脚本

set -e

# 配置
DB_HOST="snownavi-db-prod"
DB_NAME="snownavi"
DB_USER="snownavi_user"
BACKUP_DIR="/backups"
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 生成备份文件名
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/snownavi_backup_$TIMESTAMP.sql"
BACKUP_FILE_COMPRESSED="$BACKUP_FILE.gz"

echo "$(date): 开始数据库备份..."

# 执行备份
pg_dump -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" \
    --verbose \
    --no-password \
    --format=custom \
    --compress=9 \
    --file="$BACKUP_FILE"

# 压缩备份文件
gzip "$BACKUP_FILE"

# 验证备份文件
if [ -f "$BACKUP_FILE_COMPRESSED" ]; then
    BACKUP_SIZE=$(du -h "$BACKUP_FILE_COMPRESSED" | cut -f1)
    echo "$(date): 备份完成 - 文件: $(basename $BACKUP_FILE_COMPRESSED), 大小: $BACKUP_SIZE"
else
    echo "$(date): 错误 - 备份文件未创建"
    exit 1
fi

# 清理旧备份文件
echo "$(date): 清理超过 $RETENTION_DAYS 天的旧备份..."
find "$BACKUP_DIR" -name "snownavi_backup_*.sql.gz" -mtime +$RETENTION_DAYS -delete

# 显示当前备份文件
echo "$(date): 当前备份文件:"
ls -lh "$BACKUP_DIR"/snownavi_backup_*.sql.gz | tail -5

# 可选：上传到云存储
if [ -n "$BACKUP_S3_BUCKET" ] && [ -n "$AWS_ACCESS_KEY_ID" ]; then
    echo "$(date): 上传备份到 S3..."
    aws s3 cp "$BACKUP_FILE_COMPRESSED" "s3://$BACKUP_S3_BUCKET/database-backups/"
    echo "$(date): S3 上传完成"
fi

echo "$(date): 备份流程完成"
