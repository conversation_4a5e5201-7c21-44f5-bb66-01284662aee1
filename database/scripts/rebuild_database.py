#!/usr/bin/env python3
"""
完整的数据库重建脚本
从零开始重新建立数据库、迁移数据并验证
"""

import subprocess
import time
import sys
import os
import requests

def run_command(cmd, description, timeout=60):
    """执行命令并返回结果"""
    print(f"🔧 {description}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        if result.returncode == 0:
            print(f"✅ {description} - 成功")
            return True, result.stdout
        else:
            print(f"❌ {description} - 失败: {result.stderr}")
            return False, result.stderr
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - 超时")
        return False, "Timeout"
    except Exception as e:
        print(f"💥 {description} - 异常: {e}")
        return False, str(e)

def check_database_ready():
    """检查数据库是否就绪"""
    for i in range(30):
        success, _ = run_command(
            'docker exec snownavi-db pg_isready -U snownavi_user',
            f"检查数据库就绪状态 ({i+1}/30)",
            timeout=5
        )
        if success:
            return True
        time.sleep(2)
    return False

def check_pgadmin_ready():
    """检查pgAdmin是否就绪"""
    for i in range(30):
        try:
            response = requests.get('http://localhost:8080', timeout=3)
            if response.status_code == 200:
                print("✅ pgAdmin已就绪")
                return True
        except:
            pass
        print(f"   等待pgAdmin... ({i+1}/30)")
        time.sleep(2)
    return False

def main():
    """主函数"""
    print("🚀 SnowNavi数据库完整重建")
    print("=" * 60)
    print("这将从零开始重新建立数据库、迁移数据并验证")
    print()
    
    # 确认操作
    confirm = input("⚠️  这将删除所有现有数据库数据。确认继续？(y/N): ")
    if confirm.lower() != 'y':
        print("❌ 操作已取消")
        return False
    
    print("\n🗑️  步骤 1: 清理现有容器和数据")
    success, _ = run_command(
        'docker compose down -v',
        "停止并删除所有容器和卷"
    )
    if not success:
        print("⚠️  清理失败，继续...")
    
    print("\n🏗️  步骤 2: 启动数据库服务")
    success, _ = run_command(
        'docker compose up -d db',
        "启动PostgreSQL数据库"
    )
    if not success:
        print("❌ 数据库启动失败")
        return False
    
    print("\n⏳ 步骤 3: 等待数据库就绪")
    if not check_database_ready():
        print("❌ 数据库启动超时")
        return False
    
    print("\n📋 步骤 4: 创建数据库表结构")
    success, _ = run_command(
        'docker exec -i snownavi-db psql -U snownavi_user -d snownavi < database/sql/schema.sql',
        "执行数据库schema"
    )
    if not success:
        print("❌ 创建表结构失败")
        return False
    
    print("\n📊 步骤 5: 验证表结构")
    success, output = run_command(
        'docker exec snownavi-db psql -U snownavi_user -d snownavi -c "\\dt"',
        "检查数据库表"
    )
    if success:
        print(f"   创建的表:\n{output}")
    
    print("\n🔄 步骤 6: 运行数据迁移")
    success, _ = run_command(
        'docker compose run --rm migrate',
        "执行数据迁移",
        timeout=120
    )
    if not success:
        print("❌ 数据迁移失败")
        return False
    
    print("\n🧪 步骤 7: 验证迁移数据")
    tables_to_check = ['members', 'activities', 'checkins']
    for table in tables_to_check:
        success, output = run_command(
            f'docker exec snownavi-db psql -U snownavi_user -d snownavi -t -c "SELECT COUNT(*) FROM {table};"',
            f"检查{table}表数据"
        )
        if success:
            count = output.strip()
            print(f"   {table}: {count} 条记录")
    
    print("\n🖥️  步骤 8: 启动pgAdmin GUI")
    success, _ = run_command(
        'docker compose up -d pgadmin',
        "启动pgAdmin GUI界面"
    )
    if not success:
        print("⚠️  pgAdmin启动失败，但数据库已就绪")
    else:
        print("⏳ 等待pgAdmin启动...")
        if check_pgadmin_ready():
            print("✅ pgAdmin GUI已就绪: http://localhost:8080")
        else:
            print("⚠️  pgAdmin启动超时，但服务已启动")
    
    print("\n🧪 步骤 9: 运行完整验证测试")
    # 切换到项目根目录运行测试
    os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    success, _ = run_command(
        'python database/test/final_entity_types_test.py',
        "运行实体类型验证测试"
    )
    if not success:
        print("⚠️  验证测试失败，但数据库已建立")
    
    print("\n" + "=" * 60)
    print("📊 重建结果总结")
    print("=" * 60)
    
    # 最终状态检查
    db_running = run_command('docker ps | grep snownavi-db', "检查数据库状态", timeout=5)[0]
    pgadmin_running = run_command('docker ps | grep snownavi-pgadmin', "检查pgAdmin状态", timeout=5)[0]
    
    print(f"📊 数据库状态: {'✅ 运行中' if db_running else '❌ 未运行'}")
    print(f"🖥️  pgAdmin状态: {'✅ 运行中' if pgadmin_running else '❌ 未运行'}")
    
    if db_running:
        print("\n✅ 数据库重建成功！")
        print("\n📋 连接信息:")
        print("   🗄️  数据库: postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi")
        if pgadmin_running:
            print("   🖥️  pgAdmin: http://localhost:8080 (<EMAIL> / admin123)")
        
        print("\n🔧 管理命令:")
        print("   查看容器: docker ps")
        print("   查看日志: docker logs snownavi-db")
        print("   连接数据库: docker exec -it snownavi-db psql -U snownavi_user -d snownavi")
        print("   停止服务: docker compose down")
        
        return True
    else:
        print("\n❌ 数据库重建失败")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  重建被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 重建过程中发生错误: {e}")
        sys.exit(1)
