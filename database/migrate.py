#!/usr/bin/env python3
"""
SnowNavi Database Migration Script
Migrates data from JSON files to PostgreSQL database
"""

import os
import sys
import json
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime
import time

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseMigrator:
    """Handles migration from JSON files to PostgreSQL database"""
    
    def __init__(self, database_url=None):
        self.database_url = database_url or os.getenv('DATABASE_URL', 
            'postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi')
        self.data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
        self.conn = None
        
    def connect_database(self):
        """Connect to PostgreSQL database"""
        max_retries = 30
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                self.conn = psycopg2.connect(self.database_url, cursor_factory=RealDictCursor)
                logger.info("Successfully connected to database")
                return True
            except psycopg2.OperationalError as e:
                retry_count += 1
                logger.warning(f"Database connection attempt {retry_count}/{max_retries} failed: {e}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    logger.error("Failed to connect to database after all retries")
                    return False
        return False
    
    def load_json_file(self, filename):
        """Load data from JSON file"""
        file_path = os.path.join(self.data_dir, filename)
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.warning(f"JSON file not found: {file_path}")
                return {}
        except Exception as e:
            logger.error(f"Error loading JSON file {filename}: {e}")
            return {}
    
    def migrate_members(self):
        """Migrate members data"""
        logger.info("Migrating members data...")
        members_data = self.load_json_file('members.json')
        
        if not members_data:
            logger.info("No members data to migrate")
            return True
        
        cursor = self.conn.cursor()
        
        for member_id, member in members_data.items():
            try:
                # Parse validity period if it exists
                validity_start = None
                validity_end = None
                if 'validityPeriod' in member:
                    if isinstance(member['validityPeriod'], dict):
                        validity_start = member['validityPeriod'].get('start')
                        validity_end = member['validityPeriod'].get('end')
                    elif isinstance(member['validityPeriod'], str):
                        # Parse "01/01/2025 - 31/12/2025" format
                        if ' - ' in member['validityPeriod']:
                            start_str, end_str = member['validityPeriod'].split(' - ')
                            try:
                                validity_start = datetime.strptime(start_str.strip(), '%d/%m/%Y').date()
                                validity_end = datetime.strptime(end_str.strip(), '%d/%m/%Y').date()
                            except ValueError:
                                logger.warning(f"Could not parse validity period for member {member_id}")
                
                cursor.execute("""
                    INSERT INTO members (id, name, email, is_active, validity_period_start, validity_period_end, password)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (id) DO UPDATE SET
                        name = EXCLUDED.name,
                        email = EXCLUDED.email,
                        is_active = EXCLUDED.is_active,
                        validity_period_start = EXCLUDED.validity_period_start,
                        validity_period_end = EXCLUDED.validity_period_end,
                        password = EXCLUDED.password,
                        updated_at = CURRENT_TIMESTAMP
                """, (
                    member_id,
                    member.get('name'),
                    member.get('email'),
                    member.get('isActive', True),
                    validity_start,
                    validity_end,
                    member.get('password')
                ))
                
            except Exception as e:
                logger.error(f"Error migrating member {member_id}: {e}")
                return False
        
        self.conn.commit()
        logger.info(f"Successfully migrated {len(members_data)} members")
        return True
    
    def migrate_activities(self):
        """Migrate activities data"""
        logger.info("Migrating activities data...")
        activities_data = self.load_json_file('activities.json')
        
        if not activities_data:
            logger.info("No activities data to migrate")
            return True
        
        cursor = self.conn.cursor()
        
        for activity_id, activity in activities_data.items():
            try:
                # Parse date
                activity_date = None
                if 'date' in activity:
                    try:
                        activity_date = datetime.strptime(activity['date'], '%Y-%m-%d').date()
                    except ValueError:
                        logger.warning(f"Could not parse date for activity {activity_id}")
                
                # Parse timestamps
                created_at = None
                updated_at = None
                if 'createdAt' in activity:
                    try:
                        created_at = datetime.fromisoformat(activity['createdAt'].replace('Z', '+00:00'))
                    except ValueError:
                        pass
                if 'updatedAt' in activity:
                    try:
                        updated_at = datetime.fromisoformat(activity['updatedAt'].replace('Z', '+00:00'))
                    except ValueError:
                        pass
                
                cursor.execute("""
                    INSERT INTO activities (
                        id, name_en, name_zh, name_nl, type, date,
                        location_en, location_zh, location_nl,
                        description_en, description_zh, description_nl,
                        status, feedback_template_id, created_at, updated_at
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (id) DO UPDATE SET
                        name_en = EXCLUDED.name_en,
                        name_zh = EXCLUDED.name_zh,
                        name_nl = EXCLUDED.name_nl,
                        type = EXCLUDED.type,
                        date = EXCLUDED.date,
                        location_en = EXCLUDED.location_en,
                        location_zh = EXCLUDED.location_zh,
                        location_nl = EXCLUDED.location_nl,
                        description_en = EXCLUDED.description_en,
                        description_zh = EXCLUDED.description_zh,
                        description_nl = EXCLUDED.description_nl,
                        status = EXCLUDED.status,
                        feedback_template_id = EXCLUDED.feedback_template_id,
                        updated_at = CURRENT_TIMESTAMP
                """, (
                    activity_id,
                    activity.get('name', {}).get('en'),
                    activity.get('name', {}).get('zh'),
                    activity.get('name', {}).get('nl'),
                    activity.get('type'),
                    activity_date,
                    activity.get('location', {}).get('en'),
                    activity.get('location', {}).get('zh'),
                    activity.get('location', {}).get('nl'),
                    activity.get('description', {}).get('en'),
                    activity.get('description', {}).get('zh'),
                    activity.get('description', {}).get('nl'),
                    activity.get('status', 'active'),
                    activity.get('feedbackTemplateId'),
                    created_at,
                    updated_at
                ))
                
            except Exception as e:
                logger.error(f"Error migrating activity {activity_id}: {e}")
                return False
        
        self.conn.commit()
        logger.info(f"Successfully migrated {len(activities_data)} activities")
        return True

    def migrate_courses(self):
        """Migrate courses data from JSON to database"""
        logger.info("Migrating courses data...")

        # Load courses data from JSON file
        courses_file = os.path.join(self.data_dir, 'courses.json')
        if not os.path.exists(courses_file):
            logger.warning("courses.json file not found, skipping courses migration")
            return True

        try:
            with open(courses_file, 'r', encoding='utf-8') as f:
                courses_data = json.load(f)
        except Exception as e:
            logger.error(f"Error reading courses.json: {e}")
            return False

        cursor = self.conn.cursor()

        # Clear existing courses data
        cursor.execute("DELETE FROM courses")

        # Insert each course category as a separate record
        for course_id, course_data in courses_data.items():
            try:
                cursor.execute("""
                    INSERT INTO courses (id, course_data, created_at, updated_at)
                    VALUES (%s, %s, %s, %s)
                """, (
                    course_id,
                    json.dumps(course_data),
                    datetime.now(),
                    datetime.now()
                ))

            except Exception as e:
                logger.error(f"Error migrating course {course_id}: {e}")
                return False

        self.conn.commit()
        logger.info(f"Successfully migrated {len(courses_data)} courses")
        return True
    
    def create_tables(self):
        """Create database tables from schema"""
        logger.info("Creating database tables...")

        schema_file = os.path.join(os.path.dirname(__file__), 'sql', 'schema.sql')
        if not os.path.exists(schema_file):
            logger.error("Schema file not found")
            return False

        try:
            with open(schema_file, 'r', encoding='utf-8') as f:
                schema_sql = f.read()

            cursor = self.conn.cursor()
            cursor.execute(schema_sql)
            self.conn.commit()

            logger.info("Database tables created successfully")
            return True

        except Exception as e:
            # Rollback the transaction if there's an error
            self.conn.rollback()
            logger.warning(f"Tables may already exist: {e}")
            return True  # Continue with migration

    def run_migration(self):
        """Run the complete migration process"""
        logger.info("Starting database migration...")

        if not self.connect_database():
            logger.error("Failed to connect to database")
            return False

        try:
            # Create tables first (skip if already exist)
            try:
                self.create_tables()
            except Exception as e:
                logger.warning(f"Tables may already exist: {e}")
                # Continue with migration even if table creation fails

            # Run migrations in order
            if not self.migrate_members():
                return False
            
            if not self.migrate_activities():
                return False

            if not self.migrate_courses():
                return False

            # Add more migration methods here as we implement them

            logger.info("Database migration completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return False
        finally:
            if self.conn:
                self.conn.close()


if __name__ == '__main__':
    migrator = DatabaseMigrator()
    success = migrator.run_migration()
    sys.exit(0 if success else 1)
