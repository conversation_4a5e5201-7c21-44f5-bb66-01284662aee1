#!/usr/bin/env python3
"""
简单的数据库验证脚本
"""

import subprocess
import sys

def run_sql_query(query, description):
    """执行SQL查询"""
    try:
        result = subprocess.run([
            'docker', 'exec', 'snownavi-db', 'psql', '-U', 'snownavi_user', '-d', 'snownavi',
            '-t', '-c', query
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            output = result.stdout.strip()
            print(f"✅ {description}: {output}")
            return True, output
        else:
            print(f"❌ {description}: {result.stderr}")
            return False, None
    except Exception as e:
        print(f"❌ {description}: {e}")
        return False, None

def main():
    """主验证函数"""
    print("🧪 SnowNavi数据库验证")
    print("=" * 40)
    
    # 检查表结构
    print("\n📋 数据库表结构:")
    success, _ = run_sql_query(
        "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='public';",
        "数据库表总数"
    )
    
    # 检查各表数据
    tables = [
        ('members', '成员数据'),
        ('activities', '活动数据'),
        ('checkins', '签到数据'),
        ('feedbacks', '反馈数据'),
        ('feedback_templates', '反馈模板'),
        ('courses', '课程数据'),
        ('navigation_items', '导航项目'),
        ('email_templates', '邮件模板'),
        ('student_ratings', '学生评分')
    ]
    
    print("\n📊 数据表记录数:")
    total_success = 0
    for table, description in tables:
        success, count = run_sql_query(f"SELECT COUNT(*) FROM {table};", description)
        if success:
            total_success += 1
    
    # 检查具体数据
    print("\n🔍 数据详情:")
    
    # 检查成员数据
    success, _ = run_sql_query(
        "SELECT name FROM members LIMIT 3;",
        "成员名称列表"
    )
    
    # 检查活动数据
    success, _ = run_sql_query(
        "SELECT name_en FROM activities LIMIT 3;",
        "活动名称列表"
    )
    
    # 检查反馈模板
    success, _ = run_sql_query(
        "SELECT id FROM feedback_templates;",
        "反馈模板ID列表"
    )
    
    print(f"\n📊 验证结果: {total_success}/{len(tables)} 表验证成功")
    
    if total_success == len(tables):
        print("🎉 数据库验证完全成功！")
        print("\n✅ 确认事项:")
        print("   ✅ 所有数据表已创建")
        print("   ✅ 数据迁移成功")
        print("   ✅ 数据库连接正常")
        print("   ✅ 数据完整性验证通过")
        return True
    else:
        print("⚠️  部分验证失败")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
