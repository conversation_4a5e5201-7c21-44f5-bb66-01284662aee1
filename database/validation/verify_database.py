#!/usr/bin/env python3
"""
Verify database connection and data
"""

import os
import json
import psycopg2
from psycopg2.extras import RealDictCursor

def test_database_connection():
    """Test database connection"""
    database_url = "postgresql://snownavi:SnowNavi2025!@localhost:5432/snownavi"
    
    try:
        conn = psycopg2.connect(database_url, cursor_factory=RealDictCursor)
        cursor = conn.cursor()
        
        print("✅ Database connection successful!")
        
        # Get version
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"PostgreSQL version: {version['version']}")
        
        # List tables
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        tables = cursor.fetchall()
        
        print(f"\n📊 Found {len(tables)} tables:")
        for table in tables:
            print(f"  - {table['table_name']}")
        
        # Check data in key tables
        key_tables = ['members', 'activities', 'checkins', 'feedbacks']
        
        print(f"\n📈 Data counts:")
        for table in key_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table};")
                count = cursor.fetchone()
                print(f"  - {table}: {count['count']} records")
            except Exception as e:
                print(f"  - {table}: Error - {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def compare_json_vs_database():
    """Compare JSON data with database data"""
    print(f"\n🔍 Comparing JSON vs Database data:")
    
    # Load JSON data
    data_dir = 'data'
    json_files = ['members.json', 'activities.json', 'checkins.json', 'feedbacks.json']
    
    for json_file in json_files:
        file_path = os.path.join(data_dir, json_file)
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                print(f"  📄 {json_file}: {len(json_data)} records")
            except Exception as e:
                print(f"  📄 {json_file}: Error - {e}")
        else:
            print(f"  📄 {json_file}: File not found")

def test_data_access_layer():
    """Test our data access layer"""
    print(f"\n🧪 Testing Data Access Layer:")
    
    try:
        # Update the database URL in our data access layer
        os.environ['DATABASE_URL'] = "postgresql://snownavi:SnowNavi2025!@localhost:5432/snownavi"
        os.environ['DATA_MODE'] = 'database'
        
        from backend.database_access import DataAccessLayer
        
        # Test database mode
        dal = DataAccessLayer(mode='database')
        print("✅ DataAccessLayer initialized in database mode")
        
        # Test getting members
        try:
            members = dal.get_all('members')
            print(f"✅ Retrieved {len(members)} members from database")
        except Exception as e:
            print(f"⚠️  Members retrieval failed: {e}")
        
        # Test getting activities
        try:
            activities = dal.get_all('activities')
            print(f"✅ Retrieved {len(activities)} activities from database")
        except Exception as e:
            print(f"⚠️  Activities retrieval failed: {e}")
        
    except Exception as e:
        print(f"❌ Data access layer test failed: {e}")

def main():
    print("🚀 Database Verification and Migration Test")
    print("=" * 50)
    
    # Test 1: Database connection
    if not test_database_connection():
        print("❌ Database connection failed, cannot proceed")
        return False
    
    # Test 2: Compare data
    compare_json_vs_database()
    
    # Test 3: Test data access layer
    test_data_access_layer()
    
    print(f"\n✨ Verification completed!")
    
    return True

if __name__ == '__main__':
    main()
