#!/usr/bin/env python3
"""
最终迁移验证脚本
验证所有JSON文件的数据都已正确迁移到数据库
"""

import os
import sys
import json
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalMigrationVerifier:
    """最终迁移验证器"""
    
    def __init__(self, database_url=None):
        self.database_url = database_url or os.getenv('DATABASE_URL', 
            'postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi')
        self.data_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'data')
        self.conn = None
        
        # 定义验证映射
        self.verification_map = [
            ('members.json', 'members', 'Members'),
            ('activities.json', 'activities', 'Activities'),
            ('checkins.json', 'checkins', 'Checkins'),
            ('feedback_templates.json', 'feedback_templates', 'Feedback Templates'),
            ('feedbacks.json', 'feedbacks', 'Feedbacks'),
            ('courses.json', 'courses', 'Courses'),
            ('navigation.json', 'navigation_items', 'Navigation Items'),
            ('email_templates.json', 'email_templates', 'Email Templates'),
            ('student_ratings.json', 'student_ratings', 'Student Ratings')
        ]
        
    def connect_database(self):
        """连接数据库"""
        max_retries = 10
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                self.conn = psycopg2.connect(self.database_url, cursor_factory=RealDictCursor)
                logger.info("数据库连接成功")
                return True
            except psycopg2.OperationalError as e:
                retry_count += 1
                logger.warning(f"数据库连接尝试 {retry_count}/{max_retries} 失败: {e}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    logger.error("数据库连接失败")
                    return False
        return False
    
    def load_json_data(self, json_file):
        """加载JSON文件数据"""
        file_path = os.path.join(self.data_dir, json_file)
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return data
            else:
                logger.error(f"文件不存在: {file_path}")
                return None
        except Exception as e:
            logger.error(f"加载JSON文件失败 {json_file}: {e}")
            return None
    
    def get_table_count(self, table_name):
        """获取表中的记录数"""
        try:
            cursor = self.conn.cursor()
            cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
            return cursor.fetchone()['count']
        except Exception as e:
            logger.error(f"无法获取表 {table_name} 的记录数: {e}")
            return 0
    
    def get_expected_count(self, json_data, json_file):
        """获取JSON数据的预期记录数"""
        if json_data is None:
            return 0
        
        if json_file == 'navigation.json':
            # navigation.json有特殊结构
            return len(json_data.get('items', []))
        elif isinstance(json_data, dict):
            return len(json_data)
        elif isinstance(json_data, list):
            return len(json_data)
        else:
            return 0
    
    def verify_single_migration(self, json_file, table_name, display_name):
        """验证单个迁移"""
        logger.info(f"🔍 验证 {display_name} ({json_file} → {table_name})")
        
        # 加载JSON数据
        json_data = self.load_json_data(json_file)
        expected_count = self.get_expected_count(json_data, json_file)
        
        # 获取数据库记录数
        db_count = self.get_table_count(table_name)
        
        # 比较数量
        if db_count == expected_count:
            logger.info(f"✅ {display_name}: JSON {expected_count} 条 = DB {db_count} 条")
            return True
        else:
            logger.error(f"❌ {display_name}: JSON {expected_count} 条 ≠ DB {db_count} 条")
            return False
    
    def verify_data_integrity(self):
        """验证数据完整性"""
        logger.info("🔍 验证数据完整性...")
        
        cursor = self.conn.cursor()
        
        # 检查外键关系
        integrity_checks = [
            ("Checkins → Members", "SELECT COUNT(*) as count FROM checkins c LEFT JOIN members m ON c.member_id = m.id WHERE m.id IS NULL"),
            ("Checkins → Activities", "SELECT COUNT(*) as count FROM checkins c LEFT JOIN activities a ON c.activity_id = a.id WHERE a.id IS NULL"),
            ("Feedbacks → Checkins", "SELECT COUNT(*) as count FROM feedbacks f LEFT JOIN checkins c ON f.checkin_id = c.id WHERE c.id IS NULL"),
            ("Student Ratings → Feedbacks", "SELECT COUNT(*) as count FROM student_ratings sr LEFT JOIN feedbacks f ON sr.feedback_id = f.id WHERE f.id IS NULL")
        ]
        
        integrity_passed = 0
        integrity_failed = 0
        
        for check_name, query in integrity_checks:
            try:
                cursor.execute(query)
                orphan_count = cursor.fetchone()['count']
                
                if orphan_count == 0:
                    logger.info(f"✅ {check_name}: 无孤立记录")
                    integrity_passed += 1
                else:
                    logger.error(f"❌ {check_name}: {orphan_count} 条孤立记录")
                    integrity_failed += 1
            except Exception as e:
                logger.error(f"❌ {check_name}: 检查失败 - {e}")
                integrity_failed += 1
        
        logger.info(f"完整性检查结果: 通过 {integrity_passed}, 失败 {integrity_failed}")
        return integrity_failed == 0
    
    def get_database_summary(self):
        """获取数据库摘要"""
        logger.info("📊 数据库摘要:")
        
        cursor = self.conn.cursor()
        total_records = 0
        
        for _, table_name, display_name in self.verification_map:
            count = self.get_table_count(table_name)
            total_records += count
            logger.info(f"   {display_name:<20}: {count:>6} 条记录")
        
        logger.info(f"   {'总计':<20}: {total_records:>6} 条记录")
        return total_records
    
    def run_verification(self):
        """运行完整验证流程"""
        logger.info("🚀 开始最终迁移验证")
        logger.info("=" * 80)
        
        if not self.connect_database():
            return False
        
        try:
            # 验证每个迁移
            verification_passed = 0
            verification_failed = 0
            
            logger.info("📋 逐表验证:")
            for json_file, table_name, display_name in self.verification_map:
                if self.verify_single_migration(json_file, table_name, display_name):
                    verification_passed += 1
                else:
                    verification_failed += 1
            
            logger.info(f"\n📊 表验证结果: 通过 {verification_passed}, 失败 {verification_failed}")
            
            # 验证数据完整性
            logger.info("\n🔗 数据完整性验证:")
            integrity_ok = self.verify_data_integrity()
            
            # 获取数据库摘要
            logger.info("\n📊 数据库摘要:")
            total_records = self.get_database_summary()
            
            # 最终结果
            logger.info("\n" + "=" * 80)
            logger.info("📋 最终验证结果")
            logger.info("=" * 80)
            
            if verification_failed == 0 and integrity_ok:
                logger.info("🎉 所有迁移验证完全成功！")
                logger.info(f"✅ 成功迁移 {len(self.verification_map)} 个JSON文件")
                logger.info(f"✅ 总计 {total_records} 条记录")
                logger.info("✅ 数据完整性验证通过")
                logger.info("✅ 外键关系正确")
                return True
            else:
                logger.error("❌ 迁移验证存在问题")
                if verification_failed > 0:
                    logger.error(f"❌ {verification_failed} 个表验证失败")
                if not integrity_ok:
                    logger.error("❌ 数据完整性验证失败")
                return False
            
        except Exception as e:
            logger.error(f"❌ 验证过程中发生错误: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False
        finally:
            if self.conn:
                self.conn.close()

if __name__ == '__main__':
    verifier = FinalMigrationVerifier()
    success = verifier.run_verification()
    sys.exit(0 if success else 1)
