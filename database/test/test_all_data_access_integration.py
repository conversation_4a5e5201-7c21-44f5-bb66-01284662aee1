#!/usr/bin/env python3
"""
Test all data access layer integrations in server.py
"""

import requests
import json
import time

def test_members_api():
    """Test members API endpoints"""
    print("🧪 Testing Members API")
    print("-" * 30)
    
    try:
        # Test GET /data/members.json
        response = requests.get('http://localhost:8899/data/members.json')
        if response.status_code == 200:
            members = response.json()
            print(f"✅ GET /data/members.json: {len(members)} members")
        else:
            print(f"❌ GET /data/members.json failed: {response.status_code}")
            return False
        
        # Test GET /api/member/<id>
        if members:
            first_member_id = list(members.keys())[0]
            response = requests.get(f'http://localhost:8899/api/member/{first_member_id}')
            if response.status_code == 200:
                member = response.json()
                print(f"✅ GET /api/member/{first_member_id}: {member.get('name')}")
            else:
                print(f"❌ GET /api/member/{first_member_id} failed: {response.status_code}")
                return False
        
        # Test search by email
        test_email = "<EMAIL>"
        response = requests.post('http://localhost:8899/api/member/search-by-email', 
                               json={'email': test_email})
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Search by email: found={result.get('found')}")
        else:
            print(f"❌ Search by email failed: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Members API test failed: {e}")
        return False

def test_activities_api():
    """Test activities API endpoints"""
    print("\n🧪 Testing Activities API")
    print("-" * 30)
    
    try:
        # Test GET /data/activities.json
        response = requests.get('http://localhost:8899/data/activities.json')
        if response.status_code == 200:
            activities = response.json()
            print(f"✅ GET /data/activities.json: {len(activities)} activities")
            
            # Show sample activities
            for i, (activity_id, activity) in enumerate(activities.items()):
                if i < 2:  # Show first 2
                    print(f"   - {activity_id}: {activity.get('type')} on {activity.get('date')}")
            
            return True
        else:
            print(f"❌ GET /data/activities.json failed: {response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Activities API test failed: {e}")
        return False

def test_checkins_api():
    """Test checkins API endpoints"""
    print("\n🧪 Testing Checkins API")
    print("-" * 30)
    
    try:
        # Test GET /data/checkins.json
        response = requests.get('http://localhost:8899/data/checkins.json')
        if response.status_code == 200:
            checkins = response.json()
            print(f"✅ GET /data/checkins.json: {len(checkins)} checkins")
            
            # Show sample checkins
            for i, (checkin_id, checkin) in enumerate(checkins.items()):
                if i < 2:  # Show first 2
                    print(f"   - {checkin_id}: {checkin.get('memberType')} at {checkin.get('checkinTime')}")
            
            return True
        else:
            print(f"❌ GET /data/checkins.json failed: {response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Checkins API test failed: {e}")
        return False

def test_data_consistency():
    """Test data consistency between database and API"""
    print("\n🧪 Testing Data Consistency")
    print("-" * 30)
    
    try:
        # Get data from API
        members_response = requests.get('http://localhost:8899/data/members.json')
        activities_response = requests.get('http://localhost:8899/data/activities.json')
        checkins_response = requests.get('http://localhost:8899/data/checkins.json')
        
        if all(r.status_code == 200 for r in [members_response, activities_response, checkins_response]):
            api_members = len(members_response.json())
            api_activities = len(activities_response.json())
            api_checkins = len(checkins_response.json())
            
            print(f"✅ API Data Counts:")
            print(f"   - Members: {api_members}")
            print(f"   - Activities: {api_activities}")
            print(f"   - Checkins: {api_checkins}")
            
            # Check database directly
            import os
            import sys
            sys.path.append('backend')
            
            os.environ['DATABASE_URL'] = "postgresql://snownavi:SnowNavi2025!@localhost:5432/snownavi"
            os.environ['DATA_MODE'] = 'database'
            
            from database_access import DataAccessLayer
            
            dal = DataAccessLayer(mode='database')
            db_members = len(dal.get_all('members'))
            db_activities = len(dal.get_all('activities'))
            db_checkins = len(dal.get_all('checkins'))
            
            print(f"✅ Database Data Counts:")
            print(f"   - Members: {db_members}")
            print(f"   - Activities: {db_activities}")
            print(f"   - Checkins: {db_checkins}")
            
            # Check consistency
            if api_members == db_members and api_activities == db_activities and api_checkins == db_checkins:
                print("✅ Data consistency verified!")
                return True
            else:
                print("⚠️  Data inconsistency detected!")
                return False
        else:
            print("❌ Failed to get API data for consistency check")
            return False
        
    except Exception as e:
        print(f"❌ Data consistency test failed: {e}")
        return False

def test_database_mode_behavior():
    """Test that database mode doesn't modify JSON files"""
    print("\n🧪 Testing Database Mode Behavior")
    print("-" * 30)
    
    try:
        # Check current environment
        import os
        current_mode = os.getenv('DATA_MODE', 'unknown')
        print(f"📊 Current DATA_MODE: {current_mode}")
        
        # Get initial JSON file sizes
        json_files = ['data/members.json', 'data/activities.json', 'data/checkins.json']
        initial_sizes = {}
        
        for json_file in json_files:
            if os.path.exists(json_file):
                with open(json_file, 'r') as f:
                    data = json.load(f)
                    initial_sizes[json_file] = len(data)
            else:
                initial_sizes[json_file] = 0
        
        print(f"📄 Initial JSON file sizes: {initial_sizes}")
        
        # Make some API calls that would modify data
        print("📤 Making API calls...")
        
        # Try to get data (should work)
        members_response = requests.get('http://localhost:8899/data/members.json')
        activities_response = requests.get('http://localhost:8899/data/activities.json')
        
        if members_response.status_code == 200 and activities_response.status_code == 200:
            print("✅ API calls successful")
            
            # Check JSON file sizes again
            time.sleep(1)  # Give time for any file operations
            
            final_sizes = {}
            for json_file in json_files:
                if os.path.exists(json_file):
                    with open(json_file, 'r') as f:
                        data = json.load(f)
                        final_sizes[json_file] = len(data)
                else:
                    final_sizes[json_file] = 0
            
            print(f"📄 Final JSON file sizes: {final_sizes}")
            
            # Check if files were modified
            if initial_sizes == final_sizes:
                print("✅ JSON files unchanged - database mode working correctly!")
                return True
            else:
                print("⚠️  JSON files were modified - check database mode implementation")
                return False
        else:
            print("❌ API calls failed")
            return False
        
    except Exception as e:
        print(f"❌ Database mode behavior test failed: {e}")
        return False

def main():
    """Run all integration tests"""
    print("🚀 Data Access Layer Integration Test")
    print("=" * 50)
    print("Testing Flask server integration with data access layer...")
    print()
    
    tests = [
        ("Members API", test_members_api),
        ("Activities API", test_activities_api),
        ("Checkins API", test_checkins_api),
        ("Data Consistency", test_data_consistency),
        ("Database Mode Behavior", test_database_mode_behavior)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"💥 {test_name} test CRASHED: {e}")
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("\n✨ Data Access Layer Integration is working correctly!")
        print("   ✅ All APIs use data access layer")
        print("   ✅ Database mode works properly")
        print("   ✅ Data consistency maintained")
        print("   ✅ JSON files not modified in database mode")
    else:
        print("⚠️  Some tests failed.")
        print("\n🔧 Possible issues:")
        print("   - Some APIs still using direct JSON file access")
        print("   - Data access layer not fully integrated")
        print("   - Database mode configuration issues")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
