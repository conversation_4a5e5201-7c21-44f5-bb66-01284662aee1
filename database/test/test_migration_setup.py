#!/usr/bin/env python3
"""
Simple test to verify migration setup
"""

import os
import json

def main():
    print("🚀 SnowNavi Database Migration Setup Test")
    print("=" * 50)
    
    # Test 1: Check JSON data files
    print("🧪 Testing JSON data files...")
    data_dir = 'data'
    json_files = [
        'members.json', 'activities.json', 'checkins.json',
        'feedbacks.json', 'feedback_templates.json', 'email_templates.json',
        'navigation.json', 'courses.json', 'student_ratings.json'
    ]
    
    for json_file in json_files:
        file_path = os.path.join(data_dir, json_file)
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"✅ {json_file}: {len(data)} records")
            except Exception as e:
                print(f"❌ {json_file}: Error - {e}")
        else:
            print(f"⚠️  {json_file}: Not found")
    
    # Test 2: Check Docker files
    print("\n🧪 Testing Docker configuration...")
    docker_files = [
        'docker-compose.yml',
        'database/Dockerfile', 
        'database/schema.sql',
        'database/migrate.py',
        'database/test_connection.py'
    ]
    
    for file_path in docker_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}: Not found")
    
    # Test 3: Check backend files
    print("\n🧪 Testing backend files...")
    backend_files = [
        'backend/requirements.txt',
        'backend/database_access.py',
        'backend/.env.example'
    ]
    
    for file_path in backend_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}: Not found")
    
    print("\n✨ Setup verification completed!")
    print("\nNext steps:")
    print("1. Copy backend/.env.example to backend/.env and configure")
    print("2. Start database: docker compose up -d db")
    print("3. Test connection: python database/test_connection.py")
    print("4. Run migration: docker compose run --rm migrate")
    print("5. Switch to hybrid mode in .env: DATA_MODE=hybrid")

if __name__ == '__main__':
    main()
