#!/usr/bin/env python3
"""
检查数据库中的courses数据
"""

import sys
import os
import json
import logging

# 设置路径和环境变量
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
os.environ['DATA_MODE'] = "database"

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_courses_data():
    """检查数据库中的courses数据"""
    
    try:
        from database_access import DataAccessLayer
        from sqlalchemy import create_engine, text
        
        # 直接连接数据库查看原始数据
        engine = create_engine(os.environ['DATABASE_URL'])
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT id, course_data FROM courses"))
            
            logger.info("数据库中的courses记录:")
            for row in result:
                logger.info(f"ID: {row.id}")
                logger.info(f"Data type: {type(row.course_data)}")
                if isinstance(row.course_data, dict):
                    logger.info(f"Data keys: {list(row.course_data.keys())}")
                    logger.info(f"Data: {json.dumps(row.course_data, indent=2, ensure_ascii=False)}")
                else:
                    logger.info(f"Data: {row.course_data}")
                logger.info("-" * 50)
        
        # 使用数据访问层查看数据
        dal = DataAccessLayer(mode='database')
        courses_data = dal.get_all('courses')
        
        logger.info(f"\n通过数据访问层获取的courses数据:")
        logger.info(f"Type: {type(courses_data)}")
        logger.info(f"Length: {len(courses_data) if courses_data else 0}")
        
        if courses_data:
            logger.info(f"Keys: {list(courses_data.keys())}")
            logger.info(f"Data: {json.dumps(courses_data, indent=2, ensure_ascii=False)}")
        
        # 清理测试数据
        logger.info("\n清理数据库中的courses数据...")
        with engine.begin() as conn:
            conn.execute(text("DELETE FROM courses"))
        logger.info("✅ 数据清理完成")
        
    except Exception as e:
        logger.error(f"❌ 检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_courses_data()
