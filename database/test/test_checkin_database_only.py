#!/usr/bin/env python3
"""
Test checkin creation to verify it only uses database in database mode
"""

import os
import json
import requests
import time
from datetime import datetime

def backup_json_file():
    """Backup the current checkins.json file"""
    json_file = 'data/checkins.json'
    backup_file = f'data/checkins_backup_{int(time.time())}.json'
    
    if os.path.exists(json_file):
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"✅ Backed up {json_file} to {backup_file}")
        return backup_file, data
    else:
        print(f"⚠️  {json_file} does not exist")
        return None, {}

def get_json_file_content():
    """Get current content of checkins.json"""
    json_file = 'data/checkins.json'
    if os.path.exists(json_file):
        with open(json_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def test_checkin_creation():
    """Test checkin creation via API"""
    print("🧪 Testing Checkin Creation in Database Mode")
    print("=" * 50)
    
    # Get initial state
    initial_json = get_json_file_content()
    initial_count = len(initial_json)
    print(f"📄 Initial JSON checkins count: {initial_count}")
    
    # Create test checkin data with different activity to avoid duplicate
    test_checkin = {
        "activityId": "ACT20990001",  # Different activity
        "memberId": "SN20210001",
        "memberName": "Yujie Wang",
        "memberType": "coach",
        "checkinBy": "test_user",
        "checkinByEmail": "<EMAIL>",
        "notes": "Test checkin via API in database mode - final test"
    }
    
    print(f"📤 Creating checkin for member {test_checkin['memberId']}")
    
    # Send POST request to create checkin
    try:
        response = requests.post(
            'http://localhost:8899/api/checkin',
            json=test_checkin,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📥 API Response Status: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print(f"✅ Checkin created successfully")
            print(f"   Checkin ID: {result.get('checkin', {}).get('id')}")
            
            # Check if JSON file was modified
            time.sleep(1)  # Give a moment for file operations
            final_json = get_json_file_content()
            final_count = len(final_json)
            
            print(f"📄 Final JSON checkins count: {final_count}")
            
            if final_count > initial_count:
                print("❌ PROBLEM: JSON file was modified!")
                print("   This means the API is still writing to JSON in database mode")
                
                # Show the new entry
                new_entries = set(final_json.keys()) - set(initial_json.keys())
                for new_id in new_entries:
                    print(f"   New JSON entry: {new_id}")
                
                return False
            else:
                print("✅ GOOD: JSON file was NOT modified")
                print("   The API is correctly using only the database")
                return True
                
        else:
            print(f"❌ API request failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing checkin creation: {e}")
        return False

def test_checkin_retrieval():
    """Test checkin retrieval via API"""
    print("\n🧪 Testing Checkin Retrieval")
    print("=" * 30)
    
    try:
        response = requests.get('http://localhost:8899/data/checkins.json')
        
        if response.status_code == 200:
            checkins = response.json()
            print(f"✅ Retrieved {len(checkins)} checkins via API")
            
            # Show some sample checkins
            for i, (checkin_id, checkin) in enumerate(checkins.items()):
                if i < 3:  # Show first 3
                    print(f"   - {checkin_id}: {checkin.get('memberName')} ({checkin.get('memberType')})")
            
            return True
        else:
            print(f"❌ Failed to retrieve checkins: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error retrieving checkins: {e}")
        return False

def check_database_content():
    """Check database content directly"""
    print("\n🧪 Checking Database Content")
    print("=" * 30)
    
    try:
        import sys
        sys.path.append('backend')
        
        os.environ['DATABASE_URL'] = "postgresql://snownavi:SnowNavi2025!@localhost:5432/snownavi"
        os.environ['DATA_MODE'] = 'database'
        
        from database_access import DataAccessLayer
        
        dal = DataAccessLayer(mode='database')
        checkins = dal.get_all('checkins')
        
        print(f"✅ Database contains {len(checkins)} checkins")
        
        # Show recent checkins
        recent_checkins = []
        for checkin_id, checkin in checkins.items():
            if checkin.get('notes') and 'Test checkin via API' in checkin.get('notes', ''):
                recent_checkins.append((checkin_id, checkin))
        
        if recent_checkins:
            print(f"   Found {len(recent_checkins)} test checkins in database:")
            for checkin_id, checkin in recent_checkins[-3:]:  # Show last 3
                print(f"   - {checkin_id}: {checkin.get('memberName')} at {checkin.get('checkinTime')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Database Mode Checkin Test")
    print("Testing that checkin creation only uses database, not JSON files")
    print()
    
    # Backup current state
    backup_file, initial_data = backup_json_file()
    
    tests = [
        ("Checkin Creation", test_checkin_creation),
        ("Checkin Retrieval", test_checkin_retrieval),
        ("Database Content", check_database_content)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"💥 {test_name} test CRASHED: {e}")
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("\n✨ Checkin API is correctly using database mode!")
        print("   ✅ No JSON file modifications in database mode")
        print("   ✅ Data is stored in PostgreSQL database")
        print("   ✅ API retrieval works correctly")
    else:
        print("⚠️  Some tests failed.")
        if passed < total:
            print("\n🔧 Possible issues:")
            print("   - Flask server may still be using JSON file operations")
            print("   - Data access layer integration incomplete")
            print("   - Database mode not properly configured")
    
    if backup_file:
        print(f"\n📁 Backup file created: {backup_file}")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
