#!/usr/bin/env python3
"""
测试API接口在不同数据模式下的一致性
确保server.py的GET接口在database和json模式下返回相同的数据
"""

import os
import sys
import json
import logging
import requests
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_api_consistency():
    """测试API接口的一致性"""
    
    base_url = "http://127.0.0.1:8899"
    
    logger.info("🚀 开始测试API接口一致性")
    logger.info("=" * 80)
    
    # 测试的API端点
    test_endpoints = [
        '/data/members.json',
        '/data/activities.json',
        '/data/checkins.json',
        '/data/feedbacks.json',
        '/data/feedback_templates.json',
        '/data/courses.json',
        '/data/navigation.json',
        '/data/email_templates.json',
        '/data/student_ratings.json'
    ]
    
    results = {}
    
    for endpoint in test_endpoints:
        logger.info(f"\n🔍 测试端点: {endpoint}")
        
        try:
            # 发送GET请求
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    data_type = type(data).__name__
                    
                    if isinstance(data, dict):
                        count = len(data)
                        if endpoint == '/data/navigation.json' and 'items' in data:
                            count = len(data['items'])
                    elif isinstance(data, list):
                        count = len(data)
                    else:
                        count = 1
                    
                    logger.info(f"✅ {endpoint}: {response.status_code} - {data_type} - {count} 条记录")
                    results[endpoint] = {
                        'status': 'success',
                        'status_code': response.status_code,
                        'data_type': data_type,
                        'count': count,
                        'sample_data': str(data)[:200] + '...' if len(str(data)) > 200 else str(data)
                    }
                    
                except json.JSONDecodeError as e:
                    logger.error(f"❌ {endpoint}: JSON解析失败 - {e}")
                    results[endpoint] = {
                        'status': 'json_error',
                        'error': str(e)
                    }
            else:
                logger.error(f"❌ {endpoint}: HTTP {response.status_code}")
                results[endpoint] = {
                    'status': 'http_error',
                    'status_code': response.status_code,
                    'error': response.text[:200]
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ {endpoint}: 请求失败 - {e}")
            results[endpoint] = {
                'status': 'request_error',
                'error': str(e)
            }
    
    # 输出总结
    logger.info(f"\n📊 API测试总结:")
    success_count = sum(1 for r in results.values() if r.get('status') == 'success')
    total_count = len(results)
    
    logger.info(f"  成功: {success_count}/{total_count}")
    logger.info(f"  失败: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        logger.info("🎉 所有API端点都正常工作！")
        return True
    else:
        logger.error("❌ 部分API端点存在问题")
        return False

def test_members_api_specifically():
    """专门测试members API的数据一致性"""
    
    base_url = "http://127.0.0.1:8899"
    
    logger.info("\n🔍 专门测试Members API数据一致性")
    logger.info("-" * 60)
    
    try:
        # 获取API数据
        response = requests.get(f"{base_url}/data/members.json", timeout=10)
        
        if response.status_code != 200:
            logger.error(f"❌ API请求失败: HTTP {response.status_code}")
            return False
        
        api_data = response.json()
        logger.info(f"✅ API返回 {len(api_data)} 条成员记录")
        
        # 直接读取JSON文件进行对比
        json_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'members.json')
        with open(json_file_path, 'r', encoding='utf-8') as f:
            file_data = json.load(f)
        
        logger.info(f"✅ JSON文件包含 {len(file_data)} 条成员记录")
        
        # 对比数据
        if len(api_data) != len(file_data):
            logger.error(f"❌ 数据数量不一致: API={len(api_data)}, File={len(file_data)}")
            return False
        
        # 检查几个关键成员
        test_members = ['SN20210001', 'SN20230001', 'SN20250001']
        
        for member_id in test_members:
            if member_id in api_data and member_id in file_data:
                api_member = api_data[member_id]
                file_member = file_data[member_id]
                
                # 对比关键字段
                key_fields = ['name', 'email', 'isActive', 'validityPeriod', 'gender']
                
                match = True
                for field in key_fields:
                    api_val = api_member.get(field)
                    file_val = file_member.get(field)
                    
                    if api_val != file_val:
                        logger.error(f"❌ {member_id}.{field}: API='{api_val}' vs File='{file_val}'")
                        match = False
                
                if match:
                    logger.info(f"✅ {member_id}: API和文件数据完全匹配")
                else:
                    logger.error(f"❌ {member_id}: 数据不匹配")
                    return False
        
        logger.info("🎉 Members API数据完全一致！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试Members API时发生错误: {e}")
        return False

def check_server_status():
    """检查服务器状态"""
    
    base_url = "http://127.0.0.1:8899"
    
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            logger.info("✅ 服务器运行正常")
            return True
        else:
            logger.error(f"❌ 服务器响应异常: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ 无法连接到服务器: {e}")
        logger.error("请确保Flask服务器正在运行在 http://127.0.0.1:8899")
        return False

if __name__ == '__main__':
    # 检查服务器状态
    if not check_server_status():
        logger.error("请先启动Flask服务器: python backend/server.py")
        sys.exit(1)
    
    # 运行API一致性测试
    api_success = test_api_consistency()
    
    # 专门测试Members API
    members_success = test_members_api_specifically()
    
    overall_success = api_success and members_success
    
    if overall_success:
        logger.info("\n🎊 所有API测试通过！数据完全一致！")
    else:
        logger.error("\n💥 API测试存在问题，请检查日志")
    
    sys.exit(0 if overall_success else 1)
