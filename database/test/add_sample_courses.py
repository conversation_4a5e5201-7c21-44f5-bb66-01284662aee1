#!/usr/bin/env python3
"""
添加示例课程数据用于测试
"""

import sys
import os
import json
import logging
import requests

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_sample_courses():
    """添加示例课程数据"""
    
    sample_courses = {
        "beginner": {
            "id": "beginner",
            "en": {
                "title": "Beginner Snowboard Course",
                "description": "Perfect for first-time snowboarders. Learn the basics of snowboarding in a safe and fun environment.",
                "image": "assets/picture/courses/beginner.jpg",
                "pdf": "assets/pdf/beginner-guide.pdf"
            },
            "zh": {
                "title": "初级滑雪板课程",
                "description": "适合初次接触滑雪板的学员。在安全有趣的环境中学习滑雪板基础知识。",
                "image": "assets/picture/courses/beginner.jpg",
                "pdf": "assets/pdf/beginner-guide-zh.pdf"
            },
            "nl": {
                "title": "Beginners Snowboard Cursus",
                "description": "Perfect voor eerste keer snowboarders. <PERSON><PERSON> de basis van snowboarden in een veilige en leuke omgeving.",
                "image": "assets/picture/courses/beginner.jpg",
                "pdf": "assets/pdf/beginner-guide-nl.pdf"
            },
            "subcourses": {
                "beginner-basics": {
                    "id": "beginner-basics",
                    "en": {
                        "title": "Basic Techniques",
                        "desc": "Learn fundamental snowboarding techniques",
                        "image": "assets/picture/subcourses/basics.jpg",
                        "pdf": "assets/pdf/basic-techniques.pdf"
                    },
                    "zh": {
                        "title": "基础技巧",
                        "desc": "学习滑雪板基础技巧",
                        "image": "assets/picture/subcourses/basics.jpg",
                        "pdf": "assets/pdf/basic-techniques-zh.pdf"
                    },
                    "nl": {
                        "title": "Basis Technieken",
                        "desc": "Leer fundamentele snowboard technieken",
                        "image": "assets/picture/subcourses/basics.jpg",
                        "pdf": "assets/pdf/basic-techniques-nl.pdf"
                    }
                }
            }
        },
        "intermediate": {
            "id": "intermediate",
            "en": {
                "title": "Intermediate Snowboard Course",
                "description": "For snowboarders with some experience. Improve your technique and learn new skills.",
                "image": "assets/picture/courses/intermediate.jpg",
                "pdf": "assets/pdf/intermediate-guide.pdf"
            },
            "zh": {
                "title": "中级滑雪板课程",
                "description": "适合有一定经验的滑雪板手。提高技巧并学习新技能。",
                "image": "assets/picture/courses/intermediate.jpg",
                "pdf": "assets/pdf/intermediate-guide-zh.pdf"
            },
            "nl": {
                "title": "Gevorderde Snowboard Cursus",
                "description": "Voor snowboarders met wat ervaring. Verbeter je techniek en leer nieuwe vaardigheden.",
                "image": "assets/picture/courses/intermediate.jpg",
                "pdf": "assets/pdf/intermediate-guide-nl.pdf"
            },
            "subcourses": {
                "intermediate-carving": {
                    "id": "intermediate-carving",
                    "en": {
                        "title": "Carving Techniques",
                        "desc": "Master the art of carving turns",
                        "image": "assets/picture/subcourses/carving.jpg",
                        "pdf": "assets/pdf/carving-techniques.pdf"
                    },
                    "zh": {
                        "title": "刻滑技巧",
                        "desc": "掌握刻滑转弯的艺术",
                        "image": "assets/picture/subcourses/carving.jpg",
                        "pdf": "assets/pdf/carving-techniques-zh.pdf"
                    },
                    "nl": {
                        "title": "Carving Technieken",
                        "desc": "Beheers de kunst van carving bochten",
                        "image": "assets/picture/subcourses/carving.jpg",
                        "pdf": "assets/pdf/carving-techniques-nl.pdf"
                    }
                }
            }
        },
        "advanced": {
            "id": "advanced",
            "en": {
                "title": "Advanced Snowboard Course",
                "description": "For expert snowboarders. Challenge yourself with advanced techniques and freestyle elements.",
                "image": "assets/picture/courses/advanced.jpg",
                "pdf": "assets/pdf/advanced-guide.pdf"
            },
            "zh": {
                "title": "高级滑雪板课程",
                "description": "适合专业滑雪板手。挑战高级技巧和自由式元素。",
                "image": "assets/picture/courses/advanced.jpg",
                "pdf": "assets/pdf/advanced-guide-zh.pdf"
            },
            "nl": {
                "title": "Expert Snowboard Cursus",
                "description": "Voor expert snowboarders. Daag jezelf uit met geavanceerde technieken en freestyle elementen.",
                "image": "assets/picture/courses/advanced.jpg",
                "pdf": "assets/pdf/advanced-guide-nl.pdf"
            },
            "subcourses": {
                "advanced-freestyle": {
                    "id": "advanced-freestyle",
                    "en": {
                        "title": "Freestyle & Tricks",
                        "desc": "Learn advanced freestyle tricks and jumps",
                        "image": "assets/picture/subcourses/freestyle.jpg",
                        "pdf": "assets/pdf/freestyle-tricks.pdf"
                    },
                    "zh": {
                        "title": "自由式与技巧",
                        "desc": "学习高级自由式技巧和跳跃",
                        "image": "assets/picture/subcourses/freestyle.jpg",
                        "pdf": "assets/pdf/freestyle-tricks-zh.pdf"
                    },
                    "nl": {
                        "title": "Freestyle & Tricks",
                        "desc": "Leer geavanceerde freestyle tricks en sprongen",
                        "image": "assets/picture/subcourses/freestyle.jpg",
                        "pdf": "assets/pdf/freestyle-tricks-nl.pdf"
                    }
                }
            }
        }
    }
    
    try:
        logger.info("📤 添加示例课程数据...")
        
        response = requests.post(
            'http://127.0.0.1:8899/data/courses.json',
            json=sample_courses,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            logger.info("✅ 示例课程数据添加成功")
            logger.info(f"   添加了 {len(sample_courses)} 个课程类别")
            
            # 验证数据
            verify_response = requests.get('http://127.0.0.1:8899/data/courses.json')
            if verify_response.status_code == 200:
                verify_data = verify_response.json()
                logger.info(f"   验证: 数据库中现有 {len(verify_data)} 个课程")
                for course_id, course_data in verify_data.items():
                    logger.info(f"   - {course_id}: {course_data['en']['title']}")
            
            return True
        else:
            logger.error(f"❌ 添加示例课程数据失败: {response.status_code}")
            logger.error(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 添加示例课程数据时出错: {e}")
        return False

if __name__ == "__main__":
    if add_sample_courses():
        logger.info("🎉 示例课程数据添加完成！")
        sys.exit(0)
    else:
        logger.error("❌ 示例课程数据添加失败！")
        sys.exit(1)
