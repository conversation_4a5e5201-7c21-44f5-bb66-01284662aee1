#!/usr/bin/env python3
"""
Test CRUD operations (Create, Read, Update, Delete) for the data access layer
"""

import requests
import json
import time
import os
import sys

def test_member_crud():
    """Test complete CRUD operations for members"""
    print("🧪 Testing Member CRUD Operations")
    print("-" * 40)
    
    # Test data
    test_member_id = "SN20990001"
    test_member = {
        "id": test_member_id,
        "name": "Test User CRUD",
        "email": "<EMAIL>",
        "isActive": True,
        "validityPeriod": "01/01/2025 - 31/12/2025"
    }
    
    try:
        # 1. CREATE - Test member creation
        print(f"1️⃣ Creating member {test_member_id}...")
        response = requests.post('http://localhost:8899/api/member', json=test_member)
        
        if response.status_code == 201:
            result = response.json()
            print(f"✅ Member created successfully: {result.get('id')}")
        else:
            print(f"❌ Member creation failed: {response.status_code} - {response.text}")
            return False
        
        # 2. READ - Test member retrieval
        print(f"2️⃣ Reading member {test_member_id}...")
        response = requests.get(f'http://localhost:8899/api/member/{test_member_id}')
        
        if response.status_code == 200:
            member = response.json()
            print(f"✅ Member retrieved: {member.get('name')} ({member.get('email')})")
        else:
            print(f"❌ Member retrieval failed: {response.status_code}")
            return False
        
        # 3. UPDATE - Test member update
        print(f"3️⃣ Updating member {test_member_id}...")
        updated_member = test_member.copy()
        updated_member['name'] = "Test User CRUD Updated"
        updated_member['email'] = "<EMAIL>"
        updated_member['validityPeriod'] = {"start": "01/01/2025", "end": "31/12/2025"}  # Different format
        
        response = requests.put(f'http://localhost:8899/api/member/{test_member_id}', json=updated_member)
        
        if response.status_code == 200:
            print(f"✅ Member updated successfully")
            
            # Verify the update
            response = requests.get(f'http://localhost:8899/api/member/{test_member_id}')
            if response.status_code == 200:
                updated_data = response.json()
                if updated_data.get('name') == "Test User CRUD Updated":
                    print(f"✅ Update verified: {updated_data.get('name')}")
                else:
                    print(f"⚠️  Update not reflected: {updated_data.get('name')}")
            else:
                print(f"⚠️  Could not verify update")
        else:
            print(f"❌ Member update failed: {response.status_code} - {response.text}")
            return False
        
        # 4. DELETE - Test member deletion
        print(f"4️⃣ Deleting member {test_member_id}...")
        response = requests.delete(f'http://localhost:8899/api/member/{test_member_id}')
        
        if response.status_code == 200:
            print(f"✅ Member deleted successfully")
            
            # Verify the deletion
            response = requests.get(f'http://localhost:8899/api/member/{test_member_id}')
            if response.status_code == 404:
                print(f"✅ Deletion verified: member not found")
            else:
                print(f"⚠️  Deletion not complete: member still exists")
                return False
        else:
            print(f"❌ Member deletion failed: {response.status_code} - {response.text}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Member CRUD test failed: {e}")
        return False

def test_database_vs_json_consistency():
    """Test that database operations don't affect JSON files"""
    print("\n🧪 Testing Database vs JSON Consistency")
    print("-" * 40)
    
    try:
        # Get initial JSON file content
        json_file = 'data/members.json'
        initial_json = {}
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                initial_json = json.load(f)
        
        initial_count = len(initial_json)
        print(f"📄 Initial JSON members count: {initial_count}")
        
        # Get initial database content
        sys.path.append('backend')
        os.environ['DATABASE_URL'] = "postgresql://snownavi:SnowNavi2025!@localhost:5432/snownavi"
        os.environ['DATA_MODE'] = 'database'
        
        from database_access import DataAccessLayer
        dal = DataAccessLayer(mode='database')
        
        initial_db = dal.get_all('members')
        initial_db_count = len(initial_db)
        print(f"🗄️  Initial database members count: {initial_db_count}")
        
        # Perform CRUD operations via API
        test_member_id = "SN20990002"
        test_member = {
            "id": test_member_id,
            "name": "Consistency Test User",
            "email": "<EMAIL>",
            "isActive": True,
            "validityPeriod": "01/01/2025 - 31/12/2025"
        }
        
        # Create member
        response = requests.post('http://localhost:8899/api/member', json=test_member)
        if response.status_code != 201:
            print(f"❌ Failed to create test member: {response.status_code}")
            return False
        
        # Check JSON file (should be unchanged)
        time.sleep(1)
        final_json = {}
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                final_json = json.load(f)
        
        final_json_count = len(final_json)
        print(f"📄 Final JSON members count: {final_json_count}")
        
        # Check database (should have new member)
        final_db = dal.get_all('members')
        final_db_count = len(final_db)
        print(f"🗄️  Final database members count: {final_db_count}")
        
        # Clean up - delete test member
        requests.delete(f'http://localhost:8899/api/member/{test_member_id}')
        
        # Verify consistency
        if final_json_count == initial_count and final_db_count == initial_db_count + 1:
            print("✅ Consistency verified:")
            print("   - JSON file unchanged (database mode working)")
            print("   - Database correctly updated")
            return True
        else:
            print("❌ Consistency check failed:")
            print(f"   - JSON: {initial_count} -> {final_json_count} (should be unchanged)")
            print(f"   - Database: {initial_db_count} -> {final_db_count} (should be +1)")
            return False
        
    except Exception as e:
        print(f"❌ Consistency test failed: {e}")
        return False

def test_direct_database_operations():
    """Test direct database operations"""
    print("\n🧪 Testing Direct Database Operations")
    print("-" * 40)
    
    try:
        sys.path.append('backend')
        os.environ['DATABASE_URL'] = "postgresql://snownavi:SnowNavi2025!@localhost:5432/snownavi"
        os.environ['DATA_MODE'] = 'database'
        
        from database_access import DataAccessLayer
        dal = DataAccessLayer(mode='database')
        
        # Test data
        test_member_id = "SN20990003"
        test_member = {
            "id": test_member_id,
            "name": "Direct DB Test User",
            "email": "<EMAIL>",
            "isActive": True,
            "validityPeriod": "01/01/2025 - 31/12/2025"
        }
        
        # 1. CREATE
        print(f"1️⃣ Direct create: {test_member_id}")
        success = dal.create('members', test_member_id, test_member)
        if success:
            print("✅ Direct create successful")
        else:
            print("❌ Direct create failed")
            return False
        
        # 2. READ
        print(f"2️⃣ Direct read: {test_member_id}")
        member = dal.get_by_id('members', test_member_id)
        if member and member.get('name') == test_member['name']:
            print(f"✅ Direct read successful: {member.get('name')}")
        else:
            print("❌ Direct read failed")
            return False
        
        # 3. UPDATE
        print(f"3️⃣ Direct update: {test_member_id}")
        updated_data = test_member.copy()
        updated_data['name'] = "Direct DB Test User Updated"
        success = dal.update('members', test_member_id, updated_data)
        if success:
            print("✅ Direct update successful")
            
            # Verify update
            member = dal.get_by_id('members', test_member_id)
            if member and member.get('name') == "Direct DB Test User Updated":
                print("✅ Update verified")
            else:
                print("⚠️  Update not verified")
        else:
            print("❌ Direct update failed")
            return False
        
        # 4. DELETE
        print(f"4️⃣ Direct delete: {test_member_id}")
        success = dal.delete('members', test_member_id)
        if success:
            print("✅ Direct delete successful")
            
            # Verify deletion
            member = dal.get_by_id('members', test_member_id)
            if not member:
                print("✅ Deletion verified")
            else:
                print("⚠️  Deletion not verified")
                return False
        else:
            print("❌ Direct delete failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Direct database operations test failed: {e}")
        return False

def main():
    """Run all CRUD tests"""
    print("🚀 CRUD Operations Test Suite")
    print("=" * 50)
    print("Testing Create, Read, Update, Delete operations...")
    print()
    
    tests = [
        ("Member CRUD via API", test_member_crud),
        ("Database vs JSON Consistency", test_database_vs_json_consistency),
        ("Direct Database Operations", test_direct_database_operations)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"💥 {test_name} test CRASHED: {e}")
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL CRUD TESTS PASSED!")
        print("\n✨ CRUD Operations are working correctly!")
        print("   ✅ Create operations work via API and direct DB")
        print("   ✅ Read operations work via API and direct DB")
        print("   ✅ Update operations work via API and direct DB")
        print("   ✅ Delete operations work via API and direct DB")
        print("   ✅ Database mode doesn't affect JSON files")
        print("   ✅ Data consistency maintained")
    else:
        print("⚠️  Some CRUD tests failed.")
        print("\n🔧 Possible issues:")
        print("   - Update/Delete methods not properly implemented")
        print("   - Database operations not working correctly")
        print("   - API integration incomplete")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
