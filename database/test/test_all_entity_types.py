#!/usr/bin/env python3
"""
Test all implemented entity types in the data access layer
"""

import requests
import json
import os
import sys

def test_entity_type_api(entity_type, endpoint):
    """Test a specific entity type API endpoint"""
    print(f"🧪 Testing {entity_type}")
    print("-" * 30)
    
    try:
        # Test GET endpoint
        response = requests.get(f'http://localhost:8899{endpoint}')
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, dict):
                count = len(data) if entity_type != 'navigation' else len(data.get('items', []))
            else:
                count = len(data)
            print(f"✅ GET {endpoint}: {count} records")
            
            # Show sample data structure
            if isinstance(data, dict) and data:
                if entity_type == 'navigation':
                    if 'items' in data and data['items']:
                        sample = data['items'][0]
                        print(f"   Sample item: {sample.get('id', 'N/A')}")
                else:
                    first_key = list(data.keys())[0]
                    sample = data[first_key]
                    print(f"   Sample record: {first_key}")
                    if isinstance(sample, dict):
                        print(f"   Fields: {list(sample.keys())[:5]}...")
            
            return True
        else:
            print(f"❌ GET {endpoint} failed: {response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ {entity_type} API test failed: {e}")
        return False

def test_database_vs_json_consistency():
    """Test that database mode doesn't affect JSON files"""
    print("\n🧪 Testing Database vs JSON Consistency")
    print("=" * 50)
    
    try:
        # Get initial JSON file counts
        json_files = {
            'feedbacks': 'data/feedbacks.json',
            'feedback_templates': 'data/feedback_templates.json',
            'courses': 'data/courses.json',
            'navigation': 'data/navigation.json'
        }
        
        initial_counts = {}
        for entity_type, json_file in json_files.items():
            if os.path.exists(json_file):
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if entity_type == 'navigation':
                        initial_counts[entity_type] = len(data.get('items', []))
                    else:
                        initial_counts[entity_type] = len(data)
            else:
                initial_counts[entity_type] = 0
        
        print(f"📄 Initial JSON file counts: {initial_counts}")
        
        # Get database counts
        sys.path.append('backend')
        os.environ['DATABASE_URL'] = "postgresql://snownavi:SnowNavi2025!@localhost:5432/snownavi"
        os.environ['DATA_MODE'] = 'database'
        
        from database_access import DataAccessLayer
        dal = DataAccessLayer(mode='database')
        
        db_counts = {}
        for entity_type in json_files.keys():
            try:
                data = dal.get_all(entity_type)
                if entity_type == 'navigation':
                    db_counts[entity_type] = len(data.get('items', []))
                else:
                    db_counts[entity_type] = len(data)
            except Exception as e:
                print(f"⚠️  Could not get {entity_type} from database: {e}")
                db_counts[entity_type] = 0
        
        print(f"🗄️  Database counts: {db_counts}")
        
        # Make API calls to test that JSON files aren't modified
        api_endpoints = {
            'feedbacks': '/data/feedbacks.json',
            'feedback_templates': '/data/feedback_templates.json',
            'courses': '/data/courses.json',
            'navigation': '/data/navigation.json'
        }
        
        print("\n📤 Making API calls...")
        for entity_type, endpoint in api_endpoints.items():
            response = requests.get(f'http://localhost:8899{endpoint}')
            if response.status_code == 200:
                print(f"  ✅ {entity_type}: API call successful")
            else:
                print(f"  ❌ {entity_type}: API call failed ({response.status_code})")
        
        # Check JSON files again
        final_counts = {}
        for entity_type, json_file in json_files.items():
            if os.path.exists(json_file):
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if entity_type == 'navigation':
                        final_counts[entity_type] = len(data.get('items', []))
                    else:
                        final_counts[entity_type] = len(data)
            else:
                final_counts[entity_type] = 0
        
        print(f"📄 Final JSON file counts: {final_counts}")
        
        # Check consistency
        if initial_counts == final_counts:
            print("✅ JSON files unchanged - database mode working correctly!")
            return True
        else:
            print("⚠️  JSON files were modified - check database mode implementation")
            print(f"   Changes: {set(initial_counts.items()) ^ set(final_counts.items())}")
            return False
        
    except Exception as e:
        print(f"❌ Consistency test failed: {e}")
        return False

def test_direct_database_access():
    """Test direct database access for all entity types"""
    print("\n🧪 Testing Direct Database Access")
    print("=" * 50)
    
    try:
        sys.path.append('backend')
        os.environ['DATABASE_URL'] = "postgresql://snownavi:SnowNavi2025!@localhost:5432/snownavi"
        os.environ['DATA_MODE'] = 'database'
        
        from database_access import DataAccessLayer
        dal = DataAccessLayer(mode='database')
        
        entity_types = ['members', 'activities', 'checkins', 'feedbacks', 'feedback_templates', 'courses', 'navigation', 'email_templates']
        
        results = {}
        for entity_type in entity_types:
            try:
                data = dal.get_all(entity_type)
                if entity_type == 'navigation':
                    count = len(data.get('items', []))
                else:
                    count = len(data)
                results[entity_type] = count
                print(f"✅ {entity_type}: {count} records")
            except Exception as e:
                results[entity_type] = f"Error: {e}"
                print(f"❌ {entity_type}: {e}")
        
        # Summary
        successful = sum(1 for v in results.values() if isinstance(v, int))
        total = len(entity_types)
        print(f"\n📊 Database access: {successful}/{total} entity types working")
        
        return successful == total
        
    except Exception as e:
        print(f"❌ Direct database access test failed: {e}")
        return False

def main():
    """Run all entity type tests"""
    print("🚀 All Entity Types Integration Test")
    print("=" * 60)
    print("Testing all implemented entity types...")
    print()
    
    # Test API endpoints
    api_tests = [
        ('feedbacks', '/data/feedbacks.json'),
        ('feedback_templates', '/data/feedback_templates.json'),
        ('courses', '/data/courses.json'),
        ('navigation', '/data/navigation.json'),
        ('members', '/data/members.json'),
        ('activities', '/data/activities.json'),
        ('checkins', '/data/checkins.json')
    ]
    
    api_passed = 0
    for entity_type, endpoint in api_tests:
        if test_entity_type_api(entity_type, endpoint):
            api_passed += 1
        print()
    
    # Test consistency and database access
    other_tests = [
        ("Database vs JSON Consistency", test_database_vs_json_consistency),
        ("Direct Database Access", test_direct_database_access)
    ]
    
    other_passed = 0
    for test_name, test_func in other_tests:
        try:
            if test_func():
                other_passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"💥 {test_name} test CRASHED: {e}")
        print()
    
    total_api = len(api_tests)
    total_other = len(other_tests)
    total_passed = api_passed + other_passed
    total_tests = total_api + total_other
    
    print("=" * 60)
    print(f"📊 Test Results: {total_passed}/{total_tests} tests passed")
    print(f"   API Tests: {api_passed}/{total_api}")
    print(f"   Other Tests: {other_passed}/{total_other}")
    
    if total_passed == total_tests:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✨ All Entity Types are working correctly!")
        print("   ✅ All APIs use data access layer")
        print("   ✅ Database mode works properly")
        print("   ✅ JSON files not modified in database mode")
        print("   ✅ All entity types implemented")
        
        print("\n📋 Implemented Entity Types:")
        implemented = ['members', 'activities', 'checkins', 'feedbacks', 'feedback_templates', 'courses', 'navigation', 'email_templates']
        for entity_type in implemented:
            print(f"   ✅ {entity_type}")
    else:
        print("\n⚠️  Some tests failed.")
        print("\n🔧 Possible issues:")
        print("   - Some entity types not fully implemented")
        print("   - Database operations not working correctly")
        print("   - API integration incomplete")
    
    return total_passed == total_tests

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
