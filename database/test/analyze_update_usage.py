#!/usr/bin/env python3
"""
分析server.py中update操作的使用情况
确保所有update操作都使用database接口而不是直接的JSON文件操作
"""

import os
import sys
import re
import logging
from collections import defaultdict

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_server_update_usage():
    """分析server.py中的update操作使用情况"""
    
    server_file = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'server.py')
    
    try:
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        logger.info("🔍 分析server.py中的update操作使用情况")
        logger.info("=" * 80)
        
        # 定义要检查的数据实体
        entities = [
            'members', 'activities', 'checkins', 'feedbacks', 
            'feedback_templates', 'student_ratings', 'email_templates', 
            'courses', 'navigation'
        ]
        
        # 分析结果
        results = {
            'using_data_access': defaultdict(list),
            'using_json_operations': defaultdict(list),
            'routes_found': defaultdict(list)
        }
        
        # 1. 查找所有路由定义
        route_pattern = r'@app\.route\([\'"]([^\'"]+)[\'"].*?methods=\[[\'"]([^\'"]+)[\'"]'
        routes = re.findall(route_pattern, content, re.MULTILINE)
        
        logger.info(f"📋 找到 {len(routes)} 个路由定义")
        
        # 2. 分析每个实体的update操作
        for entity in entities:
            logger.info(f"\n🔍 分析 {entity} 的update操作:")
            
            # 查找相关路由
            entity_routes = []
            for route_path, method in routes:
                if entity in route_path or entity.replace('_', '-') in route_path:
                    entity_routes.append((route_path, method))
                    results['routes_found'][entity].append(f"{method} {route_path}")
            
            if entity_routes:
                logger.info(f"  找到 {len(entity_routes)} 个相关路由:")
                for route_path, method in entity_routes:
                    logger.info(f"    {method} {route_path}")
            else:
                logger.info(f"  ❌ 未找到相关路由")
            
            # 3. 查找data_access.update()的使用
            data_access_pattern = rf'data_access\.update\([\'"]({entity})[\'"]'
            data_access_matches = re.findall(data_access_pattern, content)
            
            if data_access_matches:
                results['using_data_access'][entity] = data_access_matches
                logger.info(f"  ✅ 使用data_access.update(): {len(data_access_matches)} 次")
            else:
                logger.info(f"  ❌ 未使用data_access.update()")
            
            # 4. 查找直接JSON文件操作
            json_patterns = [
                rf'json\.dump\([^,]+,\s*open\([^)]*{entity}\.json',
                rf'with\s+open\([^)]*{entity}\.json[^)]*,\s*[\'"]w[\'"]',
                rf'open\([^)]*{entity}\.json[^)]*,\s*[\'"]w[\'"]'
            ]
            
            json_operations = []
            for pattern in json_patterns:
                matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
                json_operations.extend(matches)
            
            if json_operations:
                results['using_json_operations'][entity] = json_operations
                logger.info(f"  ⚠️  发现直接JSON操作: {len(json_operations)} 次")
            else:
                logger.info(f"  ✅ 未发现直接JSON操作")
        
        # 5. 生成总结报告
        logger.info(f"\n📊 总结报告:")
        logger.info("=" * 60)
        
        using_data_access_count = len([e for e in entities if results['using_data_access'][e]])
        using_json_count = len([e for e in entities if results['using_json_operations'][e]])
        
        logger.info(f"✅ 使用data_access.update()的实体: {using_data_access_count}/{len(entities)}")
        logger.info(f"⚠️  仍使用JSON操作的实体: {using_json_count}/{len(entities)}")
        
        # 详细列出使用data_access的实体
        if results['using_data_access']:
            logger.info(f"\n✅ 正确使用data_access.update()的实体:")
            for entity, matches in results['using_data_access'].items():
                logger.info(f"  - {entity}: {len(matches)} 次调用")
        
        # 详细列出仍使用JSON操作的实体
        if results['using_json_operations']:
            logger.info(f"\n⚠️  仍使用直接JSON操作的实体:")
            for entity, operations in results['using_json_operations'].items():
                logger.info(f"  - {entity}: {len(operations)} 次JSON操作")
        
        # 列出没有找到任何update操作的实体
        no_update_entities = []
        for entity in entities:
            if not results['using_data_access'][entity] and not results['using_json_operations'][entity]:
                no_update_entities.append(entity)
        
        if no_update_entities:
            logger.info(f"\n❓ 未找到update操作的实体:")
            for entity in no_update_entities:
                logger.info(f"  - {entity}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ 分析过程中发生错误: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return None

def find_specific_update_patterns():
    """查找特定的update模式"""
    
    server_file = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'server.py')
    
    try:
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        logger.info(f"\n🔍 查找特定的update模式:")
        logger.info("-" * 60)
        
        # 1. 查找POST路由中的批量更新操作
        post_routes_pattern = r'@app\.route\([\'"]([^\'"]*\.json)[\'"].*?methods=\[[\'"]POST[\'"].*?def\s+(\w+)'
        post_routes = re.findall(post_routes_pattern, content, re.MULTILINE | re.DOTALL)
        
        logger.info(f"📋 找到 {len(post_routes)} 个POST路由:")
        for route_path, func_name in post_routes:
            logger.info(f"  - {route_path} -> {func_name}()")
        
        # 2. 查找管理页面的update操作
        admin_update_pattern = r'@app\.route\([\'"]([^\'"]*/admin/[^\'"]*).*?def\s+(\w+)'
        admin_routes = re.findall(admin_update_pattern, content, re.MULTILINE | re.DOTALL)
        
        logger.info(f"\n📋 找到 {len(admin_routes)} 个管理页面路由:")
        for route_path, func_name in admin_routes:
            if 'update' in func_name.lower() or 'edit' in func_name.lower() or 'create' in func_name.lower():
                logger.info(f"  - {route_path} -> {func_name}()")
        
        # 3. 查找所有json.dump调用
        json_dump_pattern = r'json\.dump\([^)]+\)'
        json_dumps = re.findall(json_dump_pattern, content)
        
        logger.info(f"\n📋 找到 {len(json_dumps)} 个json.dump调用:")
        for i, dump_call in enumerate(json_dumps[:10]):  # 只显示前10个
            logger.info(f"  {i+1}. {dump_call[:80]}...")
        if len(json_dumps) > 10:
            logger.info(f"  ... 还有 {len(json_dumps) - 10} 个")
        
        return {
            'post_routes': post_routes,
            'admin_routes': admin_routes,
            'json_dumps': json_dumps
        }
        
    except Exception as e:
        logger.error(f"❌ 查找特定模式时发生错误: {e}")
        return None

def check_data_access_implementation():
    """检查data_access中update方法的实现"""
    
    data_access_file = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'database_access.py')
    
    try:
        with open(data_access_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        logger.info(f"\n🔍 检查data_access中update方法的实现:")
        logger.info("-" * 60)
        
        entities = [
            'members', 'activities', 'checkins', 'feedbacks', 
            'feedback_templates', 'student_ratings', 'email_templates', 
            'courses', 'navigation'
        ]
        
        # 查找update方法中每个实体的实现
        for entity in entities:
            pattern = rf'elif entity_type == [\'"]({entity})[\'"]:'
            matches = re.findall(pattern, content)
            
            if matches:
                logger.info(f"  ✅ {entity}: update方法已实现")
            else:
                logger.info(f"  ❌ {entity}: update方法未实现")
        
        # 查找update方法的总体结构
        update_method_pattern = r'def update\(self[^)]*\):'
        update_methods = re.findall(update_method_pattern, content)
        
        logger.info(f"\n📋 找到 {len(update_methods)} 个update方法定义")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查data_access实现时发生错误: {e}")
        return False

if __name__ == '__main__':
    # 分析server.py中的update使用情况
    results = analyze_server_update_usage()
    
    # 查找特定的update模式
    patterns = find_specific_update_patterns()
    
    # 检查data_access中的实现
    check_data_access_implementation()
    
    if results:
        # 计算总体完成度
        entities = [
            'members', 'activities', 'checkins', 'feedbacks', 
            'feedback_templates', 'student_ratings', 'email_templates', 
            'courses', 'navigation'
        ]
        
        using_data_access_count = len([e for e in entities if results['using_data_access'][e]])
        completion_rate = (using_data_access_count / len(entities)) * 100
        
        logger.info(f"\n🎯 总体完成度: {completion_rate:.1f}% ({using_data_access_count}/{len(entities)})")
        
        if completion_rate == 100:
            logger.info("🎉 所有实体都正确使用data_access.update()!")
        else:
            logger.info("⚠️  还有实体需要迁移到data_access.update()")
    
    sys.exit(0)
