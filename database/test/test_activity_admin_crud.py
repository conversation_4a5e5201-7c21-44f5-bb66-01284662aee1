#!/usr/bin/env python3
"""
检查Activity管理页面数据库CRUD操作
确保所有操作都正确使用数据库而不是JSON文件
"""

import os
import sys
import json
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_activity_admin_frontend():
    """检查Activity管理前端页面"""
    
    logger.info("🔍 检查Activity管理前端页面")
    logger.info("=" * 80)
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'activity_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查数据加载
        logger.info("\n📥 数据加载检查:")
        
        load_checks = [
            ('fetch(\'data/activities.json\')', '❌ 仍使用JSON文件加载'),
            ('fetch(\'data/courses.json\')', '❌ 仍使用JSON文件加载'),
            ('fetch(\'data/feedback_templates.json\')', '❌ 仍使用JSON文件加载'),
            ('data_access', '⚠️  未找到数据访问层调用'),
            ('/api/activities', '⚠️  未找到API调用')
        ]
        
        json_file_usage = False
        for pattern, message in load_checks:
            if pattern in content:
                if 'JSON文件' in message:
                    logger.warning(f"  {message}")
                    json_file_usage = True
                else:
                    logger.info(f"  {message}")
            else:
                if 'JSON文件' in message:
                    logger.info(f"  ✅ 已移除: {pattern}")
                else:
                    logger.warning(f"  {message}")
        
        # 检查数据保存
        logger.info("\n💾 数据保存检查:")
        
        save_checks = [
            ('fetch(\'data/activities.json\', {', '❌ 仍使用JSON文件保存'),
            ('method: \'POST\'', '✅ 使用POST方法'),
            ('/api/activity', '⚠️  未找到API保存调用'),
            ('JSON.stringify(activities', '❌ 直接序列化整个对象')
        ]
        
        for pattern, message in save_checks:
            if pattern in content:
                if '❌' in message:
                    logger.warning(f"  {message}")
                    json_file_usage = True
                else:
                    logger.info(f"  {message}")
            else:
                if '⚠️' in message:
                    logger.warning(f"  {message}")
                else:
                    logger.info(f"  ✅ 已移除: {pattern}")
        
        # 检查CRUD操作
        logger.info("\n🔧 CRUD操作检查:")
        
        crud_checks = [
            ('function addNewActivity', '✅ 创建操作存在'),
            ('function updateActivity', '✅ 更新操作存在'),
            ('function deleteActivity', '✅ 删除操作存在'),
            ('function duplicateActivity', '✅ 复制操作存在'),
            ('delete activities[selectedActivity]', '❌ 本地删除操作'),
            ('activities[activityId] = ', '❌ 本地添加操作')
        ]
        
        for pattern, message in crud_checks:
            if pattern in content:
                if '❌' in message:
                    logger.warning(f"  {message}")
                else:
                    logger.info(f"  {message}")
            else:
                if '✅' in message:
                    logger.warning(f"  ⚠️  未找到: {pattern}")
                else:
                    logger.info(f"  ✅ 已移除: {pattern}")
        
        return not json_file_usage
        
    except Exception as e:
        logger.error(f"❌ 检查Activity管理前端时发生错误: {e}")
        return False

def check_activity_backend_apis():
    """检查Activity后端API"""
    
    logger.info("\n🔧 Activity后端API检查:")
    
    server_file = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'server.py')
    
    try:
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查数据获取API
        logger.info("\n📥 数据获取API检查:")
        
        get_api_checks = [
            ('@app.route(\'/data/activities.json\', methods=[\'GET\'])', '❌ 仍有JSON文件路由'),
            ('data_access.get_all(\'activities\')', '✅ 使用数据访问层'),
            ('send_from_directory(DATA_DIR, \'activities.json\')', '❌ 仍有文件服务'),
            ('@app.route(\'/api/activity/<activity_id>\', methods=[\'GET\'])', '✅ 单个活动API存在')
        ]
        
        json_api_usage = False
        for pattern, message in get_api_checks:
            if pattern in content:
                if '❌' in message:
                    logger.warning(f"  {message}")
                    json_api_usage = True
                else:
                    logger.info(f"  {message}")
            else:
                if '✅' in message:
                    logger.warning(f"  ⚠️  未找到: {pattern}")
                else:
                    logger.info(f"  ✅ 已移除: {pattern}")
        
        # 检查数据保存API
        logger.info("\n💾 数据保存API检查:")
        
        save_api_checks = [
            ('@app.route(\'/data/activities.json\', methods=[\'POST\'])', '❌ 仍有JSON文件保存路由'),
            ('activities_file = os.path.join(DATA_DIR, \'activities.json\')', '❌ 仍有JSON文件操作'),
            ('json.dump(data, f', '❌ 仍有JSON文件写入'),
            ('@app.route(\'/api/activity\', methods=[\'POST\'])', '✅ 创建活动API存在'),
            ('@app.route(\'/api/activity/<activity_id>\', methods=[\'PUT\'])', '⚠️  更新活动API'),
            ('@app.route(\'/api/activity/<activity_id>\', methods=[\'DELETE\'])', '⚠️  删除活动API')
        ]
        
        for pattern, message in save_api_checks:
            if pattern in content:
                if '❌' in message:
                    logger.warning(f"  {message}")
                    json_api_usage = True
                else:
                    logger.info(f"  {message}")
            else:
                if '⚠️' in message:
                    logger.warning(f"  {message}")
                else:
                    logger.info(f"  ✅ 已移除: {pattern}")
        
        # 检查单个活动API实现
        logger.info("\n🔍 单个活动API实现检查:")
        
        # 查找get_activity函数
        get_activity_start = content.find('def get_activity(activity_id):')
        get_activity_end = content.find('\<EMAIL>', get_activity_start + 1)
        if get_activity_end == -1:
            get_activity_end = content.find('\ndef ', get_activity_start + 1)
        
        get_activity_content = content[get_activity_start:get_activity_end] if get_activity_start != -1 else ""
        
        # 查找create_activity函数
        create_activity_start = content.find('def create_activity():')
        create_activity_end = content.find('\<EMAIL>', create_activity_start + 1)
        if create_activity_end == -1:
            create_activity_end = content.find('\ndef ', create_activity_start + 1)
        
        create_activity_content = content[create_activity_start:create_activity_end] if create_activity_start != -1 else ""
        
        api_implementation_checks = [
            ('activities_file = os.path.join(DATA_DIR, \'activities.json\')', '❌ API中仍有JSON文件操作'),
            ('with open(activities_file', '❌ API中仍有文件读写'),
            ('json.load(f)', '❌ API中仍有JSON加载'),
            ('json.dump(activities, f', '❌ API中仍有JSON保存'),
            ('data_access.get_by_id(\'activities\'', '⚠️  API中未使用数据访问层'),
            ('data_access.create(\'activities\'', '⚠️  API中未使用数据访问层'),
            ('data_access.update(\'activities\'', '⚠️  API中未使用数据访问层'),
            ('data_access.delete(\'activities\'', '⚠️  API中未使用数据访问层')
        ]
        
        for pattern, message in api_implementation_checks:
            if pattern in get_activity_content or pattern in create_activity_content:
                if '❌' in message:
                    logger.warning(f"  {message}")
                    json_api_usage = True
                else:
                    logger.info(f"  {message}")
            else:
                if '⚠️' in message:
                    logger.warning(f"  {message}")
                else:
                    logger.info(f"  ✅ 已移除: {pattern}")
        
        return not json_api_usage
        
    except Exception as e:
        logger.error(f"❌ 检查Activity后端API时发生错误: {e}")
        return False

def analyze_required_changes():
    """分析需要的修改"""
    
    logger.info("\n📊 需要的修改分析:")
    
    required_changes = [
        {
            "组件": "前端 - 数据加载",
            "当前": "fetch('data/activities.json')",
            "需要": "fetch('/api/activities') 或使用数据访问层",
            "优先级": "🔴 高"
        },
        {
            "组件": "前端 - 数据保存",
            "当前": "POST到data/activities.json",
            "需要": "调用单个活动API进行CRUD",
            "优先级": "🔴 高"
        },
        {
            "组件": "后端 - 批量数据API",
            "当前": "JSON文件读写",
            "需要": "使用data_access层",
            "优先级": "🔴 高"
        },
        {
            "组件": "后端 - 单个活动API",
            "当前": "JSON文件操作",
            "需要": "使用data_access层",
            "优先级": "🔴 高"
        },
        {
            "组件": "后端 - 缺失的API",
            "当前": "只有GET和POST",
            "需要": "添加PUT和DELETE API",
            "优先级": "🟡 中"
        },
        {
            "组件": "前端 - CRUD操作",
            "当前": "本地对象操作",
            "需要": "调用对应的API",
            "优先级": "🟡 中"
        }
    ]
    
    for change in required_changes:
        logger.info(f"\n  📂 {change['组件']} {change['优先级']}")
        logger.info(f"    当前: {change['当前']}")
        logger.info(f"    需要: {change['需要']}")

def generate_implementation_plan():
    """生成实施计划"""
    
    logger.info("\n📋 实施计划:")
    
    implementation_steps = [
        {
            "步骤": "1. 修复后端API",
            "任务": [
                "修改get_activities使用data_access层",
                "修改update_activities使用data_access层",
                "修改get_activity使用data_access层",
                "修改create_activity使用data_access层",
                "添加update_activity API (PUT)",
                "添加delete_activity API (DELETE)"
            ]
        },
        {
            "步骤": "2. 修复前端数据加载",
            "任务": [
                "修改loadData函数使用API",
                "处理API响应格式",
                "添加错误处理"
            ]
        },
        {
            "步骤": "3. 修复前端CRUD操作",
            "任务": [
                "修改addNewActivity调用POST API",
                "修改updateActivity调用PUT API",
                "修改deleteActivity调用DELETE API",
                "修改duplicateActivity调用POST API",
                "移除本地对象操作"
            ]
        },
        {
            "步骤": "4. 修复前端数据保存",
            "任务": [
                "移除saveActivities函数",
                "实现实时保存机制",
                "添加保存状态指示"
            ]
        },
        {
            "步骤": "5. 测试验证",
            "任务": [
                "测试数据加载",
                "测试CRUD操作",
                "测试数据一致性",
                "测试错误处理"
            ]
        }
    ]
    
    for step in implementation_steps:
        logger.info(f"\n  📂 {step['步骤']}")
        for task in step['任务']:
            logger.info(f"    • {task}")

if __name__ == '__main__':
    # 检查前端
    frontend_ok = check_activity_admin_frontend()
    
    # 检查后端
    backend_ok = check_activity_backend_apis()
    
    # 分析需要的修改
    analyze_required_changes()
    
    # 生成实施计划
    generate_implementation_plan()
    
    if frontend_ok and backend_ok:
        logger.info("\n🎊 Activity管理页面数据库CRUD检查通过！")
        logger.info("✅ 所有操作都正确使用数据库")
    else:
        logger.error("\n❌ Activity管理页面数据库CRUD检查失败")
        logger.error("⚠️  发现JSON文件操作，需要修复为数据库操作")
    
    sys.exit(0 if (frontend_ok and backend_ok) else 1)
