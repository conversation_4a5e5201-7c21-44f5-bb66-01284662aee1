#!/usr/bin/env python3
"""
测试Course Admin页面的完整集成
验证前端页面、后端API和数据库操作的完整流程
"""

import sys
import os
import json
import logging
import requests
import time
from datetime import datetime

# 设置路径和环境变量
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
os.environ['DATA_MODE'] = "database"

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_backend_api():
    """测试后端API"""
    
    logger.info("🔗 测试Course Admin后端API")
    logger.info("=" * 60)
    
    try:
        # 测试数据
        test_courses = {
            "test_beginner": {
                "id": "test_beginner",
                "en": {
                    "title": "Test Beginner Course",
                    "description": "A test beginner course for API testing"
                },
                "zh": {
                    "title": "测试初级课程",
                    "description": "用于API测试的初级课程"
                },
                "nl": {
                    "title": "Test Beginnerscursus",
                    "description": "Een test beginnerscursus voor API-testen"
                }
            },
            "test_advanced": {
                "id": "test_advanced",
                "en": {
                    "title": "Test Advanced Course",
                    "description": "A test advanced course for API testing"
                },
                "zh": {
                    "title": "测试高级课程",
                    "description": "用于API测试的高级课程"
                },
                "nl": {
                    "title": "Test Gevorderde cursus",
                    "description": "Een test gevorderde cursus voor API-testen"
                }
            }
        }
        
        # 1. 测试POST API (保存课程)
        logger.info("📤 测试POST API...")
        post_response = requests.post(
            'http://127.0.0.1:8899/data/courses.json',
            json=test_courses,
            headers={'Content-Type': 'application/json'}
        )
        
        if post_response.status_code == 200:
            logger.info("✅ POST API测试成功")
            result = post_response.json()
            logger.info(f"   响应: {result}")
        else:
            logger.error(f"❌ POST API测试失败: {post_response.status_code}")
            logger.error(f"   响应: {post_response.text}")
            return False
        
        # 等待一下确保数据已保存
        time.sleep(1)
        
        # 2. 测试GET API (获取课程)
        logger.info("📥 测试GET API...")
        get_response = requests.get('http://127.0.0.1:8899/data/courses.json')
        
        if get_response.status_code == 200:
            logger.info("✅ GET API测试成功")
            courses_data = get_response.json()
            logger.info(f"   获取到 {len(courses_data)} 个课程")
            
            # 验证数据完整性
            if 'test_beginner' in courses_data and 'test_advanced' in courses_data:
                logger.info("✅ 课程数据完整性验证成功")
                logger.info(f"   初级课程: {courses_data['test_beginner']['en']['title']}")
                logger.info(f"   高级课程: {courses_data['test_advanced']['en']['title']}")
            else:
                logger.error("❌ 课程数据完整性验证失败")
                logger.error(f"   实际数据: {list(courses_data.keys())}")
                return False
        else:
            logger.error(f"❌ GET API测试失败: {get_response.status_code}")
            return False
        
        logger.info("🎉 后端API测试全部通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 后端API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_consistency():
    """测试数据库一致性"""
    
    logger.info("\n🗄️  测试数据库一致性")
    logger.info("=" * 60)
    
    try:
        from database_access import DataAccessLayer
        
        dal = DataAccessLayer(mode='database')
        
        # 获取数据库中的数据
        db_courses = dal.get_all('courses')
        
        # 获取API返回的数据
        api_response = requests.get('http://127.0.0.1:8899/data/courses.json')
        api_courses = api_response.json()
        
        logger.info(f"数据库中的课程数量: {len(db_courses)}")
        logger.info(f"API返回的课程数量: {len(api_courses)}")
        
        # 比较数据一致性
        if len(db_courses) == len(api_courses):
            logger.info("✅ 课程数量一致")
        else:
            logger.error("❌ 课程数量不一致")
            return False
        
        # 比较具体内容
        for course_id in db_courses:
            if course_id in api_courses:
                db_course = db_courses[course_id]
                api_course = api_courses[course_id]
                
                # 比较英文标题
                if db_course.get('en', {}).get('title') == api_course.get('en', {}).get('title'):
                    logger.info(f"✅ 课程 {course_id} 数据一致")
                else:
                    logger.error(f"❌ 课程 {course_id} 数据不一致")
                    logger.error(f"   数据库: {db_course.get('en', {}).get('title')}")
                    logger.error(f"   API: {api_course.get('en', {}).get('title')}")
                    return False
            else:
                logger.error(f"❌ 课程 {course_id} 在API中不存在")
                return False
        
        logger.info("🎉 数据库一致性测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库一致性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_crud_operations():
    """测试完整的CRUD操作流程"""
    
    logger.info("\n🔄 测试完整CRUD操作流程")
    logger.info("=" * 60)
    
    try:
        # 1. 创建测试数据
        logger.info("📝 创建测试数据...")
        create_data = {
            "crud_test": {
                "id": "crud_test",
                "en": {
                    "title": "CRUD Test Course",
                    "description": "Course for testing CRUD operations"
                },
                "zh": {
                    "title": "CRUD测试课程",
                    "description": "用于测试CRUD操作的课程"
                }
            }
        }
        
        create_response = requests.post(
            'http://127.0.0.1:8899/data/courses.json',
            json=create_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if create_response.status_code == 200:
            logger.info("✅ 创建操作成功")
        else:
            logger.error(f"❌ 创建操作失败: {create_response.status_code}")
            return False
        
        # 2. 读取数据验证
        logger.info("📖 读取数据验证...")
        read_response = requests.get('http://127.0.0.1:8899/data/courses.json')
        read_data = read_response.json()
        
        if 'crud_test' in read_data:
            logger.info("✅ 读取操作成功")
            logger.info(f"   课程标题: {read_data['crud_test']['en']['title']}")
        else:
            logger.error("❌ 读取操作失败，未找到创建的课程")
            return False
        
        # 3. 更新数据
        logger.info("✏️  更新数据...")
        update_data = read_data.copy()
        update_data['crud_test']['en']['title'] = "Updated CRUD Test Course"
        update_data['crud_test']['en']['description'] = "Updated description for CRUD testing"
        
        update_response = requests.post(
            'http://127.0.0.1:8899/data/courses.json',
            json=update_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if update_response.status_code == 200:
            logger.info("✅ 更新操作成功")
        else:
            logger.error(f"❌ 更新操作失败: {update_response.status_code}")
            return False
        
        # 4. 验证更新结果
        logger.info("🔍 验证更新结果...")
        verify_response = requests.get('http://127.0.0.1:8899/data/courses.json')
        verify_data = verify_response.json()
        
        if verify_data['crud_test']['en']['title'] == "Updated CRUD Test Course":
            logger.info("✅ 更新验证成功")
            logger.info(f"   更新后标题: {verify_data['crud_test']['en']['title']}")
        else:
            logger.error("❌ 更新验证失败")
            return False
        
        # 5. 删除数据（通过清空所有数据模拟）
        logger.info("🗑️  删除数据...")
        delete_data = {}  # 空数据相当于删除所有课程
        
        delete_response = requests.post(
            'http://127.0.0.1:8899/data/courses.json',
            json=delete_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if delete_response.status_code == 200:
            logger.info("✅ 删除操作成功")
        else:
            logger.error(f"❌ 删除操作失败: {delete_response.status_code}")
            return False
        
        # 6. 验证删除结果
        logger.info("🔍 验证删除结果...")
        final_response = requests.get('http://127.0.0.1:8899/data/courses.json')
        final_data = final_response.json()
        
        if len(final_data) == 0:
            logger.info("✅ 删除验证成功，所有数据已清空")
        else:
            logger.error(f"❌ 删除验证失败，仍有 {len(final_data)} 个课程")
            return False
        
        logger.info("🎉 完整CRUD操作流程测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ CRUD操作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_data():
    """清理测试数据"""
    
    logger.info("\n🧹 清理测试数据...")
    
    try:
        # 清空所有课程数据
        cleanup_response = requests.post(
            'http://127.0.0.1:8899/data/courses.json',
            json={},
            headers={'Content-Type': 'application/json'}
        )
        
        if cleanup_response.status_code == 200:
            logger.info("✅ 测试数据清理完成")
        else:
            logger.warning(f"⚠️ 测试数据清理可能不完整: {cleanup_response.status_code}")
        
    except Exception as e:
        logger.warning(f"⚠️ 清理测试数据时出现错误: {e}")

if __name__ == "__main__":
    success = True
    
    try:
        # 测试后端API
        if not test_backend_api():
            success = False
        
        # 测试数据库一致性
        if not test_database_consistency():
            success = False
        
        # 测试CRUD操作
        if not test_crud_operations():
            success = False
        
    finally:
        # 清理测试数据
        cleanup_test_data()
    
    if success:
        logger.info("\n🎉 所有Course Admin集成测试通过！")
        logger.info("✅ 后端API正常工作")
        logger.info("✅ 数据库操作正常")
        logger.info("✅ CRUD流程完整")
        logger.info("✅ 数据一致性良好")
        sys.exit(0)
    else:
        logger.error("\n❌ Course Admin集成测试失败！")
        sys.exit(1)
