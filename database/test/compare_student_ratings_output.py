#!/usr/bin/env python3
"""
对比Student Ratings数据在database模式和json模式下的返回值
确保两种模式返回的数据完全一致
"""

import os
import sys
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def compare_student_ratings_output():
    """对比两种模式下的student_ratings数据输出"""
    
    # 设置环境
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
    os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
    
    try:
        from database_access import DataAccessLayer
        
        logger.info("🔍 开始对比Student Ratings数据输出")
        logger.info("=" * 80)
        
        # 1. 测试JSON模式
        logger.info("📄 测试JSON模式...")
        json_dal = DataAccessLayer(mode='json')
        json_data = json_dal.get_all('student_ratings')
        logger.info(f"JSON模式: 获取到 {len(json_data)} 条记录")
        
        # 2. 测试Database模式
        logger.info("🗄️  测试Database模式...")
        db_dal = DataAccessLayer(mode='database')
        db_data = db_dal.get_all('student_ratings')
        logger.info(f"Database模式: 获取到 {len(db_data)} 条记录")
        
        # 3. 对比数据数量
        if len(json_data) != len(db_data):
            logger.error(f"❌ 数据数量不一致: JSON={len(json_data)}, Database={len(db_data)}")
            return False
        
        logger.info(f"✅ 数据数量一致: {len(json_data)} 条记录")
        
        # 4. 对比具体数据
        logger.info("\n📊 详细数据对比:")
        
        mismatches = []
        matches = 0
        
        for rating_id in json_data.keys():
            if rating_id not in db_data:
                mismatches.append(f"Database中缺少评分: {rating_id}")
                continue
            
            json_rating = json_data[rating_id]
            db_rating = db_data[rating_id]
            
            # 对比每个字段
            field_mismatches = []
            
            # 检查所有JSON中的字段
            for field in json_rating.keys():
                json_value = json_rating.get(field)
                db_value = db_rating.get(field)
                
                if json_value != db_value:
                    field_mismatches.append(f"  {field}: JSON='{json_value}' vs DB='{db_value}'")
            
            # 检查Database中是否有额外字段
            for field in db_rating.keys():
                if field not in json_rating:
                    field_mismatches.append(f"  {field}: 仅在Database中存在='{db_rating[field]}'")
            
            if field_mismatches:
                mismatches.append(f"评分 {rating_id} 字段不匹配:")
                mismatches.extend(field_mismatches)
            else:
                matches += 1
                logger.info(f"✅ {rating_id}: 完全匹配")
        
        # 检查Database中是否有JSON中没有的评分
        for rating_id in db_data.keys():
            if rating_id not in json_data:
                mismatches.append(f"JSON中缺少评分: {rating_id}")
        
        # 5. 输出结果
        logger.info(f"\n📈 对比结果:")
        logger.info(f"  完全匹配: {matches}/{len(json_data)}")
        logger.info(f"  不匹配: {len(mismatches)}")
        
        if mismatches:
            logger.error("\n❌ 发现不匹配项:")
            for mismatch in mismatches[:10]:  # 显示前10个
                logger.error(f"  {mismatch}")
            if len(mismatches) > 10:
                logger.error(f"  ... 还有 {len(mismatches) - 10} 个不匹配项")
            return False
        else:
            logger.info("\n🎉 所有数据完全匹配！")
            
            # 6. 显示数据结构示例
            logger.info("\n📋 数据结构示例:")
            sample_id = list(json_data.keys())[0]
            logger.info(f"JSON格式 ({sample_id}):")
            for key, value in json_data[sample_id].items():
                logger.info(f"  {key}: {value}")
            
            logger.info(f"\nDatabase格式 ({sample_id}):")
            for key, value in db_data[sample_id].items():
                logger.info(f"  {key}: {value}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 对比过程中发生错误: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def test_specific_rating():
    """测试特定评分的数据"""
    
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
    os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
    
    try:
        from database_access import DataAccessLayer
        
        logger.info("\n🔍 测试特定评分数据...")
        
        # 选择一个测试评分
        test_rating_id = "rating_1749500871954"
        
        # JSON模式
        json_dal = DataAccessLayer(mode='json')
        json_data = json_dal.get_all('student_ratings')
        json_rating = json_data.get(test_rating_id)
        
        # Database模式
        db_dal = DataAccessLayer(mode='database')
        db_data = db_dal.get_all('student_ratings')
        db_rating = db_data.get(test_rating_id)
        
        logger.info(f"\n评分 {test_rating_id} 详细对比:")
        logger.info("JSON数据:")
        logger.info(json.dumps(json_rating, indent=2, ensure_ascii=False))
        
        logger.info("\nDatabase数据:")
        logger.info(json.dumps(db_rating, indent=2, ensure_ascii=False))
        
        # 字段级对比
        logger.info("\n字段级对比:")
        all_fields = set(json_rating.keys()) | set(db_rating.keys())
        for field in sorted(all_fields):
            json_val = json_rating.get(field, "❌ 缺失")
            db_val = db_rating.get(field, "❌ 缺失")
            status = "✅" if json_val == db_val else "❌"
            logger.info(f"  {status} {field}: JSON='{json_val}' | DB='{db_val}'")
        
    except Exception as e:
        logger.error(f"❌ 测试特定评分时发生错误: {e}")

def check_data_source():
    """检查当前数据来源"""
    
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
    os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
    os.environ['DATA_MODE'] = "database"
    
    try:
        from database_access import DataAccessLayer
        
        logger.info("\n🔍 检查数据来源...")
        
        # 创建database模式的实例
        dal = DataAccessLayer(mode='database')
        logger.info(f"数据访问层模式: {dal.mode}")
        
        # 检查是否真的从数据库获取数据
        ratings = dal.get_all('student_ratings')
        logger.info(f"获取到 {len(ratings)} 个学生评分")
        
        # 检查数据库连接
        if dal.engine:
            logger.info("✅ 数据库连接正常")
        else:
            logger.error("❌ 数据库连接失败")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查数据源时发生错误: {e}")
        return False

def check_server_integration():
    """检查server.py中的集成"""
    
    logger.info("\n🔍 检查server.py集成...")
    
    server_file = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'server.py')
    
    try:
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查student_ratings的GET路由
        if 'data_access.get_all(\'student_ratings\')' in content:
            logger.info("✅ server.py中student_ratings GET路由使用了data_access")
        else:
            logger.warning("⚠️  server.py中student_ratings GET路由可能未使用data_access")
        
        # 检查是否还有直接文件操作
        if 'student_ratings.json' in content and 'send_from_directory' in content:
            logger.info("✅ server.py中有JSON文件的fallback机制")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查server.py集成时发生错误: {e}")
        return False

if __name__ == '__main__':
    # 检查数据源
    check_data_source()
    
    # 检查server.py集成
    check_server_integration()
    
    # 运行对比测试
    success = compare_student_ratings_output()
    
    # 测试特定评分
    test_specific_rating()
    
    sys.exit(0 if success else 1)
