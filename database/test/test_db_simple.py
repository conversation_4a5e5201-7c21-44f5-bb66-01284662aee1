#!/usr/bin/env python3
"""
Simple database connection test
"""

import os
import sys

def test_imports():
    """Test if we can import required modules"""
    try:
        import psycopg2
        print("✅ psycopg2 imported successfully")
        print(f"psycopg2 version: {psycopg2.__version__}")
    except ImportError as e:
        print(f"❌ Failed to import psycopg2: {e}")
        return False
    
    try:
        from sqlalchemy import create_engine
        print("✅ sqlalchemy imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import sqlalchemy: {e}")
        return False
    
    return True

def test_connection():
    """Test database connection"""
    database_urls = [
        "postgresql://postgres:SnowNavi2025!@localhost:5432/snownavi",
        "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi",
        "postgresql://postgres@localhost:5432/snownavi"
    ]
    
    import psycopg2
    
    for db_url in database_urls:
        print(f"\n🧪 Testing connection: {db_url}")
        try:
            conn = psycopg2.connect(db_url)
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()
            print(f"✅ Connection successful!")
            print(f"PostgreSQL version: {version[0]}")
            
            # Test if our tables exist
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name;
            """)
            tables = cursor.fetchall()
            
            if tables:
                print(f"✅ Found {len(tables)} tables:")
                for table in tables:
                    print(f"  - {table[0]}")
            else:
                print("⚠️  No tables found in database")
            
            conn.close()
            return db_url
            
        except Exception as e:
            print(f"❌ Connection failed: {e}")
    
    return None

def main():
    print("🚀 Simple Database Connection Test")
    print("=" * 40)
    
    if not test_imports():
        print("❌ Import test failed")
        return False
    
    working_url = test_connection()
    if working_url:
        print(f"\n✅ Working database URL: {working_url}")
        return True
    else:
        print("\n❌ No working database connection found")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
