#!/usr/bin/env python3
"""
验证Feedback保存功能修复
确保feedbackRecord变量作用域正确
"""

import os
import sys
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_feedback_save_function():
    """检查feedback保存函数的变量作用域"""
    
    logger.info("🔧 检查Feedback保存函数修复")
    logger.info("=" * 80)
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'checkin_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查feedbackRecord变量定义
        logger.info("\n📝 变量作用域检查:")
        
        # 查找window.saveFeedback函数
        save_feedback_match = re.search(r'window\.saveFeedback = async function\(event\) \{(.*?)\};', content, re.DOTALL)
        
        if not save_feedback_match:
            logger.error("❌ 未找到window.saveFeedback函数")
            return False
        
        save_feedback_content = save_feedback_match.group(1)
        
        # 检查feedbackRecord变量声明
        variable_checks = [
            (r'let feedbackRecord;', '✅ feedbackRecord变量声明'),
            (r'feedbackRecord = existingFeedback;', '✅ 更新时赋值existingFeedback'),
            (r'feedbackRecord = \{', '✅ 创建时赋值新对象'),
            (r'await saveFeedback\(currentFeedbackCheckinId, feedbackRecord\)', '✅ API调用使用feedbackRecord'),
            (r'feedbacks\[currentFeedbackCheckinId\] = feedbackRecord', '✅ 本地缓存更新')
        ]
        
        all_checks_passed = True
        for pattern, message in variable_checks:
            if re.search(pattern, save_feedback_content):
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
                all_checks_passed = False
        
        # 检查是否移除了重复的本地缓存更新
        duplicate_checks = [
            (r'feedbacks\[currentFeedbackCheckinId\] = feedbackRecord;.*feedbacks\[currentFeedbackCheckinId\] = feedbackRecord;', '❌ 重复的本地缓存更新')
        ]
        
        for pattern, message in duplicate_checks:
            if re.search(pattern, save_feedback_content, re.DOTALL):
                logger.warning(f"  {message}")
                all_checks_passed = False
            else:
                logger.info(f"  ✅ 无重复的本地缓存更新")
        
        return all_checks_passed
        
    except Exception as e:
        logger.error(f"❌ 检查feedback保存函数时发生错误: {e}")
        return False

def check_api_function_definition():
    """检查API函数定义"""
    
    logger.info("\n🔌 API函数定义检查:")
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'checkin_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查saveFeedback API函数
        api_checks = [
            (r'async function saveFeedback\(checkinId, feedbackData\)', '✅ saveFeedback API函数定义'),
            (r'fetch\(`/api/feedback/\$\{checkinId\}`', '✅ RESTful API调用'),
            (r'method: feedbacks\[checkinId\] \? \'PUT\' : \'POST\'', '✅ 动态HTTP方法'),
            (r'JSON\.stringify\(feedbackData\)', '✅ JSON数据序列化'),
            (r'async function deleteFeedbackAPI\(checkinId\)', '✅ deleteFeedbackAPI函数定义')
        ]
        
        api_ok = True
        for pattern, message in api_checks:
            if re.search(pattern, content):
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
                api_ok = False
        
        return api_ok
        
    except Exception as e:
        logger.error(f"❌ 检查API函数定义时发生错误: {e}")
        return False

def check_error_handling():
    """检查错误处理"""
    
    logger.info("\n🛡️  错误处理检查:")
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'checkin_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查错误处理
        error_checks = [
            (r'catch \(error\) \{', '✅ 错误捕获'),
            (r'console\.error\(\'Error saving feedback:\', error\);', '✅ 错误日志记录'),
            (r'showResult\(\'Failed to save feedback\. Please try again\.\', \'error\'\);', '✅ 用户错误提示'),
            (r'throw error;', '✅ 错误重新抛出')
        ]
        
        error_ok = True
        for pattern, message in error_checks:
            if re.search(pattern, content):
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
                error_ok = False
        
        return error_ok
        
    except Exception as e:
        logger.error(f"❌ 检查错误处理时发生错误: {e}")
        return False

def generate_fix_summary():
    """生成修复总结"""
    
    logger.info("\n📊 Feedback保存功能修复总结:")
    
    fixes = [
        {
            "问题": "feedbackRecord变量作用域错误",
            "原因": "变量只在else分支中定义，但在外部使用",
            "修复": "在函数开始声明let feedbackRecord，在两个分支中分别赋值",
            "效果": "变量在整个函数作用域内可用"
        },
        {
            "问题": "更新feedback时变量未定义",
            "原因": "更新分支中没有定义feedbackRecord",
            "修复": "在更新分支中设置feedbackRecord = existingFeedback",
            "效果": "更新和创建都能正确传递数据给API"
        },
        {
            "问题": "API调用参数错误",
            "原因": "使用了未定义的变量",
            "修复": "确保feedbackRecord在API调用前已正确赋值",
            "效果": "API调用能够正确传递feedback数据"
        },
        {
            "问题": "本地缓存更新逻辑",
            "原因": "可能存在重复或错误的缓存更新",
            "修复": "统一在API调用成功后更新本地缓存",
            "效果": "本地缓存与服务器数据保持一致"
        }
    ]
    
    for fix in fixes:
        logger.info(f"\n  🔧 {fix['问题']}")
        logger.info(f"    原因: {fix['原因']}")
        logger.info(f"    修复: {fix['修复']}")
        logger.info(f"    效果: {fix['效果']}")

if __name__ == '__main__':
    # 检查feedback保存函数
    save_function_ok = check_feedback_save_function()
    
    # 检查API函数定义
    api_function_ok = check_api_function_definition()
    
    # 检查错误处理
    error_handling_ok = check_error_handling()
    
    # 生成修复总结
    generate_fix_summary()
    
    if save_function_ok and api_function_ok and error_handling_ok:
        logger.info("\n🎊 Feedback保存功能修复验证成功！")
        logger.info("✅ feedbackRecord变量作用域正确")
        logger.info("✅ API函数定义完整")
        logger.info("✅ 错误处理完善")
        logger.info("✅ 更新和创建逻辑统一")
    else:
        logger.error("\n❌ Feedback保存功能修复验证失败")
        if not save_function_ok:
            logger.error("⚠️  保存函数变量作用域有问题")
        if not api_function_ok:
            logger.error("⚠️  API函数定义有问题")
        if not error_handling_ok:
            logger.error("⚠️  错误处理有问题")
    
    sys.exit(0 if (save_function_ok and api_function_ok and error_handling_ok) else 1)
