#!/usr/bin/env python3
"""
数据库持久化测试脚本
验证Docker重启后数据是否保持
"""

import subprocess
import time
import sys
import os

def run_command(cmd, description):
    """执行命令并返回结果"""
    print(f"🔧 {description}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f"✅ {description} - 成功")
            return result.stdout.strip()
        else:
            print(f"❌ {description} - 失败: {result.stderr}")
            return None
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - 超时")
        return None
    except Exception as e:
        print(f"💥 {description} - 异常: {e}")
        return None

def get_data_counts():
    """获取数据库中的数据数量"""
    cmd = 'docker exec snownavi-db psql -U snownavi_user -d snownavi -t -c "SELECT COUNT(*) FROM members; SELECT COUNT(*) FROM activities;"'
    result = run_command(cmd, "获取数据库数据数量")
    if result:
        lines = [line.strip() for line in result.split('\n') if line.strip()]
        if len(lines) >= 2:
            return int(lines[0]), int(lines[1])
    return None, None

def test_database_persistence():
    """测试数据库持久化"""
    print("🚀 数据库持久化测试")
    print("=" * 50)
    
    # 1. 检查初始状态
    print("\n📊 步骤 1: 检查初始数据状态")
    initial_members, initial_activities = get_data_counts()
    if initial_members is None:
        print("❌ 无法获取初始数据，测试失败")
        return False
    
    print(f"   初始成员数量: {initial_members}")
    print(f"   初始活动数量: {initial_activities}")
    
    # 2. 停止Docker容器
    print("\n🛑 步骤 2: 停止Docker容器")
    stop_result = run_command("docker compose down", "停止Docker容器")
    if stop_result is None:
        print("❌ 停止容器失败")
        return False
    
    # 3. 验证容器已停止
    print("\n🔍 步骤 3: 验证容器状态")
    ps_result = run_command("docker ps | grep snownavi-db || echo 'No containers running'", "检查容器状态")
    if "snownavi-db" in (ps_result or ""):
        print("⚠️  容器仍在运行")
    else:
        print("✅ 容器已停止")
    
    # 4. 检查数据卷是否保持
    print("\n💾 步骤 4: 检查数据卷状态")
    volume_result = run_command("docker volume ls | grep postgres", "检查数据卷")
    if volume_result and "postgres_data" in volume_result:
        print("✅ 数据卷保持完整")
    else:
        print("❌ 数据卷丢失")
        return False
    
    # 5. 重新启动容器
    print("\n🚀 步骤 5: 重新启动Docker容器")
    start_result = run_command("docker compose up -d db", "启动数据库容器")
    if start_result is None:
        print("❌ 启动容器失败")
        return False
    
    # 6. 等待数据库就绪
    print("\n⏳ 步骤 6: 等待数据库就绪")
    time.sleep(5)
    
    ready_result = run_command("docker exec snownavi-db pg_isready -U snownavi_user", "检查数据库就绪状态")
    if ready_result and "accepting connections" in ready_result:
        print("✅ 数据库已就绪")
    else:
        print("⚠️  数据库可能未完全就绪，继续测试...")
        time.sleep(3)
    
    # 7. 验证数据完整性
    print("\n🔍 步骤 7: 验证数据完整性")
    final_members, final_activities = get_data_counts()
    if final_members is None:
        print("❌ 无法获取重启后数据")
        return False
    
    print(f"   重启后成员数量: {final_members}")
    print(f"   重启后活动数量: {final_activities}")
    
    # 8. 比较数据
    print("\n📊 步骤 8: 数据完整性对比")
    members_match = initial_members == final_members
    activities_match = initial_activities == final_activities
    
    print(f"   成员数据: {'✅ 一致' if members_match else '❌ 不一致'} ({initial_members} -> {final_members})")
    print(f"   活动数据: {'✅ 一致' if activities_match else '❌ 不一致'} ({initial_activities} -> {final_activities})")
    
    # 9. 测试应用连接
    print("\n🔗 步骤 9: 测试应用数据库连接")
    app_test_cmd = '''cd backend && python -c "
import os
import sys
sys.path.append('.')
os.environ['DATABASE_URL'] = 'postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi'
os.environ['DATA_MODE'] = 'database'
try:
    from database_access import DataAccessLayer
    dal = DataAccessLayer(mode='database')
    members = dal.get_all('members')
    print(f'应用连接成功，找到 {len(members)} 个成员')
except Exception as e:
    print(f'应用连接失败: {e}')
    sys.exit(1)
"'''
    
    app_result = run_command(app_test_cmd, "测试应用数据库连接")
    app_success = app_result and "应用连接成功" in app_result
    
    if app_success:
        print("✅ 应用可以正常连接数据库")
    else:
        print("❌ 应用连接数据库失败")
    
    # 10. 最终结果
    print("\n" + "=" * 50)
    print("📋 测试结果总结")
    print("=" * 50)
    
    all_passed = members_match and activities_match and app_success
    
    if all_passed:
        print("🎉 数据库持久化测试 - 全部通过！")
        print("\n✅ 确认事项:")
        print("   ✅ Docker容器重启后数据完整保持")
        print("   ✅ 数据卷正确配置并持久化")
        print("   ✅ 应用可以正常连接数据库")
        print("   ✅ 所有数据记录数量一致")
        
        print("\n🔒 数据安全保证:")
        print("   ✅ 数据存储在Docker卷中，与容器生命周期分离")
        print("   ✅ 使用 'docker compose down && docker compose up -d' 安全重启")
        print("   ✅ 避免使用 'docker compose down -v' 防止数据丢失")
        
        return True
    else:
        print("❌ 数据库持久化测试 - 部分失败")
        print("\n⚠️  问题:")
        if not members_match:
            print(f"   ❌ 成员数据不一致: {initial_members} -> {final_members}")
        if not activities_match:
            print(f"   ❌ 活动数据不一致: {initial_activities} -> {final_activities}")
        if not app_success:
            print("   ❌ 应用连接失败")
        
        return False

def main():
    """主函数"""
    print("🗄️ SnowNavi 数据库持久化验证测试")
    print("测试目标: 验证Docker重启后数据库数据是否保持")
    print()
    
    try:
        success = test_database_persistence()
        
        if success:
            print("\n🎯 结论: 数据库数据已确保持久化，Docker环境关闭后数据不会丢失！")
            sys.exit(0)
        else:
            print("\n⚠️  结论: 数据库持久化存在问题，需要检查配置")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
