#!/usr/bin/env python3
"""
测试Feedback Template数据库操作
验证创建、更新、删除操作是否正常工作
"""

import sys
import os
import json
import logging
from datetime import datetime

# 设置路径和环境变量
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
os.environ['DATA_MODE'] = "database"

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_feedback_template_crud():
    """测试Feedback Template的CRUD操作"""
    
    logger.info("🧪 开始测试Feedback Template数据库操作")
    logger.info("=" * 80)
    
    try:
        from database_access import DataAccessLayer
        
        # 使用database模式
        dal = DataAccessLayer(mode='database')
        logger.info(f"数据访问层模式: {dal.mode}")
        
        # 测试数据
        test_template_id = f"test_template_{int(datetime.now().timestamp())}"
        test_template_data = {
            'id': test_template_id,
            'name': {
                'en': 'Test Template',
                'zh': '测试模板',
                'nl': 'Test Template'
            },
            'description': {
                'en': 'Test feedback template for database operations',
                'zh': '用于数据库操作测试的反馈模板',
                'nl': 'Test feedback template voor database operaties'
            },
            'targetLevel': 'beginner',
            'sport': 'snowboard',
            'sections': {
                'basic_skills': {
                    'title': {
                        'en': 'Basic Skills',
                        'zh': '基础技能',
                        'nl': 'Basisvaardigheden'
                    },
                    'order': 1,
                    'skills': {
                        'balance': {
                            'name': {
                                'en': 'Balance',
                                'zh': '平衡',
                                'nl': 'Balans'
                            },
                            'description': {
                                'en': 'Ability to maintain balance on the board',
                                'zh': '在滑板上保持平衡的能力',
                                'nl': 'Vermogen om balans te houden op het board'
                            },
                            'order': 1
                        }
                    }
                }
            },
            'version': '1.0',
            'createdBy': 'test_admin',
            'createdAt': datetime.now().isoformat(),
            'updatedAt': datetime.now().isoformat()
        }
        
        # 1. 测试创建操作
        logger.info("📝 测试创建操作...")
        create_success = dal.create('feedback_templates', test_template_id, test_template_data)
        if create_success:
            logger.info("✅ 创建操作成功")
        else:
            logger.error("❌ 创建操作失败")
            return False
        
        # 2. 测试读取操作
        logger.info("📖 测试读取操作...")
        retrieved_template = dal.get_by_id('feedback_templates', test_template_id)
        if retrieved_template:
            logger.info("✅ 读取操作成功")
            logger.info(f"   模板名称: {retrieved_template['name']['en']}")
            logger.info(f"   目标级别: {retrieved_template['targetLevel']}")
            logger.info(f"   运动类型: {retrieved_template['sport']}")
        else:
            logger.error("❌ 读取操作失败")
            return False
        
        # 3. 测试更新操作
        logger.info("✏️  测试更新操作...")
        updated_data = test_template_data.copy()
        updated_data['name']['en'] = 'Updated Test Template'
        updated_data['targetLevel'] = 'intermediate'
        updated_data['updatedAt'] = datetime.now().isoformat()
        
        update_success = dal.update('feedback_templates', test_template_id, updated_data)
        if update_success:
            logger.info("✅ 更新操作成功")
            
            # 验证更新结果
            updated_template = dal.get_by_id('feedback_templates', test_template_id)
            if updated_template and updated_template['name']['en'] == 'Updated Test Template':
                logger.info("✅ 更新内容验证成功")
            else:
                logger.error("❌ 更新内容验证失败")
                return False
        else:
            logger.error("❌ 更新操作失败")
            return False
        
        # 4. 测试删除操作
        logger.info("🗑️  测试删除操作...")
        delete_success = dal.delete('feedback_templates', test_template_id)
        if delete_success:
            logger.info("✅ 删除操作成功")
            
            # 验证删除结果
            deleted_template = dal.get_by_id('feedback_templates', test_template_id)
            if not deleted_template:
                logger.info("✅ 删除结果验证成功")
            else:
                logger.error("❌ 删除结果验证失败")
                return False
        else:
            logger.error("❌ 删除操作失败")
            return False
        
        logger.info("\n🎉 所有Feedback Template数据库操作测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_existing_templates():
    """测试现有模板的读取"""
    
    logger.info("\n📋 测试现有模板读取...")
    
    try:
        from database_access import DataAccessLayer
        
        dal = DataAccessLayer(mode='database')
        templates = dal.get_all('feedback_templates')
        
        logger.info(f"数据库中共有 {len(templates)} 个反馈模板:")
        for template_id, template in templates.items():
            logger.info(f"  - {template_id}: {template['name']['en']} ({template['sport']}, {template['targetLevel']})")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 读取现有模板时发生错误: {e}")
        return False

if __name__ == "__main__":
    success = True
    
    # 测试现有模板读取
    if not test_existing_templates():
        success = False
    
    # 测试CRUD操作
    if not test_feedback_template_crud():
        success = False
    
    if success:
        logger.info("\n🎉 所有测试通过！Feedback Template数据库操作正常工作。")
        sys.exit(0)
    else:
        logger.error("\n❌ 测试失败！请检查数据库操作实现。")
        sys.exit(1)
