#!/usr/bin/env python3
"""
简单验证Feedback API修复
检查关键修复点
"""

import os
import sys
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_api_fix():
    """检查API修复"""
    
    logger.info("🔧 验证Feedback API修复")
    logger.info("=" * 80)
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'checkin_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        logger.info("\n📝 关键修复点检查:")
        
        # 检查关键修复点
        fixes = [
            ('const isUpdate = feedbacks[checkinId];', '✅ 1. 判断是否为更新操作'),
            ('const url = isUpdate ?', '✅ 2. 动态选择API URL'),
            ('const method = isUpdate ?', '✅ 3. 动态选择HTTP方法'),
            ("'/api/feedback'", '✅ 4. 创建时使用正确端点'),
            ("'PUT'", '✅ 5. 更新时使用PUT方法'),
            ("'POST'", '✅ 6. 创建时使用POST方法'),
            ('fetch(url, {', '✅ 7. 使用动态URL'),
            ('method: method,', '✅ 8. 使用动态方法')
        ]
        
        all_fixes_ok = True
        for pattern, message in fixes:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.error(f"  ❌ 未找到: {pattern}")
                all_fixes_ok = False
        
        return all_fixes_ok
        
    except Exception as e:
        logger.error(f"❌ 检查API修复时发生错误: {e}")
        return False

def check_backend_routes():
    """检查后端路由"""
    
    logger.info("\n🔌 检查后端路由:")
    
    server_file = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'server.py')
    
    try:
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键路由
        routes = [
            ("@app.route('/api/feedback', methods=['POST'])", '✅ POST /api/feedback (创建)'),
            ("@app.route('/api/feedback/<checkin_id>', methods=['PUT'])", '✅ PUT /api/feedback/{id} (更新)'),
            ("@app.route('/api/feedback/<checkin_id>', methods=['DELETE'])", '✅ DELETE /api/feedback/{id} (删除)')
        ]
        
        routes_ok = True
        for pattern, message in routes:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.error(f"  ❌ 未找到: {pattern}")
                routes_ok = False
        
        return routes_ok
        
    except Exception as e:
        logger.error(f"❌ 检查后端路由时发生错误: {e}")
        return False

def generate_fix_summary():
    """生成修复总结"""
    
    logger.info("\n📊 API修复总结:")
    
    logger.info("\n🔧 问题分析:")
    logger.info("  ❌ 原问题: 前端调用 POST /api/feedback/{id}")
    logger.info("  ❌ 后端只有: POST /api/feedback (不带ID)")
    logger.info("  ❌ 结果: 405 METHOD NOT ALLOWED")
    
    logger.info("\n✅ 修复方案:")
    logger.info("  1. 前端逻辑修复:")
    logger.info("     - 添加 isUpdate 判断")
    logger.info("     - 创建: POST /api/feedback")
    logger.info("     - 更新: PUT /api/feedback/{id}")
    
    logger.info("\n  2. API调用流程:")
    logger.info("     - 检查 feedbacks[checkinId] 是否存在")
    logger.info("     - 存在 → 更新 → PUT /api/feedback/{id}")
    logger.info("     - 不存在 → 创建 → POST /api/feedback")
    
    logger.info("\n🎯 修复效果:")
    logger.info("  ✅ 解决405错误")
    logger.info("  ✅ 前后端API匹配")
    logger.info("  ✅ RESTful设计规范")
    logger.info("  ✅ 创建和更新逻辑清晰")

def simulate_scenarios():
    """模拟使用场景"""
    
    logger.info("\n🎭 使用场景模拟:")
    
    scenarios = [
        {
            "场景": "首次创建feedback",
            "条件": "feedbacks[CHK20250004] = undefined",
            "逻辑": "isUpdate = false",
            "API调用": "POST /api/feedback",
            "预期": "201 Created"
        },
        {
            "场景": "编辑现有feedback",
            "条件": "feedbacks[CHK20250004] = {...}",
            "逻辑": "isUpdate = true", 
            "API调用": "PUT /api/feedback/CHK20250004",
            "预期": "200 OK"
        }
    ]
    
    for scenario in scenarios:
        logger.info(f"\n  📋 {scenario['场景']}:")
        logger.info(f"    条件: {scenario['条件']}")
        logger.info(f"    逻辑: {scenario['逻辑']}")
        logger.info(f"    API调用: {scenario['API调用']}")
        logger.info(f"    预期: {scenario['预期']}")

if __name__ == '__main__':
    # 检查API修复
    api_fix_ok = check_api_fix()
    
    # 检查后端路由
    backend_ok = check_backend_routes()
    
    # 生成修复总结
    generate_fix_summary()
    
    # 模拟使用场景
    simulate_scenarios()
    
    if api_fix_ok and backend_ok:
        logger.info("\n🎊 Feedback API修复验证成功！")
        logger.info("✅ 前端API调用逻辑正确")
        logger.info("✅ 后端路由完整")
        logger.info("✅ 405错误已解决")
        logger.info("✅ 创建和更新逻辑清晰")
        logger.info("\n🚀 现在可以正常保存feedback了！")
        logger.info("\n💡 测试建议:")
        logger.info("  1. 尝试创建新的feedback")
        logger.info("  2. 尝试编辑现有的feedback")
        logger.info("  3. 检查浏览器控制台是否还有错误")
    else:
        logger.error("\n❌ Feedback API修复验证失败")
        if not api_fix_ok:
            logger.error("⚠️  前端API修复有问题")
        if not backend_ok:
            logger.error("⚠️  后端路由有问题")
    
    sys.exit(0 if (api_fix_ok and backend_ok) else 1)
