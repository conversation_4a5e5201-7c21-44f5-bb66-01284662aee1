#!/usr/bin/env python3
"""
测试Courses CRUD操作
验证数据访问层的courses操作是否正常工作
"""

import sys
import os
import json
import logging
from datetime import datetime

# 设置路径和环境变量
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
os.environ['DATA_MODE'] = "database"

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_courses_crud():
    """测试Courses的CRUD操作"""
    
    logger.info("🧪 开始测试Courses数据库操作")
    logger.info("=" * 80)
    
    try:
        from database_access import DataAccessLayer
        
        # 使用database模式
        dal = DataAccessLayer(mode='database')
        logger.info(f"数据访问层模式: {dal.mode}")
        
        # 测试数据 - 模拟courses.json的结构
        test_courses_data = {
            "beginner": {
                "id": "beginner",
                "name": {
                    "en": "Beginner Course",
                    "zh": "初级课程",
                    "nl": "Beginnerscursus"
                },
                "description": {
                    "en": "Perfect for first-time snowboarders",
                    "zh": "适合初次接触滑雪板的学员",
                    "nl": "Perfect voor eerste keer snowboarders"
                },
                "level": "beginner",
                "duration": "2 hours",
                "price": {
                    "adult": 80,
                    "child": 60
                },
                "features": [
                    "Basic equipment introduction",
                    "Safety guidelines",
                    "First steps on snow"
                ],
                "subcourses": {
                    "beginner-1": {
                        "id": "beginner-1",
                        "name": {
                            "en": "First Steps",
                            "zh": "第一步",
                            "nl": "Eerste Stappen"
                        },
                        "description": {
                            "en": "Learn the basics",
                            "zh": "学习基础知识",
                            "nl": "Leer de basis"
                        }
                    }
                }
            },
            "intermediate": {
                "id": "intermediate", 
                "name": {
                    "en": "Intermediate Course",
                    "zh": "中级课程",
                    "nl": "Gevorderde cursus"
                },
                "description": {
                    "en": "For those with some experience",
                    "zh": "适合有一定经验的学员",
                    "nl": "Voor degenen met wat ervaring"
                },
                "level": "intermediate",
                "duration": "3 hours",
                "price": {
                    "adult": 120,
                    "child": 90
                }
            }
        }
        
        course_id = 'main'  # We use 'main' as the single ID for all courses data
        
        # 1. 测试创建操作
        logger.info("📝 测试创建操作...")
        create_success = dal.create('courses', course_id, test_courses_data)
        if create_success:
            logger.info("✅ 创建操作成功")
        else:
            logger.error("❌ 创建操作失败")
            return False
        
        # 2. 测试读取操作
        logger.info("📖 测试读取操作...")
        retrieved_courses = dal.get_all('courses')
        if retrieved_courses:
            logger.info("✅ 读取操作成功")
            logger.info(f"   读取到 {len(retrieved_courses)} 个课程类别")
            
            # 验证数据结构
            if 'beginner' in retrieved_courses and 'intermediate' in retrieved_courses:
                logger.info("✅ 课程数据结构验证成功")
                logger.info(f"   初级课程: {retrieved_courses['beginner']['name']['en']}")
                logger.info(f"   中级课程: {retrieved_courses['intermediate']['name']['en']}")
            else:
                logger.error("❌ 课程数据结构验证失败")
                return False
        else:
            logger.error("❌ 读取操作失败")
            return False
        
        # 3. 测试更新操作
        logger.info("✏️  测试更新操作...")
        updated_courses_data = test_courses_data.copy()
        updated_courses_data['advanced'] = {
            "id": "advanced",
            "name": {
                "en": "Advanced Course",
                "zh": "高级课程", 
                "nl": "Gevorderde cursus"
            },
            "description": {
                "en": "For expert snowboarders",
                "zh": "适合专业滑雪板手",
                "nl": "Voor expert snowboarders"
            },
            "level": "advanced",
            "duration": "4 hours",
            "price": {
                "adult": 160,
                "child": 120
            }
        }
        
        update_success = dal.update('courses', course_id, updated_courses_data)
        if update_success:
            logger.info("✅ 更新操作成功")
            
            # 验证更新结果
            updated_courses = dal.get_all('courses')
            if updated_courses and 'advanced' in updated_courses:
                logger.info("✅ 更新内容验证成功")
                logger.info(f"   新增高级课程: {updated_courses['advanced']['name']['en']}")
            else:
                logger.error("❌ 更新内容验证失败")
                return False
        else:
            logger.error("❌ 更新操作失败")
            return False
        
        # 4. 测试删除操作
        logger.info("🗑️  测试删除操作...")
        delete_success = dal.delete('courses', course_id)
        if delete_success:
            logger.info("✅ 删除操作成功")
            
            # 验证删除结果
            deleted_courses = dal.get_all('courses')
            if not deleted_courses or len(deleted_courses) == 0:
                logger.info("✅ 删除结果验证成功")
            else:
                logger.error("❌ 删除结果验证失败")
                return False
        else:
            logger.error("❌ 删除操作失败")
            return False
        
        logger.info("\n🎉 所有Courses数据库操作测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_consistency():
    """测试API一致性"""
    
    logger.info("\n🔗 测试API一致性...")
    
    try:
        import requests
        
        # 首先创建一些测试数据
        from database_access import DataAccessLayer
        dal = DataAccessLayer(mode='database')
        
        test_data = {
            "test_course": {
                "id": "test_course",
                "name": {"en": "Test Course", "zh": "测试课程"},
                "level": "beginner"
            }
        }
        
        dal.create('courses', 'main', test_data)
        
        # 测试GET API
        response = requests.get('http://127.0.0.1:8899/data/courses.json')
        if response.status_code == 200:
            api_courses = response.json()
            logger.info(f"✅ API返回 {len(api_courses)} 个课程")
            
            # 测试数据访问层
            dal_courses = dal.get_all('courses')
            
            if len(api_courses) == len(dal_courses):
                logger.info("✅ API和数据访问层返回的课程数量一致")
            else:
                logger.warning(f"⚠️ API返回{len(api_courses)}个课程，数据访问层返回{len(dal_courses)}个课程")
            
            # 测试POST API
            updated_data = test_data.copy()
            updated_data["updated_course"] = {
                "id": "updated_course",
                "name": {"en": "Updated Course", "zh": "更新课程"},
                "level": "intermediate"
            }
            
            post_response = requests.post('http://127.0.0.1:8899/data/courses.json', 
                                        json=updated_data,
                                        headers={'Content-Type': 'application/json'})
            
            if post_response.status_code == 200:
                logger.info("✅ POST API测试成功")
                
                # 验证更新结果
                verify_response = requests.get('http://127.0.0.1:8899/data/courses.json')
                if verify_response.status_code == 200:
                    verify_data = verify_response.json()
                    if 'updated_course' in verify_data:
                        logger.info("✅ 课程更新验证成功")
                    else:
                        logger.error("❌ 课程更新验证失败")
                        return False
            else:
                logger.error(f"❌ POST API测试失败: {post_response.status_code}")
                return False
            
            return True
        else:
            logger.error(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ API一致性测试失败: {e}")
        return False

if __name__ == "__main__":
    success = True
    
    # 测试CRUD操作
    if not test_courses_crud():
        success = False
    
    # 测试API一致性
    if not test_api_consistency():
        success = False
    
    if success:
        logger.info("\n🎉 所有测试通过！Courses CRUD操作正常工作。")
        sys.exit(0)
    else:
        logger.error("\n❌ 测试失败！请检查Courses数据库操作实现。")
        sys.exit(1)
