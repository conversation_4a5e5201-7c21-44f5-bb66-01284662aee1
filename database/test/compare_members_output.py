#!/usr/bin/env python3
"""
对比Members数据在database模式和json模式下的返回值
确保两种模式返回的数据完全一致
"""

import os
import sys
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def compare_members_output():
    """对比两种模式下的members数据输出"""
    
    # 设置环境
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
    os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
    
    try:
        from database_access import DataAccessLayer
        
        logger.info("🔍 开始对比Members数据输出")
        logger.info("=" * 80)
        
        # 1. 测试JSON模式
        logger.info("📄 测试JSON模式...")
        json_dal = DataAccessLayer(mode='json')
        json_data = json_dal.get_all('members')
        logger.info(f"JSON模式: 获取到 {len(json_data)} 条记录")
        
        # 2. 测试Database模式
        logger.info("🗄️  测试Database模式...")
        db_dal = DataAccessLayer(mode='database')
        db_data = db_dal.get_all('members')
        logger.info(f"Database模式: 获取到 {len(db_data)} 条记录")
        
        # 3. 对比数据数量
        if len(json_data) != len(db_data):
            logger.error(f"❌ 数据数量不一致: JSON={len(json_data)}, Database={len(db_data)}")
            return False
        
        logger.info(f"✅ 数据数量一致: {len(json_data)} 条记录")
        
        # 4. 对比具体数据
        logger.info("\n📊 详细数据对比:")
        
        mismatches = []
        matches = 0
        
        for member_id in json_data.keys():
            if member_id not in db_data:
                mismatches.append(f"Database中缺少成员: {member_id}")
                continue
            
            json_member = json_data[member_id]
            db_member = db_data[member_id]
            
            # 对比每个字段
            field_mismatches = []
            
            # 检查所有JSON中的字段
            for field in json_member.keys():
                json_value = json_member.get(field)
                db_value = db_member.get(field)
                
                if json_value != db_value:
                    field_mismatches.append(f"  {field}: JSON='{json_value}' vs DB='{db_value}'")
            
            # 检查Database中是否有额外字段
            for field in db_member.keys():
                if field not in json_member:
                    field_mismatches.append(f"  {field}: 仅在Database中存在='{db_member[field]}'")
            
            if field_mismatches:
                mismatches.append(f"成员 {member_id} 字段不匹配:")
                mismatches.extend(field_mismatches)
            else:
                matches += 1
                logger.info(f"✅ {member_id}: 完全匹配")
        
        # 检查Database中是否有JSON中没有的成员
        for member_id in db_data.keys():
            if member_id not in json_data:
                mismatches.append(f"JSON中缺少成员: {member_id}")
        
        # 5. 输出结果
        logger.info(f"\n📈 对比结果:")
        logger.info(f"  完全匹配: {matches}/{len(json_data)}")
        logger.info(f"  不匹配: {len(mismatches)}")
        
        if mismatches:
            logger.error("\n❌ 发现不匹配项:")
            for mismatch in mismatches[:10]:  # 只显示前10个
                logger.error(f"  {mismatch}")
            if len(mismatches) > 10:
                logger.error(f"  ... 还有 {len(mismatches) - 10} 个不匹配项")
            return False
        else:
            logger.info("\n🎉 所有数据完全匹配！")
            
            # 6. 显示数据结构示例
            logger.info("\n📋 数据结构示例:")
            sample_id = list(json_data.keys())[0]
            logger.info(f"JSON格式 ({sample_id}):")
            for key, value in json_data[sample_id].items():
                logger.info(f"  {key}: {value}")
            
            logger.info(f"\nDatabase格式 ({sample_id}):")
            for key, value in db_data[sample_id].items():
                logger.info(f"  {key}: {value}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 对比过程中发生错误: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def test_specific_member():
    """测试特定成员的数据"""
    
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
    os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
    
    try:
        from database_access import DataAccessLayer
        
        logger.info("\n🔍 测试特定成员数据...")
        
        # 选择一个测试成员
        test_member_id = "SN20210001"
        
        # JSON模式
        json_dal = DataAccessLayer(mode='json')
        json_data = json_dal.get_all('members')
        json_member = json_data.get(test_member_id)
        
        # Database模式
        db_dal = DataAccessLayer(mode='database')
        db_data = db_dal.get_all('members')
        db_member = db_data.get(test_member_id)
        
        logger.info(f"\n成员 {test_member_id} 详细对比:")
        logger.info("JSON数据:")
        logger.info(json.dumps(json_member, indent=2, ensure_ascii=False))
        
        logger.info("\nDatabase数据:")
        logger.info(json.dumps(db_member, indent=2, ensure_ascii=False))
        
        # 字段级对比
        logger.info("\n字段级对比:")
        all_fields = set(json_member.keys()) | set(db_member.keys())
        for field in sorted(all_fields):
            json_val = json_member.get(field, "❌ 缺失")
            db_val = db_member.get(field, "❌ 缺失")
            status = "✅" if json_val == db_val else "❌"
            logger.info(f"  {status} {field}: JSON='{json_val}' | DB='{db_val}'")
        
    except Exception as e:
        logger.error(f"❌ 测试特定成员时发生错误: {e}")

if __name__ == '__main__':
    success = compare_members_output()
    test_specific_member()
    sys.exit(0 if success else 1)
