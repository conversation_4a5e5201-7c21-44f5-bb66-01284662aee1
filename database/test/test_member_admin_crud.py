#!/usr/bin/env python3
"""
测试Member Admin的CRUD操作是否正确使用database接口
"""

import os
import sys
import json
import logging
import time
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_member_admin_crud():
    """测试Member Admin的CRUD操作"""
    
    # 设置环境
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
    os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
    
    try:
        from database_access import DataAccessLayer
        
        logger.info("🔍 测试Member Admin CRUD操作")
        logger.info("=" * 80)
        
        # 创建database模式的实例
        dal = DataAccessLayer(mode='database')
        
        # 1. 测试READ操作 (GET /data/members.json)
        logger.info("📖 测试READ操作...")
        members_before = dal.get_all('members')
        logger.info(f"  当前数据库中有 {len(members_before)} 个成员")
        
        # 2. 测试CREATE操作 (POST /api/member)
        logger.info("\n➕ 测试CREATE操作...")
        test_member_id = f"SN2025TEST{int(time.time())}"
        test_member = {
            "id": test_member_id,
            "name": "Test Member Admin",
            "isActive": True,
            "validityPeriod": "01/01/2025 - 31/12/2025",
            "gender": "test"
        }
        
        # 创建成员
        created_member = dal.create('members', test_member_id, test_member)
        if created_member:
            logger.info(f"  ✅ 成功创建测试成员: {test_member_id}")
        else:
            logger.error(f"  ❌ 创建测试成员失败")
            return False
        
        # 验证创建
        members_after_create = dal.get_all('members')
        if len(members_after_create) == len(members_before) + 1:
            logger.info(f"  ✅ 数据库中成员数量正确增加: {len(members_after_create)}")
        else:
            logger.error(f"  ❌ 数据库中成员数量不正确: 期望{len(members_before) + 1}, 实际{len(members_after_create)}")
            return False
        
        # 3. 测试UPDATE操作 (PUT /api/member/<id>)
        logger.info("\n✏️  测试UPDATE操作...")
        updated_data = {
            "id": test_member_id,
            "name": "Updated Test Member Admin",
            "isActive": False,
            "validityPeriod": "01/01/2025 - 31/12/2025",
            "gender": "updated"
        }
        
        # 更新成员
        updated_member = dal.update('members', test_member_id, updated_data)
        if updated_member:
            logger.info(f"  ✅ 成功更新测试成员: {test_member_id}")
            
            # 验证更新
            retrieved_member = dal.get_by_id('members', test_member_id)
            if retrieved_member and retrieved_member['name'] == "Updated Test Member Admin":
                logger.info(f"  ✅ 更新内容正确保存到数据库")
            else:
                logger.error(f"  ❌ 更新内容未正确保存到数据库")
                return False
        else:
            logger.error(f"  ❌ 更新测试成员失败")
            return False
        
        # 4. 测试DELETE操作 (DELETE /api/member/<id>)
        logger.info("\n🗑️  测试DELETE操作...")
        
        # 删除成员
        deleted = dal.delete('members', test_member_id)
        if deleted:
            logger.info(f"  ✅ 成功删除测试成员: {test_member_id}")
        else:
            logger.error(f"  ❌ 删除测试成员失败")
            return False
        
        # 验证删除
        members_after_delete = dal.get_all('members')
        if len(members_after_delete) == len(members_before):
            logger.info(f"  ✅ 数据库中成员数量正确恢复: {len(members_after_delete)}")
        else:
            logger.error(f"  ❌ 数据库中成员数量不正确: 期望{len(members_before)}, 实际{len(members_after_delete)}")
            return False
        
        # 确认成员已被删除
        deleted_member = dal.get_by_id('members', test_member_id)
        if deleted_member is None:
            logger.info(f"  ✅ 测试成员已从数据库中完全删除")
        else:
            logger.error(f"  ❌ 测试成员仍存在于数据库中")
            return False
        
        logger.info("\n🎉 所有Member Admin CRUD操作测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def check_member_admin_file():
    """检查member_admin.html文件的修改"""
    
    logger.info("\n🔍 检查member_admin.html文件修改...")
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'member_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改
        checks = [
            ('async function addNewMember()', '✅ addNewMember函数已改为async'),
            ('fetch(\'/api/member\', {', '✅ CREATE操作使用POST /api/member'),
            ('async function deleteMember(', '✅ deleteMember函数已改为async'),
            ('fetch(`/api/member/${memberId}`, { method: \'DELETE\'', '✅ DELETE操作使用DELETE /api/member/<id>'),
            ('async function updateMember(', '✅ 添加了updateMember函数'),
            ('fetch(`/api/member/${memberId}`, { method: \'PUT\'', '✅ UPDATE操作使用PUT /api/member/<id>'),
            ('await updateMember(memberId, member)', '✅ 字段更新时调用updateMember'),
            ('onclick="loadMembers()">Reload Data</button>', '✅ Save按钮改为Reload Data')
        ]
        
        for pattern, message in checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        # 检查是否还有旧的批量保存逻辑
        old_patterns = [
            ('fetch(\'data/members.json\', { method: \'POST\'', '⚠️  仍有批量POST到JSON文件'),
            ('saveMembers().then(', '⚠️  仍有saveMembers调用链')
        ]
        
        for pattern, warning in old_patterns:
            if pattern in content:
                logger.warning(f"  {warning}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查member_admin.html时发生错误: {e}")
        return False

def test_api_endpoints():
    """测试API端点是否正确配置"""
    
    logger.info("\n🔍 检查server.py中的API端点...")
    
    server_file = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'server.py')
    
    try:
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查必要的API端点
        endpoints = [
            ('@app.route(\'/api/member\', methods=[\'POST\'])', '✅ POST /api/member端点存在'),
            ('@app.route(\'/api/member/<member_id>\', methods=[\'GET\'])', '✅ GET /api/member/<id>端点存在'),
            ('@app.route(\'/api/member/<member_id>\', methods=[\'PUT\'])', '✅ PUT /api/member/<id>端点存在'),
            ('@app.route(\'/api/member/<member_id>\', methods=[\'DELETE\'])', '✅ DELETE /api/member/<id>端点存在'),
            ('data_access.create(\'members\'', '✅ CREATE使用data_access'),
            ('data_access.get_by_id(\'members\'', '✅ GET使用data_access'),
            ('data_access.update(\'members\'', '✅ UPDATE使用data_access'),
            ('data_access.delete(\'members\'', '✅ DELETE使用data_access')
        ]
        
        for pattern, message in endpoints:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查server.py时发生错误: {e}")
        return False

if __name__ == '__main__':
    # 检查文件修改
    check_member_admin_file()
    
    # 检查API端点
    test_api_endpoints()
    
    # 运行CRUD测试
    success = test_member_admin_crud()
    
    if success:
        logger.info("\n🎊 Member Admin CRUD操作已成功迁移到database接口！")
    else:
        logger.error("\n❌ Member Admin CRUD操作迁移存在问题，需要进一步检查")
    
    sys.exit(0 if success else 1)
