#!/usr/bin/env python3
"""
Test the data access layer with the migrated database
"""

import os
import sys
import json

def test_database_mode():
    """Test data access layer in database mode"""
    print("🧪 Testing Data Access Layer in Database Mode")
    print("=" * 50)
    
    # Set environment variables for database mode
    os.environ['DATABASE_URL'] = "postgresql://snownavi:SnowNavi2025!@localhost:5432/snownavi"
    os.environ['DATA_MODE'] = 'database'
    
    try:
        # Import our data access layer
        sys.path.append('backend')
        from database_access import DataAccessLayer
        
        # Create data access layer in database mode
        dal = DataAccessLayer(mode='database')
        print("✅ DataAccessLayer initialized in database mode")
        
        # Test getting members
        print("\n📥 Testing members retrieval...")
        try:
            members = dal.get_all('members')
            print(f"✅ Retrieved {len(members)} members from database")
            
            # Show sample members
            for i, (member_id, member) in enumerate(members.items()):
                if i < 3:  # Show first 3
                    print(f"  - {member_id}: {member.get('name')} ({member.get('email')})")
        except Exception as e:
            print(f"❌ Members retrieval failed: {e}")
        
        # Test getting activities
        print("\n📥 Testing activities retrieval...")
        try:
            activities = dal.get_all('activities')
            print(f"✅ Retrieved {len(activities)} activities from database")
            
            # Show sample activities
            for i, (activity_id, activity) in enumerate(activities.items()):
                if i < 3:  # Show first 3
                    print(f"  - {activity_id}: {activity.get('type')} on {activity.get('date')}")
        except Exception as e:
            print(f"❌ Activities retrieval failed: {e}")
        
        # Test getting a specific member
        print("\n📥 Testing single member retrieval...")
        try:
            member = dal.get_by_id('members', 'SN20210001')
            if member:
                print(f"✅ Retrieved member: {member.get('name')} ({member.get('email')})")
            else:
                print("⚠️  Member SN20210001 not found")
        except Exception as e:
            print(f"❌ Single member retrieval failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data access layer test failed: {e}")
        return False

def test_json_mode():
    """Test data access layer in JSON mode"""
    print("\n🧪 Testing Data Access Layer in JSON Mode")
    print("=" * 50)
    
    # Set environment variables for JSON mode
    os.environ['DATA_MODE'] = 'json'
    
    try:
        # Import our data access layer
        sys.path.append('backend')
        from database_access import DataAccessLayer
        
        # Create data access layer in JSON mode
        dal = DataAccessLayer(mode='json')
        print("✅ DataAccessLayer initialized in JSON mode")
        
        # Test getting members
        print("\n📥 Testing JSON members retrieval...")
        try:
            members = dal.get_all('members')
            print(f"✅ Retrieved {len(members)} members from JSON")
            
            # Show sample members
            for i, (member_id, member) in enumerate(members.items()):
                if i < 3:  # Show first 3
                    print(f"  - {member_id}: {member.get('name')} ({member.get('email')})")
        except Exception as e:
            print(f"❌ JSON members retrieval failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON mode test failed: {e}")
        return False

def test_hybrid_mode():
    """Test data access layer in hybrid mode"""
    print("\n🧪 Testing Data Access Layer in Hybrid Mode")
    print("=" * 50)
    
    # Set environment variables for hybrid mode
    os.environ['DATABASE_URL'] = "postgresql://snownavi:SnowNavi2025!@localhost:5432/snownavi"
    os.environ['DATA_MODE'] = 'hybrid'
    
    try:
        # Import our data access layer
        sys.path.append('backend')
        from database_access import DataAccessLayer
        
        # Create data access layer in hybrid mode
        dal = DataAccessLayer(mode='hybrid')
        print("✅ DataAccessLayer initialized in hybrid mode")
        
        # Test getting members (should use database)
        print("\n📥 Testing hybrid members retrieval...")
        try:
            members = dal.get_all('members')
            print(f"✅ Retrieved {len(members)} members in hybrid mode")
            
            # Show sample members
            for i, (member_id, member) in enumerate(members.items()):
                if i < 3:  # Show first 3
                    print(f"  - {member_id}: {member.get('name')} ({member.get('email')})")
        except Exception as e:
            print(f"❌ Hybrid members retrieval failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Hybrid mode test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 SnowNavi Data Access Layer Test")
    print("Testing all three storage modes...")
    print()
    
    success_count = 0
    
    # Test database mode
    if test_database_mode():
        success_count += 1
    
    # Test JSON mode
    if test_json_mode():
        success_count += 1
    
    # Test hybrid mode
    if test_hybrid_mode():
        success_count += 1
    
    print(f"\n✨ Test Results: {success_count}/3 modes working")
    
    if success_count == 3:
        print("🎉 All storage modes are working correctly!")
        print("\nMigration is complete and verified!")
        print("\nNext steps:")
        print("1. Update backend/.env to set DATA_MODE=hybrid")
        print("2. Restart the Flask server")
        print("3. Test the web application")
        print("4. Once verified, switch to DATA_MODE=database")
        return True
    else:
        print("⚠️  Some storage modes have issues")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
