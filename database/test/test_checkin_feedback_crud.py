#!/usr/bin/env python3
"""
检查Checkin页面中Feedback部分是否完全使用数据库CRUD
确保数据一致性和统一的数据访问方式
"""

import os
import sys
import logging
import re
import json

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_frontend_feedback_apis():
    """检查前端feedback相关的API调用"""
    
    logger.info("🔍 检查前端Feedback API调用")
    logger.info("=" * 80)
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'checkin_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否仍在使用JSON文件直接访问
        logger.info("\n📁 JSON文件直接访问检查:")

        json_access_patterns = [
            (r'fetch\([\'"]data/feedbacks\.json[\'"]', '❌ 直接访问feedbacks.json'),
            (r'fetch\([\'"]data/feedback_templates\.json[\'"]', '❌ 直接访问feedback_templates.json'),
            (r'await saveFeedbacks\(\)', '❌ 使用旧的saveFeedbacks函数'),
            (r'loadFeedbacks\(\)', '⚠️  使用loadFeedbacks函数')
        ]
        
        json_access_found = False
        for pattern, message in json_access_patterns:
            matches = re.findall(pattern, content)
            if matches:
                logger.warning(f"  {message} - 找到 {len(matches)} 处")
                json_access_found = True
            else:
                logger.info(f"  ✅ 未发现: {message}")
        
        # 检查是否使用了数据库API
        logger.info("\n🗄️  数据库API使用检查:")
        
        db_api_patterns = [
            (r'fetch\([\'"][^\'\"]*api/feedback[^\'\"]*[\'"]', '✅ 使用feedback API'),
            (r'fetch\([\'"][^\'\"]*api/member-feedback[^\'\"]*[\'"]', '✅ 使用member feedback API'),
            (r'fetch\([\'"][^\'\"]*api/member-skills-progress[^\'\"]*[\'"]', '✅ 使用skills progress API'),
            (r'/api/feedback/', '✅ 使用RESTful feedback API')
        ]
        
        db_api_found = False
        for pattern, message in db_api_patterns:
            matches = re.findall(pattern, content)
            if matches:
                logger.info(f"  {message} - 找到 {len(matches)} 处")
                db_api_found = True
            else:
                logger.warning(f"  ⚠️  未发现: {message}")
        
        return not json_access_found and db_api_found
        
    except Exception as e:
        logger.error(f"❌ 检查前端API调用时发生错误: {e}")
        return False

def check_backend_feedback_apis():
    """检查后端feedback相关的API实现"""
    
    logger.info("\n🔧 检查后端Feedback API实现:")
    
    server_file = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'server.py')
    
    try:
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有完整的CRUD API
        crud_apis = [
            (r'@app\.route\([\'"][^\'\"]*api/feedback[\'"].*POST', '✅ CREATE feedback API'),
            (r'@app\.route\([\'"][^\'\"]*api/feedback.*GET', '✅ READ feedback API'),
            (r'@app\.route\([\'"][^\'\"]*api/feedback.*PUT', '✅ UPDATE feedback API'),
            (r'@app\.route\([\'"][^\'\"]*api/feedback.*DELETE', '✅ DELETE feedback API')
        ]
        
        crud_complete = True
        for pattern, message in crud_apis:
            if re.search(pattern, content):
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未发现: {message}")
                crud_complete = False
        
        # 检查是否使用了data_access抽象层
        logger.info("\n🔄 数据访问抽象层使用检查:")
        
        data_access_patterns = [
            (r'data_access\.get_all\([\'"]feedbacks[\'"]', '✅ 使用data_access获取feedbacks'),
            (r'data_access\.create\([\'"]feedbacks[\'"]', '✅ 使用data_access创建feedback'),
            (r'data_access\.update\([\'"]feedbacks[\'"]', '✅ 使用data_access更新feedback'),
            (r'data_access\.delete\([\'"]feedbacks[\'"]', '✅ 使用data_access删除feedback')
        ]
        
        data_access_used = False
        for pattern, message in data_access_patterns:
            if re.search(pattern, content):
                logger.info(f"  {message}")
                data_access_used = True
            else:
                logger.warning(f"  ⚠️  未发现: {message}")
        
        # 检查是否仍有直接文件操作
        logger.info("\n📁 直接文件操作检查:")
        
        file_operations = [
            (r'feedbacks_file.*json\.load', '❌ 直接读取feedbacks.json'),
            (r'feedbacks_file.*json\.dump', '❌ 直接写入feedbacks.json'),
            (r'with open\(.*feedbacks\.json', '❌ 直接操作feedbacks.json文件')
        ]
        
        file_ops_found = False
        for pattern, message in file_operations:
            matches = re.findall(pattern, content)
            if matches:
                logger.warning(f"  {message} - 找到 {len(matches)} 处")
                file_ops_found = True
            else:
                logger.info(f"  ✅ 未发现: {message}")
        
        return crud_complete and data_access_used and not file_ops_found
        
    except Exception as e:
        logger.error(f"❌ 检查后端API实现时发生错误: {e}")
        return False

def check_data_access_layer():
    """检查数据访问层的feedback实现"""
    
    logger.info("\n🗃️  检查数据访问层Feedback实现:")
    
    data_access_file = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'database_access.py')
    
    try:
        with open(data_access_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查feedbacks的数据库操作
        feedback_operations = [
            (r'elif entity_type == [\'"]feedbacks[\'"]:', '✅ feedbacks实体类型支持'),
            (r'INSERT INTO feedbacks', '✅ feedbacks数据库插入'),
            (r'UPDATE feedbacks', '✅ feedbacks数据库更新'),
            (r'DELETE FROM feedbacks', '✅ feedbacks数据库删除'),
            (r'SELECT \* FROM feedbacks', '✅ feedbacks数据库查询')
        ]
        
        db_ops_complete = True
        for pattern, message in feedback_operations:
            if re.search(pattern, content):
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未发现: {message}")
                db_ops_complete = False
        
        return db_ops_complete
        
    except Exception as e:
        logger.error(f"❌ 检查数据访问层时发生错误: {e}")
        return False

def check_database_schema():
    """检查数据库schema中的feedback表"""
    
    logger.info("\n🏗️  检查数据库Schema:")
    
    schema_file = os.path.join(os.path.dirname(__file__), '..', 'sql', 'schema.sql')
    
    try:
        with open(schema_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查feedbacks表定义
        schema_checks = [
            (r'CREATE TABLE.*feedbacks', '✅ feedbacks表定义'),
            (r'id VARCHAR\(20\) PRIMARY KEY', '✅ 主键定义'),
            (r'checkin_id VARCHAR\(20\) REFERENCES checkins\(id\)', '✅ checkin外键'),
            (r'activity_id VARCHAR\(20\) REFERENCES activities\(id\)', '✅ activity外键'),
            (r'member_id VARCHAR\(20\) REFERENCES members\(id\)', '✅ member外键'),
            (r'skill_assessment JSONB', '✅ skill_assessment JSONB字段'),
            (r'student_rating JSONB', '✅ student_rating JSONB字段'),
            (r'created_at TIMESTAMP', '✅ 创建时间字段'),
            (r'updated_at TIMESTAMP', '✅ 更新时间字段')
        ]
        
        schema_complete = True
        for pattern, message in schema_checks:
            if re.search(pattern, content):
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未发现: {message}")
                schema_complete = False
        
        # 检查索引
        index_checks = [
            (r'idx_feedbacks_activity_id', '✅ activity_id索引'),
            (r'idx_feedbacks_member_id', '✅ member_id索引')
        ]
        
        for pattern, message in index_checks:
            if re.search(pattern, content):
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未发现: {message}")
        
        return schema_complete
        
    except Exception as e:
        logger.error(f"❌ 检查数据库schema时发生错误: {e}")
        return False

def generate_migration_recommendations():
    """生成迁移建议"""
    
    logger.info("\n📋 Feedback数据库CRUD迁移建议:")
    
    recommendations = [
        {
            "问题": "前端仍使用JSON文件直接访问",
            "现状": "checkin_admin.html中使用fetch('data/feedbacks.json')",
            "建议": "改为使用RESTful API: /api/feedback",
            "优先级": "高"
        },
        {
            "问题": "后端API未完全使用数据访问层",
            "现状": "server.py中仍有直接文件操作",
            "建议": "所有feedback操作改为使用data_access抽象层",
            "优先级": "高"
        },
        {
            "问题": "批量更新操作未实现",
            "现状": "saveFeedbacks()仍使用文件批量写入",
            "建议": "实现data_access.bulk_update()方法",
            "优先级": "中"
        },
        {
            "问题": "数据一致性检查缺失",
            "现状": "JSON和数据库可能不同步",
            "建议": "添加数据一致性验证和同步机制",
            "优先级": "中"
        }
    ]
    
    for rec in recommendations:
        logger.info(f"\n  🔧 {rec['问题']}")
        logger.info(f"    现状: {rec['现状']}")
        logger.info(f"    建议: {rec['建议']}")
        logger.info(f"    优先级: {rec['优先级']}")

if __name__ == '__main__':
    # 检查前端API调用
    frontend_ok = check_frontend_feedback_apis()
    
    # 检查后端API实现
    backend_ok = check_backend_feedback_apis()
    
    # 检查数据访问层
    data_access_ok = check_data_access_layer()
    
    # 检查数据库schema
    schema_ok = check_database_schema()
    
    # 生成迁移建议
    generate_migration_recommendations()
    
    if frontend_ok and backend_ok and data_access_ok and schema_ok:
        logger.info("\n🎊 Checkin页面Feedback部分完全使用数据库CRUD！")
        logger.info("✅ 前端使用RESTful API")
        logger.info("✅ 后端使用数据访问层")
        logger.info("✅ 数据库schema完整")
        logger.info("✅ 数据一致性保证")
    else:
        logger.error("\n❌ Checkin页面Feedback部分未完全使用数据库CRUD")
        if not frontend_ok:
            logger.error("⚠️  前端仍有JSON文件直接访问")
        if not backend_ok:
            logger.error("⚠️  后端API未完全使用数据访问层")
        if not data_access_ok:
            logger.error("⚠️  数据访问层实现不完整")
        if not schema_ok:
            logger.error("⚠️  数据库schema有问题")
    
    sys.exit(0 if (frontend_ok and backend_ok and data_access_ok and schema_ok) else 1)
