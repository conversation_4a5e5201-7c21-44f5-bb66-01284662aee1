#!/usr/bin/env python3
"""
测试Email Template CRUD操作
验证数据访问层的email_templates操作是否正常工作
"""

import sys
import os
import json
import logging
from datetime import datetime

# 设置路径和环境变量
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
os.environ['DATA_MODE'] = "database"

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_email_template_crud():
    """测试Email Template的CRUD操作"""
    
    logger.info("🧪 开始测试Email Template数据库操作")
    logger.info("=" * 80)
    
    try:
        from database_access import DataAccessLayer
        
        # 使用database模式
        dal = DataAccessLayer(mode='database')
        logger.info(f"数据访问层模式: {dal.mode}")
        
        # 测试数据
        test_template_id = f"test_email_template_{int(datetime.now().timestamp())}"
        test_template_data = {
            'id': test_template_id,
            'name': 'Test Email Template',
            'description': 'Test email template for database operations',
            'type': 'feedback',
            'subject': '🎿 Test Email - {{courseName}}',
            'htmlContent': '''
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <div style="background: #E53512; color: white; padding: 20px; text-align: center;">
                        <h1>🎿 SnowNavi Test</h1>
                    </div>
                    <div style="padding: 20px;">
                        <p>Dear {{studentName}},</p>
                        <p>This is a test email template.</p>
                        <p>Course: {{courseName}}</p>
                        <p>Instructor: {{instructorName}}</p>
                    </div>
                </div>
            ''',
            'triggers': {
                'onFeedbackComplete': True,
                'onFeedbackUpdate': False,
                'manualOnly': False
            },
            'active': True,
            'createdAt': datetime.now().isoformat(),
            'updatedAt': datetime.now().isoformat()
        }
        
        # 1. 测试创建操作
        logger.info("📝 测试创建操作...")
        create_success = dal.create('email_templates', test_template_id, test_template_data)
        if create_success:
            logger.info("✅ 创建操作成功")
        else:
            logger.error("❌ 创建操作失败")
            return False
        
        # 2. 测试读取操作
        logger.info("📖 测试读取操作...")
        retrieved_template = dal.get_by_id('email_templates', test_template_id)
        if retrieved_template:
            logger.info("✅ 读取操作成功")
            logger.info(f"   模板名称: {retrieved_template['name']}")
            logger.info(f"   模板类型: {retrieved_template['type']}")
            logger.info(f"   是否激活: {retrieved_template['active']}")
        else:
            logger.error("❌ 读取操作失败")
            return False
        
        # 3. 测试更新操作
        logger.info("✏️  测试更新操作...")
        updated_data = test_template_data.copy()
        updated_data['name'] = 'Updated Test Email Template'
        updated_data['subject'] = '🎿 Updated Test Email - {{courseName}}'
        updated_data['active'] = False
        updated_data['updatedAt'] = datetime.now().isoformat()
        
        update_success = dal.update('email_templates', test_template_id, updated_data)
        if update_success:
            logger.info("✅ 更新操作成功")
            
            # 验证更新结果
            updated_template = dal.get_by_id('email_templates', test_template_id)
            if updated_template and updated_template['name'] == 'Updated Test Email Template':
                logger.info("✅ 更新内容验证成功")
            else:
                logger.error("❌ 更新内容验证失败")
                return False
        else:
            logger.error("❌ 更新操作失败")
            return False
        
        # 4. 测试删除操作
        logger.info("🗑️  测试删除操作...")
        delete_success = dal.delete('email_templates', test_template_id)
        if delete_success:
            logger.info("✅ 删除操作成功")
            
            # 验证删除结果
            deleted_template = dal.get_by_id('email_templates', test_template_id)
            if not deleted_template:
                logger.info("✅ 删除结果验证成功")
            else:
                logger.error("❌ 删除结果验证失败")
                return False
        else:
            logger.error("❌ 删除操作失败")
            return False
        
        logger.info("\n🎉 所有Email Template数据库操作测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_existing_templates():
    """测试现有模板的读取"""
    
    logger.info("\n📋 测试现有模板读取...")
    
    try:
        from database_access import DataAccessLayer
        
        dal = DataAccessLayer(mode='database')
        templates = dal.get_all('email_templates')
        
        logger.info(f"数据库中共有 {len(templates)} 个邮件模板:")
        for template_id, template in templates.items():
            logger.info(f"  - {template_id}: {template['name']} ({template['type']}, active: {template['active']})")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 读取现有模板时发生错误: {e}")
        return False

def test_api_consistency():
    """测试API一致性"""
    
    logger.info("\n🔗 测试API一致性...")
    
    try:
        import requests
        
        # 测试GET API
        response = requests.get('http://127.0.0.1:8899/api/email-templates')
        if response.status_code == 200:
            api_templates = response.json()
            logger.info(f"✅ API返回 {len(api_templates)} 个邮件模板")
            
            # 测试数据访问层
            from database_access import DataAccessLayer
            dal = DataAccessLayer(mode='database')
            dal_templates = dal.get_all('email_templates')
            
            if len(api_templates) == len(dal_templates):
                logger.info("✅ API和数据访问层返回的模板数量一致")
            else:
                logger.warning(f"⚠️ API返回{len(api_templates)}个模板，数据访问层返回{len(dal_templates)}个模板")
            
            return True
        else:
            logger.error(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ API一致性测试失败: {e}")
        return False

if __name__ == "__main__":
    success = True
    
    # 测试现有模板读取
    if not test_existing_templates():
        success = False
    
    # 测试CRUD操作
    if not test_email_template_crud():
        success = False
    
    # 测试API一致性
    if not test_api_consistency():
        success = False
    
    if success:
        logger.info("\n🎉 所有测试通过！Email Template CRUD操作正常工作。")
        sys.exit(0)
    else:
        logger.error("\n❌ 测试失败！请检查Email Template数据库操作实现。")
        sys.exit(1)
