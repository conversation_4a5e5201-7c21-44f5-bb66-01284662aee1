#!/usr/bin/env python3
"""
测试删除成员功能修复
验证删除操作能正确保存到数据库
"""

import os
import sys
import json
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_delete_implementation():
    """检查删除功能实现"""
    
    logger.info("🔍 检查删除成员功能修复")
    logger.info("=" * 80)
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'member_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查全局变量
        logger.info("\n📋 全局变量检查:")
        
        global_checks = [
            ('let deletedMembers = [];', '✅ deletedMembers数组声明'),
            ('Track deleted members for database deletion', '✅ 注释说明')
        ]
        
        for pattern, message in global_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        # 检查deleteMember函数修改
        logger.info("\n🗑️  deleteMember函数检查:")
        
        delete_checks = [
            ('if (!deletedMembers.includes(memberId))', '✅ 检查重复删除'),
            ('deletedMembers.push(memberId);', '✅ 添加到删除列表'),
            ('delete members[memberId];', '✅ 从本地对象删除')
        ]
        
        for pattern, message in delete_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        # 检查saveAllMembers函数修改
        logger.info("\n💾 saveAllMembers函数检查:")
        
        save_checks = [
            ('for (const memberId of deletedMembers)', '✅ 遍历删除列表'),
            ('await deleteMemberFromDatabase(memberId);', '✅ 调用数据库删除'),
            ('deleteSuccessCount++;', '✅ 删除成功计数'),
            ('deleteErrorCount++;', '✅ 删除错误计数'),
            ('deletedMembers = [];', '✅ 清空删除列表')
        ]
        
        for pattern, message in save_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        # 检查新增的API函数
        logger.info("\n🔌 API函数检查:")
        
        api_checks = [
            ('async function createMember(', '✅ createMember函数'),
            ('async function deleteMemberFromDatabase(', '✅ deleteMemberFromDatabase函数'),
            ('method: \'POST\'', '✅ POST方法创建'),
            ('method: \'DELETE\'', '✅ DELETE方法删除'),
            ('/api/member', '✅ 正确的API端点')
        ]
        
        for pattern, message in api_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        # 检查错误处理和统计
        logger.info("\n📊 错误处理和统计检查:")
        
        stats_checks = [
            ('deleteSuccessCount', '✅ 删除成功统计'),
            ('deleteErrorCount', '✅ 删除错误统计'),
            ('Successfully deleted', '✅ 删除成功消息'),
            ('totalErrors = errorCount + deleteErrorCount', '✅ 总错误计算'),
            ('totalSuccess = successCount + deleteSuccessCount', '✅ 总成功计算')
        ]
        
        for pattern, message in stats_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        # 检查loadMembers清理
        logger.info("\n🔄 loadMembers清理检查:")
        
        load_checks = [
            ('deletedMembers = [];', '✅ 清空删除列表')
        ]
        
        for pattern, message in load_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查删除功能时发生错误: {e}")
        return False

def test_database_delete_api():
    """测试数据库删除API"""
    
    logger.info("\n🧪 测试数据库删除API...")
    
    # 设置环境
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
    os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
    
    try:
        from database_access import DataAccessLayer
        
        # 创建database模式的实例
        dal = DataAccessLayer(mode='database')
        
        # 创建测试成员
        test_member_id = f"SN20250999"
        
        # 先删除可能存在的测试数据
        try:
            dal.delete('members', test_member_id)
        except:
            pass
        
        test_member = {
            "id": test_member_id,
            "name": "Delete Test Member",
            "isActive": True,
            "validityPeriod": "12/06/2025 - 12/06/2026",
            "gender": "test"
        }
        
        logger.info(f"📝 创建测试成员: {test_member_id}")
        created = dal.create('members', test_member_id, test_member)
        if not created:
            logger.error("❌ 创建测试成员失败")
            return False
        
        # 验证成员存在
        member = dal.get_by_id('members', test_member_id)
        if not member:
            logger.error("❌ 测试成员创建后无法找到")
            return False
        logger.info("✅ 测试成员创建成功")
        
        # 测试删除
        logger.info(f"🗑️  删除测试成员: {test_member_id}")
        deleted = dal.delete('members', test_member_id)
        if not deleted:
            logger.error("❌ 删除测试成员失败")
            return False
        
        # 验证成员已删除
        member_after_delete = dal.get_by_id('members', test_member_id)
        if member_after_delete:
            logger.error("❌ 成员删除后仍然存在")
            return False
        
        logger.info("✅ 数据库删除API测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库删除API测试失败: {e}")
        return False

def generate_delete_workflow():
    """生成删除工作流程"""
    
    logger.info("\n🔄 删除成员完整工作流程:")
    
    workflow = [
        "1. 用户点击Delete Member按钮",
        "2. 显示确认对话框",
        "3. 用户确认删除",
        "4. deleteMember()函数执行:",
        "   - 将memberId添加到deletedMembers数组",
        "   - 从本地members对象中删除",
        "   - 更新UI显示",
        "   - 调用markAsUnsaved()显示未保存状态",
        "5. 用户点击Save All Changes",
        "6. saveAllMembers()函数执行:",
        "   - 遍历deletedMembers数组",
        "   - 对每个ID调用deleteMemberFromDatabase()",
        "   - 发送DELETE请求到/api/member/{id}",
        "   - 统计删除成功/失败数量",
        "   - 清空deletedMembers数组",
        "   - 处理其他成员的更新/创建",
        "7. 显示操作结果统计",
        "8. 刷新UI显示最新状态"
    ]
    
    for step in workflow:
        logger.info(f"  {step}")

def generate_test_scenarios():
    """生成测试场景"""
    
    logger.info("\n🧪 删除功能测试场景:")
    
    scenarios = [
        {
            "name": "单个成员删除",
            "steps": [
                "1. 选择一个现有成员",
                "2. 点击Delete Member",
                "3. 确认删除",
                "4. 点击Save All Changes",
                "5. 验证成员从数据库中删除",
                "6. 刷新页面验证成员不再出现"
            ]
        },
        {
            "name": "多个成员删除",
            "steps": [
                "1. 删除成员A",
                "2. 删除成员B",
                "3. 修改成员C的信息",
                "4. 点击Save All Changes",
                "5. 验证A和B被删除，C被更新"
            ]
        },
        {
            "name": "删除后撤销",
            "steps": [
                "1. 删除一个成员",
                "2. 点击Discard Changes",
                "3. 验证成员恢复显示",
                "4. 验证deletedMembers数组被清空"
            ]
        },
        {
            "name": "新增后立即删除",
            "steps": [
                "1. 添加新成员",
                "2. 立即删除该成员",
                "3. 点击Save All Changes",
                "4. 验证没有多余的API调用"
            ]
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        logger.info(f"\n  场景 {i}: {scenario['name']}")
        for step in scenario['steps']:
            logger.info(f"    {step}")

if __name__ == '__main__':
    # 检查实现
    implementation_ok = check_delete_implementation()
    
    # 测试数据库API
    api_ok = test_database_delete_api()
    
    # 生成工作流程
    generate_delete_workflow()
    
    # 生成测试场景
    generate_test_scenarios()
    
    if implementation_ok and api_ok:
        logger.info("\n🎊 删除成员功能修复验证成功！")
        logger.info("✅ deletedMembers跟踪机制正确")
        logger.info("✅ saveAllMembers处理删除操作")
        logger.info("✅ 数据库删除API正常工作")
        logger.info("✅ 错误处理和统计完整")
        logger.info("✅ 删除操作能正确保存到数据库")
    else:
        logger.error("\n❌ 删除成员功能修复验证失败")
    
    sys.exit(0 if (implementation_ok and api_ok) else 1)
