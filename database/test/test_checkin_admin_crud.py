#!/usr/bin/env python3
"""
检查Checkin管理页面数据库CRUD操作
确保所有操作都正确使用数据库而不是JSON文件
"""

import os
import sys
import json
import logging
import time
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_checkin_admin_frontend():
    """检查Checkin管理前端页面"""
    
    logger.info("🔍 检查Checkin管理前端页面")
    logger.info("=" * 80)
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'checkin_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查数据加载
        logger.info("\n📥 数据加载检查:")
        
        load_checks = [
            ('fetch(\'data/checkins.json\')', '❌ 仍使用JSON文件加载'),
            ('fetch(\'data/members.json\')', '❌ 仍使用JSON文件加载'),
            ('fetch(\'data/activities.json\')', '❌ 仍使用JSON文件加载'),
            ('data_access', '⚠️  未找到数据访问层调用'),
            ('/api/checkins', '⚠️  未找到API调用'),
            ('/api/members', '⚠️  未找到API调用'),
            ('/api/activities', '⚠️  未找到API调用')
        ]
        
        json_file_usage = False
        for pattern, message in load_checks:
            if pattern in content:
                if 'JSON文件' in message:
                    logger.warning(f"  {message}")
                    json_file_usage = True
                else:
                    logger.info(f"  {message}")
            else:
                if 'JSON文件' in message:
                    logger.info(f"  ✅ 已移除: {pattern}")
                else:
                    logger.warning(f"  {message}")
        
        # 检查数据保存
        logger.info("\n💾 数据保存检查:")
        
        save_checks = [
            ('fetch(\'data/checkins.json\', {', '❌ 仍使用JSON文件保存'),
            ('method: \'POST\'', '✅ 使用POST方法'),
            ('/api/checkin', '⚠️  未找到API保存调用'),
            ('JSON.stringify(checkins', '❌ 直接序列化整个对象')
        ]
        
        for pattern, message in save_checks:
            if pattern in content:
                if '❌' in message:
                    logger.warning(f"  {message}")
                    json_file_usage = True
                else:
                    logger.info(f"  {message}")
            else:
                if '⚠️' in message:
                    logger.warning(f"  {message}")
                else:
                    logger.info(f"  ✅ 已移除: {pattern}")
        
        # 检查CRUD操作
        logger.info("\n🔧 CRUD操作检查:")
        
        crud_checks = [
            ('function addNewCheckin', '✅ 创建操作存在'),
            ('function updateCheckin', '✅ 更新操作存在'),
            ('function deleteCheckin', '✅ 删除操作存在'),
            ('function saveCheckins', '❌ 批量保存操作'),
            ('delete checkins[', '❌ 本地删除操作'),
            ('checkins[checkinId] = ', '❌ 本地添加操作')
        ]
        
        for pattern, message in crud_checks:
            if pattern in content:
                if '❌' in message:
                    logger.warning(f"  {message}")
                else:
                    logger.info(f"  {message}")
            else:
                if '✅' in message:
                    logger.warning(f"  ⚠️  未找到: {pattern}")
                else:
                    logger.info(f"  ✅ 已移除: {pattern}")
        
        # 检查Toast系统
        logger.info("\n🍞 Toast系统检查:")
        
        toast_checks = [
            ('.toast {', '⚠️  Toast样式'),
            ('<div id="toast" class="toast"></div>', '⚠️  Toast HTML元素'),
            ('function showToast(', '⚠️  showToast函数'),
            ('function markAsUnsaved(', '⚠️  markAsUnsaved函数'),
            ('function markAsSaved(', '⚠️  markAsSaved函数')
        ]
        
        for pattern, message in toast_checks:
            if pattern in content:
                logger.info(f"  ✅ 已有: {pattern}")
            else:
                logger.warning(f"  {message}")
        
        return not json_file_usage
        
    except Exception as e:
        logger.error(f"❌ 检查Checkin管理前端时发生错误: {e}")
        return False

def check_checkin_backend_apis():
    """检查Checkin后端API"""
    
    logger.info("\n🔧 Checkin后端API检查:")
    
    server_file = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'server.py')
    
    try:
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查数据获取API
        logger.info("\n📥 数据获取API检查:")
        
        get_api_checks = [
            ('@app.route(\'/data/checkins.json\', methods=[\'GET\'])', '❌ 仍有JSON文件路由'),
            ('data_access.get_all(\'checkins\')', '✅ 使用数据访问层'),
            ('send_from_directory(DATA_DIR, \'checkins.json\')', '❌ 仍有文件服务'),
            ('@app.route(\'/api/checkin/<checkin_id>\', methods=[\'GET\'])', '✅ 单个checkin API存在')
        ]
        
        json_api_usage = False
        for pattern, message in get_api_checks:
            if pattern in content:
                if '❌' in message:
                    logger.warning(f"  {message}")
                    json_api_usage = True
                else:
                    logger.info(f"  {message}")
            else:
                if '✅' in message:
                    logger.warning(f"  ⚠️  未找到: {pattern}")
                else:
                    logger.info(f"  ✅ 已移除: {pattern}")
        
        # 检查数据保存API
        logger.info("\n💾 数据保存API检查:")
        
        save_api_checks = [
            ('@app.route(\'/data/checkins.json\', methods=[\'POST\'])', '❌ 仍有JSON文件保存路由'),
            ('checkins_file = os.path.join(DATA_DIR, \'checkins.json\')', '❌ 仍有JSON文件操作'),
            ('json.dump(data, f', '❌ 仍有JSON文件写入'),
            ('@app.route(\'/api/checkin\', methods=[\'POST\'])', '✅ 创建checkin API存在'),
            ('@app.route(\'/api/checkin/<checkin_id>\', methods=[\'PUT\'])', '⚠️  更新checkin API'),
            ('@app.route(\'/api/checkin/<checkin_id>\', methods=[\'DELETE\'])', '⚠️  删除checkin API')
        ]
        
        for pattern, message in save_api_checks:
            if pattern in content:
                if '❌' in message:
                    logger.warning(f"  {message}")
                    json_api_usage = True
                else:
                    logger.info(f"  {message}")
            else:
                if '⚠️' in message:
                    logger.warning(f"  {message}")
                else:
                    logger.info(f"  ✅ 已移除: {pattern}")
        
        # 检查现有checkin API实现
        logger.info("\n🔍 现有checkin API实现检查:")
        
        # 查找checkin相关函数
        checkin_functions = [
            'def get_checkins():',
            'def create_checkin():',
            'def get_checkin(',
            'def update_checkin(',
            'def delete_checkin('
        ]
        
        for func_name in checkin_functions:
            func_start = content.find(func_name)
            if func_start != -1:
                logger.info(f"  ✅ 找到函数: {func_name}")
                
                # 检查函数中是否使用数据访问层
                func_end = content.find('\<EMAIL>', func_start + 1)
                if func_end == -1:
                    func_end = content.find('\ndef ', func_start + 1)
                
                func_content = content[func_start:func_end] if func_end != -1 else content[func_start:]
                
                if 'data_access.' in func_content:
                    logger.info(f"    ✅ 使用数据访问层")
                elif 'checkins_file' in func_content or 'json.load' in func_content:
                    logger.warning(f"    ❌ 仍使用JSON文件操作")
                    json_api_usage = True
                else:
                    logger.warning(f"    ⚠️  无法确定数据访问方式")
            else:
                logger.warning(f"  ⚠️  未找到函数: {func_name}")
        
        return not json_api_usage
        
    except Exception as e:
        logger.error(f"❌ 检查Checkin后端API时发生错误: {e}")
        return False

def analyze_required_changes():
    """分析需要的修改"""
    
    logger.info("\n📊 需要的修改分析:")
    
    required_changes = [
        {
            "组件": "前端 - 数据加载",
            "当前": "fetch('data/checkins.json')",
            "需要": "fetch('/data/checkins.json') 或使用API",
            "优先级": "🔴 高"
        },
        {
            "组件": "前端 - 数据保存",
            "当前": "POST到data/checkins.json",
            "需要": "调用单个checkin API进行CRUD",
            "优先级": "🔴 高"
        },
        {
            "组件": "后端 - 批量数据API",
            "当前": "JSON文件读写",
            "需要": "使用data_access层",
            "优先级": "🔴 高"
        },
        {
            "组件": "后端 - 单个checkin API",
            "当前": "可能使用JSON文件",
            "需要": "使用data_access层",
            "优先级": "🔴 高"
        },
        {
            "组件": "后端 - 缺失的API",
            "当前": "可能只有GET和POST",
            "需要": "添加PUT和DELETE API",
            "优先级": "🟡 中"
        },
        {
            "组件": "前端 - Toast系统",
            "当前": "可能缺失",
            "需要": "添加与其他页面一致的Toast",
            "优先级": "🟡 中"
        },
        {
            "组件": "前端 - CRUD操作",
            "当前": "本地对象操作",
            "需要": "调用对应的API",
            "优先级": "🟡 中"
        }
    ]
    
    for change in required_changes:
        logger.info(f"\n  📂 {change['组件']} {change['优先级']}")
        logger.info(f"    当前: {change['当前']}")
        logger.info(f"    需要: {change['需要']}")

def generate_implementation_plan():
    """生成实施计划"""
    
    logger.info("\n📋 实施计划:")
    
    implementation_steps = [
        {
            "步骤": "1. 检查现有后端API",
            "任务": [
                "检查get_checkins是否使用data_access层",
                "检查create_checkin是否使用data_access层",
                "检查是否有update_checkin和delete_checkin API",
                "修复任何仍使用JSON文件的API"
            ]
        },
        {
            "步骤": "2. 修复前端数据加载",
            "任务": [
                "修改loadData函数使用正确的API路径",
                "处理API响应格式",
                "添加错误处理"
            ]
        },
        {
            "步骤": "3. 修复前端CRUD操作",
            "任务": [
                "修改addNewCheckin调用POST API",
                "修改updateCheckin调用PUT API",
                "修改deleteCheckin调用DELETE API",
                "移除本地对象操作"
            ]
        },
        {
            "步骤": "4. 添加Toast系统",
            "任务": [
                "添加Toast CSS样式",
                "添加Toast HTML元素",
                "添加Toast JavaScript函数",
                "集成到CRUD操作中"
            ]
        },
        {
            "步骤": "5. 修复前端数据保存",
            "任务": [
                "移除saveCheckins函数",
                "实现实时保存机制",
                "添加保存状态指示"
            ]
        },
        {
            "步骤": "6. 测试验证",
            "任务": [
                "测试数据加载",
                "测试CRUD操作",
                "测试数据一致性",
                "测试Toast系统"
            ]
        }
    ]
    
    for step in implementation_steps:
        logger.info(f"\n  📂 {step['步骤']}")
        for task in step['任务']:
            logger.info(f"    • {task}")

def verify_fixes():
    """验证修复结果"""

    logger.info("\n🔍 验证修复结果:")

    # 重新检查前端和后端
    frontend_ok = check_checkin_admin_frontend()
    backend_ok = check_checkin_backend_apis()

    # 检查Toast系统
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'checkin_admin.html')

    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()

        logger.info("\n🍞 Toast系统验证:")

        toast_checks = [
            ('.toast {', '✅ Toast CSS样式'),
            ('<div id="toast" class="toast"></div>', '✅ Toast HTML元素'),
            ('function showToast(', '✅ showToast函数'),
            ('function markAsSaved(', '✅ markAsSaved函数'),
            ('markAsSaved();', '✅ Toast集成到操作中')
        ]

        toast_ok = True
        for pattern, message in toast_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
                toast_ok = False

        # 检查是否移除了主要CRUD操作的alert
        crud_alert_patterns = [
            'alert.*checked in successfully',
            'alert.*saved successfully',
            'alert.*deleted successfully',
            'alert.*created successfully'
        ]

        crud_alerts_found = False
        for pattern in crud_alert_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                logger.warning(f"  ⚠️  仍有CRUD操作使用alert: {pattern}")
                crud_alerts_found = True

        if not crud_alerts_found:
            logger.info("  ✅ 主要CRUD操作已使用Toast替换alert")
            toast_ok = toast_ok and True
        else:
            toast_ok = False

        return frontend_ok and backend_ok and toast_ok

    except Exception as e:
        logger.error(f"❌ 验证Toast系统时发生错误: {e}")
        return False

if __name__ == '__main__':
    # 检查前端
    frontend_ok = check_checkin_admin_frontend()

    # 检查后端
    backend_ok = check_checkin_backend_apis()

    # 分析需要的修改
    analyze_required_changes()

    # 生成实施计划
    generate_implementation_plan()

    # 验证修复结果
    fixes_ok = verify_fixes()

    if fixes_ok:
        logger.info("\n🎊 Checkin管理页面数据库CRUD修复验证成功！")
        logger.info("✅ 所有操作都正确使用数据库")
        logger.info("✅ Toast系统已正确集成")
        logger.info("✅ 前端使用API而不是JSON文件")
        logger.info("✅ 后端使用data_access层")
    else:
        logger.error("\n❌ Checkin管理页面数据库CRUD修复验证失败")
        if not frontend_ok:
            logger.error("⚠️  前端仍有问题")
        if not backend_ok:
            logger.error("⚠️  后端仍有问题")

    sys.exit(0 if fixes_ok else 1)
