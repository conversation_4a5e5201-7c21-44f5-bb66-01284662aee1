#!/usr/bin/env python3
"""
检查Feedback Template管理页CRUD使用情况
验证是否使用数据库而不是JSON文件
"""

import os
import sys
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_frontend_api_usage():
    """检查前端API使用情况"""
    
    logger.info("🔍 检查Feedback Template前端API使用")
    logger.info("=" * 80)
    
    template_admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'feedback_template_admin.html')
    
    try:
        with open(template_admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        logger.info("\n📁 JSON文件直接访问检查:")
        
        # 检查是否仍在使用JSON文件直接访问
        json_access_patterns = [
            (r'fetch\([\'"]data/feedback_templates\.json[\'"]', '❌ 直接访问feedback_templates.json'),
            (r'data/feedback_templates\.json[\'"].*POST', '❌ 直接POST到JSON文件'),
            (r'loadTemplates.*fetch.*json', '❌ 使用JSON文件加载'),
            (r'saveTemplates.*fetch.*json', '❌ 使用JSON文件保存')
        ]
        
        json_issues = []
        for pattern, message in json_access_patterns:
            if re.search(pattern, content):
                logger.warning(f"  {message}")
                json_issues.append(message)
            else:
                logger.info(f"  ✅ 未发现: {message}")
        
        logger.info("\n🗄️  数据库API使用检查:")
        
        # 检查是否使用数据库API
        api_patterns = [
            (r'/api/feedback-templates', '✅ 使用RESTful API'),
            (r'fetch\([\'"][^\'\"]*api/feedback-template', '✅ 使用API端点'),
            (r'POST.*api/feedback-template', '✅ 使用POST API'),
            (r'PUT.*api/feedback-template', '✅ 使用PUT API'),
            (r'DELETE.*api/feedback-template', '✅ 使用DELETE API')
        ]
        
        api_usage = []
        for pattern, message in api_patterns:
            if re.search(pattern, content):
                logger.info(f"  {message}")
                api_usage.append(message)
            else:
                logger.warning(f"  ⚠️  未发现: {message}")
        
        return len(json_issues) == 0, len(api_usage) > 0
        
    except Exception as e:
        logger.error(f"❌ 检查前端API使用时发生错误: {e}")
        return False, False

def check_backend_api_routes():
    """检查后端API路由"""
    
    logger.info("\n🔌 检查后端API路由:")
    
    server_file = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'server.py')
    
    try:
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查新的RESTful API
        new_api_routes = [
            (r"@app\.route\('/api/feedback-templates', methods=\['GET'\]\)", '✅ GET /api/feedback-templates'),
            (r"@app\.route\('/api/feedback-template', methods=\['POST'\]\)", '✅ POST /api/feedback-template'),
            (r"@app\.route\('/api/feedback-template/<[^>]+>', methods=\['GET'\]\)", '✅ GET /api/feedback-template/{id}'),
            (r"@app\.route\('/api/feedback-template/<[^>]+>', methods=\['PUT'\]\)", '✅ PUT /api/feedback-template/{id}'),
            (r"@app\.route\('/api/feedback-template/<[^>]+>', methods=\['DELETE'\]\)", '✅ DELETE /api/feedback-template/{id}')
        ]
        
        new_api_count = 0
        for pattern, message in new_api_routes:
            if re.search(pattern, content):
                logger.info(f"  {message}")
                new_api_count += 1
            else:
                logger.warning(f"  ⚠️  未找到: {message}")
        
        # 检查旧的文件API
        old_api_routes = [
            (r"@app\.route\('/data/feedback_templates\.json', methods=\['GET'\]\)", '⚠️  旧的GET文件API'),
            (r"@app\.route\('/data/feedback_templates\.json', methods=\['POST'\]\)", '⚠️  旧的POST文件API')
        ]
        
        old_api_count = 0
        for pattern, message in old_api_routes:
            if re.search(pattern, content):
                logger.warning(f"  {message}")
                old_api_count += 1
            else:
                logger.info(f"  ✅ 未发现旧API: {message}")
        
        # 检查数据访问层使用
        data_access_patterns = [
            (r'data_access\.get_all\([\'"]feedback_templates[\'"]', '✅ 使用data_access获取'),
            (r'data_access\.create\([\'"]feedback_templates[\'"]', '✅ 使用data_access创建'),
            (r'data_access\.update\([\'"]feedback_templates[\'"]', '✅ 使用data_access更新'),
            (r'data_access\.delete\([\'"]feedback_templates[\'"]', '✅ 使用data_access删除')
        ]
        
        data_access_count = 0
        for pattern, message in data_access_patterns:
            if re.search(pattern, content):
                logger.info(f"  {message}")
                data_access_count += 1
            else:
                logger.warning(f"  ⚠️  未找到: {message}")
        
        return new_api_count, old_api_count, data_access_count
        
    except Exception as e:
        logger.error(f"❌ 检查后端API路由时发生错误: {e}")
        return 0, 0, 0

def check_data_access_layer():
    """检查数据访问层支持"""
    
    logger.info("\n🗃️  检查数据访问层支持:")
    
    data_access_file = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'database_access.py')
    
    try:
        with open(data_access_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查feedback_templates支持
        support_checks = [
            (r"'feedback_templates'.*'feedback_templates\.json'", '✅ feedback_templates实体类型支持'),
            (r'elif entity_type == [\'"]feedback_templates[\'"].*INSERT', '✅ feedback_templates数据库插入'),
            (r'elif entity_type == [\'"]feedback_templates[\'"].*UPDATE', '✅ feedback_templates数据库更新'),
            (r'elif entity_type == [\'"]feedback_templates[\'"].*DELETE', '✅ feedback_templates数据库删除'),
            (r'elif entity_type == [\'"]feedback_templates[\'"].*SELECT', '✅ feedback_templates数据库查询')
        ]
        
        support_count = 0
        for pattern, message in support_checks:
            if re.search(pattern, content, re.DOTALL):
                logger.info(f"  {message}")
                support_count += 1
            else:
                logger.warning(f"  ⚠️  未找到: {message}")
        
        return support_count >= 3  # 至少需要基本的CRUD支持
        
    except Exception as e:
        logger.error(f"❌ 检查数据访问层支持时发生错误: {e}")
        return False

def check_database_schema():
    """检查数据库Schema"""
    
    logger.info("\n🏗️  检查数据库Schema:")
    
    schema_file = os.path.join(os.path.dirname(__file__), '..', 'sql', 'create_tables.sql')
    
    try:
        with open(schema_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查feedback_templates表定义
        schema_checks = [
            (r'CREATE TABLE.*feedback_templates', '✅ feedback_templates表定义'),
            (r'feedback_templates.*id.*PRIMARY KEY', '✅ 主键定义'),
            (r'feedback_templates.*name.*JSONB', '✅ name JSONB字段'),
            (r'feedback_templates.*description.*JSONB', '✅ description JSONB字段'),
            (r'feedback_templates.*sections.*JSONB', '✅ sections JSONB字段'),
            (r'feedback_templates.*created_at.*TIMESTAMP', '✅ 创建时间字段'),
            (r'feedback_templates.*updated_at.*TIMESTAMP', '✅ 更新时间字段')
        ]
        
        schema_count = 0
        for pattern, message in schema_checks:
            if re.search(pattern, content, re.DOTALL | re.IGNORECASE):
                logger.info(f"  {message}")
                schema_count += 1
            else:
                logger.warning(f"  ⚠️  未找到: {message}")
        
        return schema_count >= 5  # 至少需要基本字段
        
    except Exception as e:
        logger.error(f"❌ 检查数据库Schema时发生错误: {e}")
        return False

def generate_migration_plan():
    """生成迁移计划"""
    
    logger.info("\n📋 Feedback Template CRUD迁移计划:")
    
    migration_steps = [
        {
            "优先级": "高",
            "任务": "修改前端API调用",
            "描述": "将loadTemplates()改为使用/api/feedback-templates",
            "文件": "feedback_template_admin.html"
        },
        {
            "优先级": "高", 
            "任务": "修改前端保存逻辑",
            "描述": "将saveTemplates()改为使用RESTful API",
            "文件": "feedback_template_admin.html"
        },
        {
            "优先级": "高",
            "任务": "添加后端CRUD API",
            "描述": "实现完整的feedback template CRUD API",
            "文件": "backend/server.py"
        },
        {
            "优先级": "中",
            "任务": "移除旧的文件API",
            "描述": "删除/data/feedback_templates.json路由",
            "文件": "backend/server.py"
        },
        {
            "优先级": "低",
            "任务": "添加数据验证",
            "描述": "添加template数据格式验证",
            "文件": "backend/server.py"
        }
    ]
    
    for step in migration_steps:
        logger.info(f"\n  🔧 {step['任务']} ({step['优先级']}优先级)")
        logger.info(f"    描述: {step['描述']}")
        logger.info(f"    文件: {step['文件']}")

if __name__ == '__main__':
    # 检查前端API使用
    no_json_issues, has_api_usage = check_frontend_api_usage()
    
    # 检查后端API路由
    new_api_count, old_api_count, data_access_count = check_backend_api_routes()
    
    # 检查数据访问层支持
    data_access_ok = check_data_access_layer()
    
    # 检查数据库Schema
    schema_ok = check_database_schema()
    
    # 生成迁移计划
    generate_migration_plan()
    
    # 总结
    if no_json_issues and has_api_usage and new_api_count >= 3 and data_access_ok and schema_ok:
        logger.info("\n🎊 Feedback Template管理页完全使用数据库CRUD！")
        logger.info("✅ 前端使用RESTful API")
        logger.info("✅ 后端使用数据访问层")
        logger.info("✅ 数据库schema完整")
    else:
        logger.error("\n❌ Feedback Template管理页未完全使用数据库CRUD")
        if not no_json_issues:
            logger.error("⚠️  前端仍使用JSON文件直接访问")
        if not has_api_usage:
            logger.error("⚠️  前端未使用RESTful API")
        if new_api_count < 3:
            logger.error("⚠️  后端缺少完整的CRUD API")
        if old_api_count > 0:
            logger.warning("⚠️  后端仍有旧的文件API")
        if not data_access_ok:
            logger.error("⚠️  数据访问层支持不完整")
        if not schema_ok:
            logger.error("⚠️  数据库schema不完整")
    
    sys.exit(0 if (no_json_issues and has_api_usage and new_api_count >= 3 and data_access_ok and schema_ok) else 1)
