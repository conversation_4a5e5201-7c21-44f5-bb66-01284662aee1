#!/usr/bin/env python3
"""
验证Activity管理页面Toast提示系统
确保与Member管理页面完全一致的用户体验
"""

import os
import sys
import json
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_toast_styles():
    """检查Toast样式"""
    
    logger.info("🎨 检查Toast样式")
    logger.info("=" * 80)
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'activity_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查Toast CSS样式
        logger.info("\n🎨 Toast CSS样式检查:")
        
        toast_styles = [
            ('.toast {', '✅ Toast基础样式'),
            ('position: fixed;', '✅ 固定定位'),
            ('top: 20px;', '✅ 顶部位置'),
            ('right: 20px;', '✅ 右侧位置'),
            ('transform: translateX(100%);', '✅ 初始隐藏'),
            ('transition: transform 0.3s ease;', '✅ 动画过渡'),
            ('.toast.show {', '✅ 显示状态'),
            ('transform: translateX(0);', '✅ 显示动画'),
            ('.toast.unsaved {', '✅ 未保存样式'),
            ('background: #FFF3CD;', '✅ 未保存背景色'),
            ('border-left: 4px solid #FFC107;', '✅ 未保存边框'),
            ('.toast.saved {', '✅ 已保存样式'),
            ('background: #D4EDDA;', '✅ 已保存背景色'),
            ('border-left: 4px solid #28A745;', '✅ 已保存边框')
        ]
        
        for pattern, message in toast_styles:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查Toast样式时发生错误: {e}")
        return False

def check_toast_html():
    """检查Toast HTML元素"""
    
    logger.info("\n📄 Toast HTML元素检查:")
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'activity_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        html_elements = [
            ('<div id="toast" class="toast"></div>', '✅ Toast HTML元素存在')
        ]
        
        for pattern, message in html_elements:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查Toast HTML时发生错误: {e}")
        return False

def check_toast_javascript():
    """检查Toast JavaScript功能"""
    
    logger.info("\n🔧 Toast JavaScript功能检查:")
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'activity_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查Toast相关变量和函数
        js_functions = [
            ('let hasUnsavedChanges = false;', '✅ 未保存状态变量'),
            ('function showToast(message, type = \'unsaved\', duration = 0)', '✅ showToast函数'),
            ('function hideToast()', '✅ hideToast函数'),
            ('function markAsUnsaved()', '✅ markAsUnsaved函数'),
            ('function markAsSaved()', '✅ markAsSaved函数'),
            ('showToast(\'📝 You have unsaved changes\', \'unsaved\');', '✅ 未保存提示'),
            ('showToast(\'✅ All changes saved successfully\', \'saved\', 5000);', '✅ 已保存提示')
        ]
        
        for pattern, message in js_functions:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        # 检查事件监听器
        logger.info("\n📡 事件监听器检查:")
        
        event_listeners = [
            ('field.addEventListener(\'input\', markAsUnsaved);', '✅ input事件监听'),
            ('field.addEventListener(\'change\', markAsUnsaved);', '✅ change事件监听'),
            ('feedbackTemplateSelect.addEventListener(\'change\', markAsUnsaved);', '✅ 反馈模板监听')
        ]
        
        for pattern, message in event_listeners:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查Toast JavaScript时发生错误: {e}")
        return False

def check_button_text_change():
    """检查按钮文本修改"""
    
    logger.info("\n🔘 按钮文本修改检查:")
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'activity_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        button_checks = [
            ('onclick="updateActivity()">Save Activity</button>', '✅ 按钮文本改为Save Activity'),
            ('Update Activity', '⚠️  仍有Update Activity文本')
        ]
        
        for pattern, message in button_checks:
            if pattern in content:
                if '✅' in message:
                    logger.info(f"  {message}")
                else:
                    logger.warning(f"  {message}")
            else:
                if '⚠️' in message:
                    logger.info(f"  ✅ 已移除: Update Activity文本")
                else:
                    logger.warning(f"  ⚠️  未找到: {pattern}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查按钮文本时发生错误: {e}")
        return False

def check_crud_operations_toast():
    """检查CRUD操作的Toast集成"""
    
    logger.info("\n🔧 CRUD操作Toast集成检查:")
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'activity_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查各个CRUD操作是否调用了markAsSaved
        crud_toast_checks = [
            ('updateActivity', 'markAsSaved();', '✅ 更新活动显示保存Toast'),
            ('addNewActivity', 'markAsSaved();', '✅ 创建活动显示保存Toast'),
            ('deleteActivity', 'markAsSaved();', '✅ 删除活动显示保存Toast'),
            ('performActivityDuplication', 'markAsSaved();', '✅ 复制活动显示保存Toast')
        ]
        
        for func_name, toast_call, message in crud_toast_checks:
            # 查找函数
            func_start = content.find(f'function {func_name}(')
            if func_start == -1:
                func_start = content.find(f'async function {func_name}(')
            
            if func_start != -1:
                func_end = content.find('\n    }', func_start)
                if func_end == -1:
                    func_end = content.find('\n  }', func_start)
                
                func_content = content[func_start:func_end] if func_end != -1 else content[func_start:]
                
                if toast_call in func_content:
                    logger.info(f"  {message}")
                else:
                    logger.warning(f"  ⚠️  {func_name}中未找到{toast_call}")
            else:
                logger.warning(f"  ⚠️  未找到函数: {func_name}")
        
        # 检查是否移除了alert调用
        logger.info("\n🔕 Alert移除检查:")
        
        alert_checks = [
            ('alert(\'Activity updated successfully!\')', '⚠️  仍有更新成功alert'),
            ('alert(`Activity created successfully: ${activityId}`)', '⚠️  仍有创建成功alert'),
            ('alert(\'Activity deleted successfully!\')', '⚠️  仍有删除成功alert'),
            ('alert(`Activity duplicated successfully!', '⚠️  仍有复制成功alert')
        ]
        
        alerts_found = False
        for pattern, message in alert_checks:
            if pattern in content:
                logger.warning(f"  {message}")
                alerts_found = True
        
        if not alerts_found:
            logger.info("  ✅ 所有成功操作的alert已替换为Toast")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查CRUD操作Toast时发生错误: {e}")
        return False

def compare_with_member_admin():
    """与Member管理页面对比"""
    
    logger.info("\n🔄 与Member管理页面对比:")
    
    try:
        # 读取两个文件
        activity_file = os.path.join(os.path.dirname(__file__), '..', '..', 'activity_admin.html')
        member_file = os.path.join(os.path.dirname(__file__), '..', '..', 'member_admin.html')
        
        with open(activity_file, 'r', encoding='utf-8') as f:
            activity_content = f.read()
        
        with open(member_file, 'r', encoding='utf-8') as f:
            member_content = f.read()
        
        # 对比关键功能
        comparison_items = [
            ('Toast CSS样式', '.toast {', '.toast {'),
            ('Toast HTML元素', '<div id="toast" class="toast"></div>', '<div id="toast" class="toast"></div>'),
            ('showToast函数', 'function showToast(', 'function showToast('),
            ('hideToast函数', 'function hideToast()', 'function hideToast()'),
            ('markAsUnsaved函数', 'function markAsUnsaved()', 'function markAsUnsaved()'),
            ('markAsSaved函数', 'function markAsSaved()', 'function markAsSaved()'),
            ('未保存状态变量', 'let hasUnsavedChanges = false;', 'let hasUnsavedChanges = false;')
        ]
        
        for item_name, activity_pattern, member_pattern in comparison_items:
            activity_has = activity_pattern in activity_content
            member_has = member_pattern in member_content
            
            if activity_has and member_has:
                logger.info(f"  ✅ {item_name}: 两个页面都有")
            elif member_has and not activity_has:
                logger.warning(f"  ⚠️  {item_name}: Member有，Activity缺失")
            elif activity_has and not member_has:
                logger.info(f"  ℹ️  {item_name}: Activity有，Member没有")
            else:
                logger.warning(f"  ❌ {item_name}: 两个页面都没有")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 对比时发生错误: {e}")
        return False

def generate_usage_guide():
    """生成使用指南"""
    
    logger.info("\n📖 Activity管理页面Toast系统使用指南:")
    
    usage_scenarios = [
        {
            "场景": "用户修改活动信息",
            "触发": "在任何表单字段输入或选择",
            "显示": "📝 You have unsaved changes (黄色Toast)",
            "持续": "直到用户保存或切换活动"
        },
        {
            "场景": "用户保存活动",
            "触发": "点击Save Activity按钮成功",
            "显示": "✅ All changes saved successfully (绿色Toast)",
            "持续": "5秒后自动消失"
        },
        {
            "场景": "用户创建新活动",
            "触发": "Add New Activity成功",
            "显示": "✅ All changes saved successfully (绿色Toast)",
            "持续": "5秒后自动消失"
        },
        {
            "场景": "用户删除活动",
            "触发": "Delete Activity成功",
            "显示": "✅ All changes saved successfully (绿色Toast)",
            "持续": "5秒后自动消失"
        },
        {
            "场景": "用户复制活动",
            "触发": "Duplicate Activity成功",
            "显示": "✅ All changes saved successfully (绿色Toast)",
            "持续": "5秒后自动消失"
        },
        {
            "场景": "用户切换活动",
            "触发": "选择不同的活动",
            "显示": "Toast隐藏",
            "持续": "立即清除未保存状态"
        }
    ]
    
    for scenario in usage_scenarios:
        logger.info(f"\n  📋 {scenario['场景']}:")
        logger.info(f"    触发条件: {scenario['触发']}")
        logger.info(f"    显示内容: {scenario['显示']}")
        logger.info(f"    持续时间: {scenario['持续']}")

if __name__ == '__main__':
    # 检查Toast样式
    styles_ok = check_toast_styles()
    
    # 检查Toast HTML
    html_ok = check_toast_html()
    
    # 检查Toast JavaScript
    js_ok = check_toast_javascript()
    
    # 检查按钮文本
    button_ok = check_button_text_change()
    
    # 检查CRUD操作Toast集成
    crud_ok = check_crud_operations_toast()
    
    # 与Member管理页面对比
    compare_ok = compare_with_member_admin()
    
    # 生成使用指南
    generate_usage_guide()
    
    all_ok = styles_ok and html_ok and js_ok and button_ok and crud_ok and compare_ok
    
    if all_ok:
        logger.info("\n🎊 Activity管理页面Toast系统验证成功！")
        logger.info("✅ Toast样式完全一致")
        logger.info("✅ Toast功能完全实现")
        logger.info("✅ 按钮文本已更新")
        logger.info("✅ CRUD操作集成Toast")
        logger.info("✅ 与Member管理页面保持一致")
    else:
        logger.error("\n❌ Activity管理页面Toast系统验证失败")
        if not styles_ok:
            logger.error("⚠️  Toast样式有问题")
        if not html_ok:
            logger.error("⚠️  Toast HTML有问题")
        if not js_ok:
            logger.error("⚠️  Toast JavaScript有问题")
        if not button_ok:
            logger.error("⚠️  按钮文本有问题")
        if not crud_ok:
            logger.error("⚠️  CRUD操作Toast集成有问题")
    
    sys.exit(0 if all_ok else 1)
