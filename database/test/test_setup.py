#!/usr/bin/env python3
"""
Test the database setup and migration process
"""

import os
import sys
import json

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_json_data_loading():
    """Test loading JSON data files"""
    print("🧪 Testing JSON data loading...")
    
    data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
    json_files = [
        'members.json',
        'activities.json', 
        'checkins.json',
        'feedbacks.json',
        'feedback_templates.json',
        'email_templates.json',
        'navigation.json',
        'courses.json',
        'student_ratings.json'
    ]
    
    for json_file in json_files:
        file_path = os.path.join(data_dir, json_file)
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"✅ {json_file}: {len(data)} records")
            except Exception as e:
                print(f"❌ {json_file}: Error loading - {e}")
        else:
            print(f"⚠️  {json_file}: File not found")
    
    print()

def test_data_access_layer():
    """Test the data access layer"""
    print("🧪 Testing Data Access Layer...")
    
    try:
        from backend.database_access import DataAccessLayer
        
        # Test JSON mode
        dal_json = DataAccessLayer(mode='json')
        print(f"✅ JSON mode initialized")
        
        # Test loading members
        members = dal_json.get_all('members')
        print(f"✅ Loaded {len(members)} members from JSON")
        
        # Test loading activities
        activities = dal_json.get_all('activities')
        print(f"✅ Loaded {len(activities)} activities from JSON")
        
        # Test database mode (will fail without database, but should handle gracefully)
        try:
            dal_db = DataAccessLayer(mode='database')
            print(f"✅ Database mode initialized")
        except Exception as e:
            print(f"⚠️  Database mode failed (expected without running database): {e}")
        
        # Test hybrid mode
        try:
            dal_hybrid = DataAccessLayer(mode='hybrid')
            print(f"✅ Hybrid mode initialized")
            
            # Should fall back to JSON
            members_hybrid = dal_hybrid.get_all('members')
            print(f"✅ Hybrid mode loaded {len(members_hybrid)} members (fallback to JSON)")
            
        except Exception as e:
            print(f"❌ Hybrid mode failed: {e}")
        
    except ImportError as e:
        print(f"❌ Failed to import DataAccessLayer: {e}")
    except Exception as e:
        print(f"❌ Data access layer test failed: {e}")
    
    print()

def test_migration_script():
    """Test the migration script imports"""
    print("🧪 Testing Migration Script...")
    
    try:
        from database.migrate import DatabaseMigrator
        print("✅ Migration script imports successfully")
        
        # Test migrator initialization
        migrator = DatabaseMigrator()
        print("✅ DatabaseMigrator initialized")
        
    except ImportError as e:
        print(f"❌ Failed to import migration script: {e}")
    except Exception as e:
        print(f"❌ Migration script test failed: {e}")
    
    print()

def test_docker_files():
    """Test Docker configuration files"""
    print("🧪 Testing Docker Configuration...")
    
    # Check docker-compose.yml
    if os.path.exists('docker-compose.yml'):
        print("✅ docker-compose.yml exists")
    else:
        print("❌ docker-compose.yml not found")
    
    # Check Dockerfile
    if os.path.exists('database/Dockerfile'):
        print("✅ database/Dockerfile exists")
    else:
        print("❌ database/Dockerfile not found")
    
    # Check schema file
    if os.path.exists('database/schema.sql'):
        print("✅ database/schema.sql exists")
    else:
        print("❌ database/schema.sql not found")
    
    # Check init directory
    if os.path.exists('database/init'):
        print("✅ database/init directory exists")
        if os.path.exists('database/init/01-schema.sql'):
            print("✅ database/init/01-schema.sql exists")
        else:
            print("❌ database/init/01-schema.sql not found")
    else:
        print("❌ database/init directory not found")
    
    print()

def main():
    """Run all tests"""
    print("🚀 SnowNavi Database Migration Setup Test")
    print("=" * 50)
    
    test_json_data_loading()
    test_data_access_layer()
    test_migration_script()
    test_docker_files()
    
    print("✨ Setup test completed!")
    print("\nNext steps:")
    print("1. Start database: docker compose up -d db")
    print("2. Test connection: python database/test_connection.py")
    print("3. Run migration: docker compose run --rm migrate")

if __name__ == '__main__':
    main()
