#!/usr/bin/env python3
"""
对比Feedbacks数据在database模式和json模式下的返回值
确保两种模式返回的数据完全一致
"""

import os
import sys
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def compare_feedbacks_output():
    """对比两种模式下的feedbacks数据输出"""
    
    # 设置环境
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
    os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
    
    try:
        from database_access import DataAccessLayer
        
        logger.info("🔍 开始对比Feedbacks数据输出")
        logger.info("=" * 80)
        
        # 1. 测试JSON模式
        logger.info("📄 测试JSON模式...")
        json_dal = DataAccessLayer(mode='json')
        json_data = json_dal.get_all('feedbacks')
        logger.info(f"JSON模式: 获取到 {len(json_data)} 条记录")
        
        # 2. 测试Database模式
        logger.info("🗄️  测试Database模式...")
        db_dal = DataAccessLayer(mode='database')
        db_data = db_dal.get_all('feedbacks')
        logger.info(f"Database模式: 获取到 {len(db_data)} 条记录")
        
        # 3. 对比数据数量
        if len(json_data) != len(db_data):
            logger.error(f"❌ 数据数量不一致: JSON={len(json_data)}, Database={len(db_data)}")
            return False
        
        logger.info(f"✅ 数据数量一致: {len(json_data)} 条记录")
        
        # 4. 对比具体数据
        logger.info("\n📊 详细数据对比:")
        
        mismatches = []
        matches = 0
        
        for feedback_key in json_data.keys():
            if feedback_key not in db_data:
                mismatches.append(f"Database中缺少反馈: {feedback_key}")
                continue
            
            json_feedback = json_data[feedback_key]
            db_feedback = db_data[feedback_key]
            
            # 对比每个字段
            field_mismatches = []
            
            # 检查所有JSON中的字段
            for field in json_feedback.keys():
                json_value = json_feedback.get(field)
                db_value = db_feedback.get(field)
                
                # 特殊处理复杂字段
                if field in ['skillAssessment', 'studentRating']:
                    if json_value != db_value:
                        field_mismatches.append(f"  {field}: 复杂对象不匹配")
                        # 详细对比复杂对象
                        if isinstance(json_value, dict) and isinstance(db_value, dict):
                            for sub_field in set(json_value.keys()) | set(db_value.keys()):
                                json_sub = json_value.get(sub_field, "缺失")
                                db_sub = db_value.get(sub_field, "缺失")
                                if json_sub != db_sub:
                                    field_mismatches.append(f"    {sub_field}: JSON='{json_sub}' vs DB='{db_sub}'")
                elif json_value != db_value:
                    field_mismatches.append(f"  {field}: JSON='{json_value}' vs DB='{db_value}'")
            
            # 检查Database中是否有额外字段
            for field in db_feedback.keys():
                if field not in json_feedback:
                    field_mismatches.append(f"  {field}: 仅在Database中存在='{db_feedback[field]}'")
            
            if field_mismatches:
                mismatches.append(f"反馈 {feedback_key} 字段不匹配:")
                mismatches.extend(field_mismatches)
            else:
                matches += 1
                logger.info(f"✅ {feedback_key}: 完全匹配")
        
        # 检查Database中是否有JSON中没有的反馈
        for feedback_key in db_data.keys():
            if feedback_key not in json_data:
                mismatches.append(f"JSON中缺少反馈: {feedback_key}")
        
        # 5. 输出结果
        logger.info(f"\n📈 对比结果:")
        logger.info(f"  完全匹配: {matches}/{len(json_data)}")
        logger.info(f"  不匹配: {len(mismatches)}")
        
        if mismatches:
            logger.error("\n❌ 发现不匹配项:")
            for mismatch in mismatches[:15]:  # 显示前15个
                logger.error(f"  {mismatch}")
            if len(mismatches) > 15:
                logger.error(f"  ... 还有 {len(mismatches) - 15} 个不匹配项")
            return False
        else:
            logger.info("\n🎉 所有数据完全匹配！")
            
            # 6. 显示数据结构示例
            logger.info("\n📋 数据结构示例:")
            sample_key = list(json_data.keys())[0]
            logger.info(f"JSON格式 ({sample_key}):")
            for key, value in json_data[sample_key].items():
                if isinstance(value, dict):
                    logger.info(f"  {key}: {{复杂对象}}")
                else:
                    logger.info(f"  {key}: {value}")
            
            logger.info(f"\nDatabase格式 ({sample_key}):")
            for key, value in db_data[sample_key].items():
                if isinstance(value, dict):
                    logger.info(f"  {key}: {{复杂对象}}")
                else:
                    logger.info(f"  {key}: {value}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 对比过程中发生错误: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def test_specific_feedback():
    """测试特定反馈的数据"""
    
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
    os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
    
    try:
        from database_access import DataAccessLayer
        
        logger.info("\n🔍 测试特定反馈数据...")
        
        # 选择一个测试反馈
        test_feedback_key = "CHK20250001"
        
        # JSON模式
        json_dal = DataAccessLayer(mode='json')
        json_data = json_dal.get_all('feedbacks')
        json_feedback = json_data.get(test_feedback_key)
        
        # Database模式
        db_dal = DataAccessLayer(mode='database')
        db_data = db_dal.get_all('feedbacks')
        db_feedback = db_data.get(test_feedback_key)
        
        logger.info(f"\n反馈 {test_feedback_key} 详细对比:")
        logger.info("JSON数据:")
        logger.info(json.dumps(json_feedback, indent=2, ensure_ascii=False))
        
        logger.info("\nDatabase数据:")
        logger.info(json.dumps(db_feedback, indent=2, ensure_ascii=False))
        
        # 字段级对比
        logger.info("\n字段级对比:")
        all_fields = set(json_feedback.keys()) | set(db_feedback.keys())
        for field in sorted(all_fields):
            json_val = json_feedback.get(field, "❌ 缺失")
            db_val = db_feedback.get(field, "❌ 缺失")
            
            # 对于复杂对象，进行深度比较
            if isinstance(json_val, dict) and isinstance(db_val, dict):
                if json_val == db_val:
                    status = "✅"
                else:
                    status = "❌"
                logger.info(f"  {status} {field}: 复杂对象 - {'匹配' if status == '✅' else '不匹配'}")
            else:
                status = "✅" if json_val == db_val else "❌"
                logger.info(f"  {status} {field}: JSON='{json_val}' | DB='{db_val}'")
        
    except Exception as e:
        logger.error(f"❌ 测试特定反馈时发生错误: {e}")

def check_data_source():
    """检查当前数据来源"""
    
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
    os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
    os.environ['DATA_MODE'] = "database"
    
    try:
        from database_access import DataAccessLayer
        
        logger.info("\n🔍 检查数据来源...")
        
        # 创建database模式的实例
        dal = DataAccessLayer(mode='database')
        logger.info(f"数据访问层模式: {dal.mode}")
        
        # 检查是否真的从数据库获取数据
        feedbacks = dal.get_all('feedbacks')
        logger.info(f"获取到 {len(feedbacks)} 个反馈")
        
        # 检查数据库连接
        if dal.engine:
            logger.info("✅ 数据库连接正常")
        else:
            logger.error("❌ 数据库连接失败")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查数据源时发生错误: {e}")
        return False

def check_server_integration():
    """检查server.py中的集成"""
    
    logger.info("\n🔍 检查server.py集成...")
    
    server_file = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'server.py')
    
    try:
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查feedbacks的GET路由
        if 'data_access.get_all(\'feedbacks\')' in content:
            logger.info("✅ server.py中feedbacks GET路由使用了data_access")
        else:
            logger.warning("⚠️  server.py中feedbacks GET路由可能未使用data_access")
        
        # 检查是否还有直接文件操作
        if 'feedbacks.json' in content and 'send_from_directory' in content:
            logger.info("✅ server.py中有JSON文件的fallback机制")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查server.py集成时发生错误: {e}")
        return False

if __name__ == '__main__':
    # 检查数据源
    check_data_source()
    
    # 检查server.py集成
    check_server_integration()
    
    # 运行对比测试
    success = compare_feedbacks_output()
    
    # 测试特定反馈
    test_specific_feedback()
    
    sys.exit(0 if success else 1)
