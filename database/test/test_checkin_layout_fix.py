#!/usr/bin/env python3
"""
验证Checkin记录布局修复
确保按钮移到信息下方，信息不会变形
"""

import os
import sys
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_layout_structure():
    """检查布局结构修改"""
    
    logger.info("📐 检查Checkin记录布局结构")
    logger.info("=" * 80)
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'checkin_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查checkin-item布局改为垂直
        logger.info("\n📦 Checkin项目布局检查:")
        
        layout_checks = [
            ('flex-direction: column;', '✅ 改为垂直布局'),
            ('align-items: flex-start;', '✅ 左对齐'),
            ('gap: 0.75rem;', '✅ 设置间距')
        ]

        layout_ok = True
        for pattern, message in layout_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
                layout_ok = False

        # 检查checkin-item是否还有justify-content: space-between
        checkin_item_section = re.search(r'\.checkin-item \{[^}]*\}', content, re.DOTALL)
        if checkin_item_section:
            if 'justify-content: space-between' in checkin_item_section.group():
                logger.warning("  ❌ checkin-item仍有水平分布样式")
                layout_ok = False
            else:
                logger.info("  ✅ 已移除checkin-item的水平分布样式")
        
        return layout_ok
        
    except Exception as e:
        logger.error(f"❌ 检查布局结构时发生错误: {e}")
        return False

def check_info_area_styles():
    """检查信息区域样式"""
    
    logger.info("\n📝 信息区域样式检查:")
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'checkin_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查checkin-info样式
        info_checks = [
            ('.checkin-info {', '✅ 信息区域样式'),
            ('width: 100%;', '✅ 信息区域全宽'),
            ('flex: 1;', '✅ 信息区域弹性布局'),
            ('line-height: 1.3;', '✅ 标题行高'),
            ('line-height: 1.4;', '✅ 段落行高')
        ]
        
        info_ok = True
        for pattern, message in info_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
                info_ok = False
        
        return info_ok
        
    except Exception as e:
        logger.error(f"❌ 检查信息区域样式时发生错误: {e}")
        return False

def check_actions_area_styles():
    """检查按钮区域样式"""
    
    logger.info("\n🎯 按钮区域样式检查:")
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'checkin_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查checkin-actions样式
        actions_checks = [
            ('width: 100%;', '✅ 按钮区域全宽'),
            ('flex-wrap: wrap;', '✅ 按钮可换行'),
            ('margin-top: 0.25rem;', '✅ 按钮区域上边距'),
            ('gap: 0.5rem;', '✅ 按钮间距')
        ]
        
        actions_ok = True
        for pattern, message in actions_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
                actions_ok = False
        
        return actions_ok
        
    except Exception as e:
        logger.error(f"❌ 检查按钮区域样式时发生错误: {e}")
        return False

def check_responsive_layout():
    """检查响应式布局"""
    
    logger.info("\n📱 响应式布局检查:")
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'checkin_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查移动端布局
        responsive_checks = [
            ('justify-content: flex-start;', '✅ 移动端按钮左对齐'),
            ('opacity: 1; /* Always visible on mobile */', '✅ 移动端按钮始终可见'),
            ('margin-top: 0.5rem;', '✅ 移动端按钮上边距')
        ]
        
        responsive_ok = True
        for pattern, message in responsive_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
                responsive_ok = False
        
        return responsive_ok
        
    except Exception as e:
        logger.error(f"❌ 检查响应式布局时发生错误: {e}")
        return False

def check_html_structure():
    """检查HTML结构"""
    
    logger.info("\n🏗️  HTML结构检查:")
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'checkin_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查HTML结构是否正确
        html_checks = [
            ('<div class="checkin-info">', '✅ 信息区域容器'),
            ('<div class="checkin-actions">', '✅ 按钮区域容器'),
            ('checkinItem.innerHTML = `', '✅ 动态HTML生成')
        ]
        
        html_ok = True
        for pattern, message in html_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
                html_ok = False
        
        # 检查HTML结构顺序
        info_pos = content.find('<div class="checkin-info">')
        actions_pos = content.find('<div class="checkin-actions">')
        
        if info_pos > 0 and actions_pos > info_pos:
            logger.info("  ✅ HTML结构顺序正确 (信息在前，按钮在后)")
        else:
            logger.warning("  ⚠️  HTML结构顺序可能有问题")
            html_ok = False
        
        return html_ok
        
    except Exception as e:
        logger.error(f"❌ 检查HTML结构时发生错误: {e}")
        return False

def generate_layout_summary():
    """生成布局修改总结"""
    
    logger.info("\n📊 布局修改总结:")
    
    improvements = [
        {
            "改进": "垂直布局结构",
            "修改前": "水平布局，按钮在右侧挤压信息",
            "修改后": "垂直布局，信息在上，按钮在下",
            "效果": "信息区域有足够空间，不会变形"
        },
        {
            "改进": "信息区域优化",
            "修改前": "信息区域被按钮挤压",
            "修改后": "信息区域占满宽度，弹性布局",
            "效果": "文字显示完整，行高适中"
        },
        {
            "改进": "按钮区域重新设计",
            "修改前": "按钮在右侧，空间受限",
            "修改后": "按钮在下方，全宽显示，可换行",
            "效果": "按钮有足够空间，布局整齐"
        },
        {
            "改进": "响应式体验统一",
            "修改前": "桌面端和移动端布局差异大",
            "修改后": "桌面端和移动端都使用垂直布局",
            "效果": "各设备体验一致"
        }
    ]
    
    for improvement in improvements:
        logger.info(f"\n  📈 {improvement['改进']}")
        logger.info(f"    修改前: {improvement['修改前']}")
        logger.info(f"    修改后: {improvement['修改后']}")
        logger.info(f"    效果: {improvement['效果']}")

if __name__ == '__main__':
    # 检查布局结构
    layout_ok = check_layout_structure()
    
    # 检查信息区域样式
    info_ok = check_info_area_styles()
    
    # 检查按钮区域样式
    actions_ok = check_actions_area_styles()
    
    # 检查响应式布局
    responsive_ok = check_responsive_layout()
    
    # 检查HTML结构
    html_ok = check_html_structure()
    
    # 生成布局总结
    generate_layout_summary()
    
    if layout_ok and info_ok and actions_ok and responsive_ok and html_ok:
        logger.info("\n🎊 Checkin记录布局修复验证成功！")
        logger.info("✅ 按钮成功移到信息下方")
        logger.info("✅ 信息区域有足够空间")
        logger.info("✅ 布局不会变形")
        logger.info("✅ 响应式设计一致")
        logger.info("✅ HTML结构正确")
    else:
        logger.error("\n❌ Checkin记录布局修复验证失败")
        if not layout_ok:
            logger.error("⚠️  布局结构有问题")
        if not info_ok:
            logger.error("⚠️  信息区域样式有问题")
        if not actions_ok:
            logger.error("⚠️  按钮区域样式有问题")
        if not responsive_ok:
            logger.error("⚠️  响应式布局有问题")
        if not html_ok:
            logger.error("⚠️  HTML结构有问题")
    
    sys.exit(0 if (layout_ok and info_ok and actions_ok and responsive_ok and html_ok) else 1)
