#!/usr/bin/env python3
"""
Test database connection and basic operations
"""

import os
import sys
import psycopg2
from psycopg2.extras import RealDictCursor
import time

def test_database_connection():
    """Test connection to PostgreSQL database"""
    database_url = os.getenv('DATABASE_URL', 
        'postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi')
    
    print(f"Testing connection to: {database_url}")
    
    max_retries = 10
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            conn = psycopg2.connect(database_url, cursor_factory=RealDictCursor)
            cursor = conn.cursor()
            
            # Test basic query
            cursor.execute("SELECT version();")
            version = cursor.fetchone()
            print(f"✅ Database connection successful!")
            print(f"PostgreSQL version: {version['version']}")
            
            # Test table existence
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name;
            """)
            tables = cursor.fetchall()
            
            if tables:
                print(f"✅ Found {len(tables)} tables:")
                for table in tables:
                    print(f"  - {table['table_name']}")
            else:
                print("⚠️  No tables found in database")
            
            conn.close()
            return True
            
        except psycopg2.OperationalError as e:
            retry_count += 1
            print(f"❌ Connection attempt {retry_count}/{max_retries} failed: {e}")
            if retry_count < max_retries:
                print(f"Retrying in 2 seconds...")
                time.sleep(2)
            else:
                print("❌ Failed to connect to database after all retries")
                return False
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return False
    
    return False

if __name__ == '__main__':
    success = test_database_connection()
    sys.exit(0 if success else 1)
