#!/usr/bin/env python3
"""
测试Member Admin UI改进
验证按钮样式、顺序和toast通知功能
"""

import os
import sys
import json
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_button_layout_and_styles():
    """检查按钮布局和样式"""
    
    logger.info("🔍 检查Member Admin UI改进")
    logger.info("=" * 80)
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'member_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查按钮顺序
        logger.info("\n📋 按钮顺序检查:")
        
        # 提取actions div中的按钮
        actions_match = re.search(r'<div class="actions">(.*?)</div>', content, re.DOTALL)
        if actions_match:
            actions_content = actions_match.group(1)
            
            # 检查按钮顺序
            button_order = [
                ('Add New Member', 'add-btn'),
                ('Generate All Passwords', 'password-btn'),
                ('Save All Changes', 'save-btn'),
                ('Discard Changes', 'discard-btn')
            ]
            
            current_pos = 0
            for button_text, button_class in button_order:
                button_pos = actions_content.find(button_text, current_pos)
                if button_pos != -1:
                    logger.info(f"  ✅ {button_text} - 位置正确")
                    current_pos = button_pos
                else:
                    logger.warning(f"  ⚠️  {button_text} - 未找到或位置错误")
        else:
            logger.error("  ❌ 未找到actions div")
        
        # 检查按钮样式
        logger.info("\n🎨 按钮样式检查:")
        
        style_checks = [
            ('.add-btn', 'background: #4CAF50', '✅ Add按钮绿色'),
            ('.password-btn', 'background: #17A2B8', '✅ Password按钮青色'),
            ('.save-btn', 'background: #2196F3', '✅ Save按钮蓝色'),
            ('.discard-btn', 'background: #6C757D', '✅ Discard按钮灰色'),
            ('.password-btn:hover', 'background-color: #138496', '✅ Password按钮悬停效果'),
            ('.discard-btn:hover', 'background-color: #5A6268', '✅ Discard按钮悬停效果')
        ]
        
        for selector, style_rule, message in style_checks:
            if selector in content and style_rule in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {selector} 的 {style_rule}")
        
        # 检查按钮统一样式
        logger.info("\n📏 按钮统一样式检查:")
        
        unified_checks = [
            ('.save-btn, .discard-btn, .add-btn, .delete-btn, .password-btn', '✅ 统一按钮选择器'),
            ('padding: 0.6rem 1rem', '✅ 统一按钮内边距'),
            ('font-size: 0.9rem', '✅ 统一按钮字体大小')
        ]
        
        for pattern, message in unified_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查按钮样式时发生错误: {e}")
        return False

def check_toast_implementation():
    """检查toast通知实现"""
    
    logger.info("\n🍞 Toast通知功能检查:")
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'member_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查toast HTML结构
        html_checks = [
            ('<div id="toast" class="toast"></div>', '✅ Toast HTML容器'),
        ]
        
        for pattern, message in html_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        # 检查toast CSS样式
        css_checks = [
            ('.toast {', '✅ Toast基础样式'),
            ('position: fixed;', '✅ Toast固定定位'),
            ('top: 20px;', '✅ Toast顶部位置'),
            ('right: 20px;', '✅ Toast右侧位置'),
            ('transform: translateX(400px);', '✅ Toast初始隐藏'),
            ('transition: transform 0.3s ease-in-out;', '✅ Toast动画效果'),
            ('.toast.show {', '✅ Toast显示状态'),
            ('.toast.unsaved {', '✅ Toast未保存样式'),
            ('background: #FFF3CD;', '✅ Toast淡黄色背景'),
            ('.toast.saved {', '✅ Toast已保存样式'),
            ('background: #D4EDDA;', '✅ Toast绿色背景'),
            ('.toast.saving {', '✅ Toast保存中样式')
        ]
        
        for pattern, message in css_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        # 检查toast JavaScript函数
        js_checks = [
            ('function showToast(', '✅ showToast函数'),
            ('function hideToast(', '✅ hideToast函数'),
            ('function markAsUnsaved(', '✅ markAsUnsaved函数'),
            ('function markAsSaved(', '✅ markAsSaved函数'),
            ('📝 You have unsaved changes', '✅ 未保存消息'),
            ('✅ All changes saved successfully', '✅ 已保存消息'),
            ('💾 Saving all changes...', '✅ 保存中消息'),
            ('toast.classList.add(\'show\')', '✅ Toast显示逻辑'),
            ('toast.classList.remove(\'show\')', '✅ Toast隐藏逻辑'),
            ('duration = 0', '✅ Toast持续时间参数'),
            ('duration = 5000', '✅ 5秒自动隐藏')
        ]
        
        for pattern, message in js_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查toast功能时发生错误: {e}")
        return False

def check_removed_old_elements():
    """检查是否移除了旧的元素"""
    
    logger.info("\n🧹 旧元素清理检查:")
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'member_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了旧的save-status
        removed_checks = [
            ('save-status', '⚠️  仍有save-status引用'),
            ('Refresh Data', '⚠️  仍有Refresh Data文本'),
            ('.refresh-btn', '⚠️  仍有refresh-btn样式')
        ]
        
        found_old_elements = False
        for pattern, warning in removed_checks:
            if pattern in content:
                logger.warning(f"  {warning}")
                found_old_elements = True
        
        if not found_old_elements:
            logger.info("  ✅ 已成功移除所有旧元素")
        
        # 检查新元素
        new_checks = [
            ('Discard Changes', '✅ 新的Discard Changes文本'),
            ('.discard-btn', '✅ 新的discard-btn样式'),
            ('onclick="loadMembers()"', '✅ Discard按钮功能正确')
        ]
        
        for pattern, message in new_checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查旧元素清理时发生错误: {e}")
        return False

def generate_ui_guide():
    """生成UI改进指南"""
    
    logger.info("\n📖 Member Admin UI改进指南")
    logger.info("=" * 80)
    
    guide = """
🎨 新的UI设计:

📋 按钮布局 (从左到右):
   1. 🆕 Add New Member (绿色) - 添加新成员
   2. 🔑 Generate All Passwords (青色) - 生成所有密码
   3. 💾 Save All Changes (蓝色) - 保存所有更改
   4. 🗑️  Discard Changes (灰色) - 丢弃更改

🍞 Toast通知系统:
   📍 位置: 右上角悬浮
   🎨 样式: 
      - 未保存: 淡黄色背景 + 橙色左边框
      - 已保存: 淡绿色背景 + 绿色左边框
      - 保存中: 灰色背景 + 灰色左边框
   ⏱️  行为:
      - 未保存状态: 持续显示直到保存或丢弃
      - 已保存状态: 显示5秒后自动消失
      - 保存中状态: 显示直到保存完成

🎯 用户体验改进:
   ✅ 按钮大小统一，视觉更协调
   ✅ 颜色编码清晰，功能一目了然
   ✅ Toast通知不干扰主界面
   ✅ 状态反馈及时且直观
   ✅ 自动隐藏减少视觉干扰
    """
    
    logger.info(guide)

if __name__ == '__main__':
    # 检查按钮布局和样式
    button_ok = check_button_layout_and_styles()
    
    # 检查toast实现
    toast_ok = check_toast_implementation()
    
    # 检查旧元素清理
    cleanup_ok = check_removed_old_elements()
    
    # 生成UI指南
    generate_ui_guide()
    
    if button_ok and toast_ok and cleanup_ok:
        logger.info("\n🎊 Member Admin UI改进验证成功！")
        logger.info("✅ 按钮布局和样式正确")
        logger.info("✅ Toast通知功能完整")
        logger.info("✅ 旧元素清理完成")
        logger.info("✅ 用户界面更加美观和直观")
    else:
        logger.error("\n❌ Member Admin UI改进验证失败")
    
    sys.exit(0 if (button_ok and toast_ok and cleanup_ok) else 1)
