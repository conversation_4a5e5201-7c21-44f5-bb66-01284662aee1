#!/usr/bin/env python3
"""
Simple migration script to import JSON data to existing database
"""

import os
import json
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime

def load_json_file(filename):
    """Load data from JSON file"""
    try:
        with open(f'data/{filename}', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {filename}: {e}")
        return {}

def migrate_members(cursor):
    """Migrate members data"""
    print("📥 Migrating members...")
    members_data = load_json_file('members.json')
    
    if not members_data:
        print("No members data to migrate")
        return
    
    for member_id, member in members_data.items():
        try:
            cursor.execute("""
                INSERT INTO members (id, name, email, is_active, created_at, updated_at)
                VALUES (%s, %s, %s, %s, NOW(), NOW())
                ON CONFLICT (id) DO UPDATE SET
                    name = EXCLUDED.name,
                    email = EXCLUDED.email,
                    is_active = EXCLUDED.is_active,
                    updated_at = NOW()
            """, (
                member_id,
                member.get('name'),
                member.get('email'),
                member.get('isActive', True)
            ))
            print(f"  ✅ Migrated member: {member_id}")
        except Exception as e:
            print(f"  ❌ Error migrating member {member_id}: {e}")

def migrate_activities(cursor):
    """Migrate activities data"""
    print("📥 Migrating activities...")
    activities_data = load_json_file('activities.json')
    
    if not activities_data:
        print("No activities data to migrate")
        return
    
    for activity_id, activity in activities_data.items():
        try:
            # Parse date
            activity_date = None
            if 'date' in activity:
                try:
                    activity_date = datetime.strptime(activity['date'], '%Y-%m-%d').date()
                except ValueError:
                    pass
            
            cursor.execute("""
                INSERT INTO activities (id, type, date, status, created_at, updated_at)
                VALUES (%s, %s, %s, %s, NOW(), NOW())
                ON CONFLICT (id) DO UPDATE SET
                    type = EXCLUDED.type,
                    date = EXCLUDED.date,
                    status = EXCLUDED.status,
                    updated_at = NOW()
            """, (
                activity_id,
                activity.get('type'),
                activity_date,
                activity.get('status', 'active')
            ))
            print(f"  ✅ Migrated activity: {activity_id}")
        except Exception as e:
            print(f"  ❌ Error migrating activity {activity_id}: {e}")

def migrate_checkins(cursor):
    """Migrate checkins data"""
    print("📥 Migrating checkins...")
    checkins_data = load_json_file('checkins.json')
    
    if not checkins_data:
        print("No checkins data to migrate")
        return
    
    for checkin_id, checkin in checkins_data.items():
        try:
            # Parse checkin time
            checkin_time = None
            if 'checkinTime' in checkin:
                try:
                    checkin_time = datetime.fromisoformat(checkin['checkinTime'].replace('Z', '+00:00'))
                except ValueError:
                    pass
            
            cursor.execute("""
                INSERT INTO checkins (id, activity_id, member_id, member_name, member_type, checkin_time, checkin_by, notes, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW())
                ON CONFLICT (id) DO UPDATE SET
                    activity_id = EXCLUDED.activity_id,
                    member_id = EXCLUDED.member_id,
                    member_name = EXCLUDED.member_name,
                    member_type = EXCLUDED.member_type,
                    checkin_time = EXCLUDED.checkin_time,
                    checkin_by = EXCLUDED.checkin_by,
                    notes = EXCLUDED.notes
            """, (
                checkin_id,
                checkin.get('activityId'),
                checkin.get('memberId'),
                checkin.get('memberName'),
                checkin.get('memberType'),
                checkin_time,
                checkin.get('checkinBy'),
                checkin.get('notes')
            ))
            print(f"  ✅ Migrated checkin: {checkin_id}")
        except Exception as e:
            print(f"  ❌ Error migrating checkin {checkin_id}: {e}")

def migrate_feedbacks(cursor):
    """Migrate feedbacks data"""
    print("📥 Migrating feedbacks...")
    feedbacks_data = load_json_file('feedbacks.json')
    
    if not feedbacks_data:
        print("No feedbacks data to migrate")
        return
    
    for key, feedback in feedbacks_data.items():
        try:
            # Parse created time
            created_at = None
            if 'createdAt' in feedback:
                try:
                    created_at = datetime.fromisoformat(feedback['createdAt'].replace('Z', '+00:00'))
                except ValueError:
                    pass
            
            cursor.execute("""
                INSERT INTO feedbacks (id, checkin_id, activity_id, member_id, member_name, overall_feedback, skill_json, created_by, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                ON CONFLICT (id) DO UPDATE SET
                    checkin_id = EXCLUDED.checkin_id,
                    activity_id = EXCLUDED.activity_id,
                    member_id = EXCLUDED.member_id,
                    member_name = EXCLUDED.member_name,
                    overall_feedback = EXCLUDED.overall_feedback,
                    skill_json = EXCLUDED.skill_json,
                    created_by = EXCLUDED.created_by,
                    updated_at = NOW()
            """, (
                feedback.get('id'),
                feedback.get('checkinId'),
                feedback.get('activityId'),
                feedback.get('memberId'),
                feedback.get('memberName'),
                feedback.get('overallFeedback'),
                json.dumps(feedback.get('skillAssessment', {})),
                feedback.get('createdBy'),
                created_at
            ))
            print(f"  ✅ Migrated feedback: {feedback.get('id')}")
        except Exception as e:
            print(f"  ❌ Error migrating feedback {feedback.get('id')}: {e}")

def main():
    """Main migration function"""
    print("🚀 Starting JSON to Database Migration")
    print("=" * 50)
    
    database_url = "postgresql://snownavi:SnowNavi2025!@localhost:5432/snownavi"
    
    try:
        conn = psycopg2.connect(database_url, cursor_factory=RealDictCursor)
        cursor = conn.cursor()
        
        print("✅ Connected to database")
        
        # Run migrations
        migrate_members(cursor)
        migrate_activities(cursor)
        migrate_checkins(cursor)
        migrate_feedbacks(cursor)
        
        # Commit changes
        conn.commit()
        print("\n✅ All migrations completed successfully!")
        
        # Verify data
        print("\n📊 Verification:")
        tables = ['members', 'activities', 'checkins', 'feedbacks']
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
            count = cursor.fetchone()
            print(f"  - {table}: {count['count']} records")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    if success:
        print("\n🎉 Migration completed successfully!")
        print("You can now switch to database mode by setting DATA_MODE=database in backend/.env")
    else:
        print("\n💥 Migration failed!")
