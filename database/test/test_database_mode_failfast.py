#!/usr/bin/env python3
"""
Test database mode fail-fast behavior (no automatic fallback to JSON)
"""

import os
import sys

def test_database_mode_with_valid_connection():
    """Test database mode with valid database connection"""
    print("🧪 Testing Database Mode with Valid Connection")
    print("=" * 50)
    
    try:
        # Set environment for database mode
        os.environ['DATABASE_URL'] = "postgresql://snownavi:SnowNavi2025!@localhost:5432/snownavi"
        os.environ['DATA_MODE'] = 'database'
        
        sys.path.append('backend')
        from database_access import DataAccessLayer
        
        dal = DataAccessLayer(mode='database')
        print("✅ DataAccessLayer initialized in database mode")
        
        # Test getting members (should work)
        members = dal.get_all('members')
        print(f"✅ Retrieved {len(members)} members from database")
        
        # Test getting activities (should work)
        activities = dal.get_all('activities')
        print(f"✅ Retrieved {len(activities)} activities from database")
        
        return True
        
    except Exception as e:
        print(f"❌ Database mode test failed: {e}")
        return False

def test_database_mode_with_invalid_connection():
    """Test database mode with invalid database connection (should fail fast)"""
    print("\n🧪 Testing Database Mode with Invalid Connection")
    print("=" * 50)
    
    try:
        # Set environment for database mode with invalid URL
        os.environ['DATABASE_URL'] = "postgresql://invalid:invalid@localhost:9999/invalid"
        os.environ['DATA_MODE'] = 'database'
        
        sys.path.append('backend')
        from database_access import DataAccessLayer
        
        dal = DataAccessLayer(mode='database')
        print("⚠️  DataAccessLayer initialized (connection not tested yet)")
        
        # This should fail fast without fallback to JSON
        try:
            members = dal.get_all('members')
            print(f"❌ UNEXPECTED: Retrieved {len(members)} members (should have failed)")
            return False
        except Exception as e:
            print(f"✅ EXPECTED: Database operation failed fast: {type(e).__name__}")
            print(f"   Error message: {str(e)[:100]}...")
            return True
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        return False

def test_hybrid_mode_with_invalid_connection():
    """Test hybrid mode with invalid database connection (should fallback to JSON)"""
    print("\n🧪 Testing Hybrid Mode with Invalid Connection")
    print("=" * 50)
    
    try:
        # Set environment for hybrid mode with invalid URL
        os.environ['DATABASE_URL'] = "postgresql://invalid:invalid@localhost:9999/invalid"
        os.environ['DATA_MODE'] = 'hybrid'
        
        sys.path.append('backend')
        from database_access import DataAccessLayer
        
        dal = DataAccessLayer(mode='hybrid')
        print("✅ DataAccessLayer initialized in hybrid mode")
        
        # This should fallback to JSON
        try:
            members = dal.get_all('members')
            print(f"✅ EXPECTED: Retrieved {len(members)} members (fallback to JSON)")
            return True
        except Exception as e:
            print(f"❌ UNEXPECTED: Hybrid mode failed completely: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        return False

def test_json_mode():
    """Test JSON mode (should always work)"""
    print("\n🧪 Testing JSON Mode")
    print("=" * 50)
    
    try:
        # Set environment for JSON mode
        os.environ['DATA_MODE'] = 'json'
        
        sys.path.append('backend')
        from database_access import DataAccessLayer
        
        dal = DataAccessLayer(mode='json')
        print("✅ DataAccessLayer initialized in JSON mode")
        
        # This should always work
        members = dal.get_all('members')
        print(f"✅ Retrieved {len(members)} members from JSON")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON mode test failed: {e}")
        return False

def main():
    """Run all fail-fast behavior tests"""
    print("🚀 Database Mode Fail-Fast Behavior Test")
    print("Testing that database mode fails fast without JSON fallback...")
    print()
    
    tests = [
        ("Database Mode (Valid)", test_database_mode_with_valid_connection),
        ("Database Mode (Invalid)", test_database_mode_with_invalid_connection),
        ("Hybrid Mode (Invalid)", test_hybrid_mode_with_invalid_connection),
        ("JSON Mode", test_json_mode)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"💥 {test_name} test CRASHED: {e}")
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("\n✨ Fail-fast behavior is working correctly!")
        print("\n📋 Summary:")
        print("  ✅ Database mode fails fast when database is unavailable")
        print("  ✅ Database mode works when database is available")
        print("  ✅ Hybrid mode falls back to JSON when database fails")
        print("  ✅ JSON mode always works")
        
        print("\n🔧 Behavior Confirmed:")
        print("  • Database mode: NO automatic fallback (fail fast)")
        print("  • Hybrid mode: Automatic fallback to JSON")
        print("  • JSON mode: Direct JSON access")
        
        return True
    else:
        print("⚠️  Some tests failed. Please review the behavior.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
