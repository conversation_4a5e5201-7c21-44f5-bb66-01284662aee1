#!/usr/bin/env python3
"""
测试Member Admin的validity period修复
验证validity period可以为空，以及状态变更时的自动处理
"""

import os
import sys
import json
import logging
import time
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_validity_period_fix():
    """测试validity period的修复"""
    
    # 设置环境
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
    os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
    
    try:
        from database_access import DataAccessLayer
        
        logger.info("🔍 测试Member Validity Period修复")
        logger.info("=" * 80)
        
        # 创建database模式的实例
        dal = DataAccessLayer(mode='database')
        
        # 创建测试成员
        test_member_id = f"SN2025TEST{int(time.time())}"
        test_member = {
            "id": test_member_id,
            "name": "Test Validity Period",
            "isActive": True,
            "validityPeriod": "12/06/2025 - 12/06/2026",
            "gender": "test"
        }
        
        logger.info(f"📝 创建测试成员: {test_member_id}")
        created_member = dal.create('members', test_member_id, test_member)
        if not created_member:
            logger.error("❌ 创建测试成员失败")
            return False
        
        # 测试1: 更新成员，validity period为正常字符串
        logger.info("\n🧪 测试1: 更新validity period为正常字符串")
        update_data_1 = {
            "id": test_member_id,
            "name": "Test Validity Period Updated",
            "isActive": True,
            "validityPeriod": "01/01/2025 - 31/12/2025"
        }
        
        success_1 = dal.update('members', test_member_id, update_data_1)
        if success_1:
            logger.info("  ✅ 正常字符串validity period更新成功")
        else:
            logger.error("  ❌ 正常字符串validity period更新失败")
            return False
        
        # 测试2: 更新成员，validity period为空字符串
        logger.info("\n🧪 测试2: 更新validity period为空字符串")
        update_data_2 = {
            "id": test_member_id,
            "name": "Test Validity Period Updated",
            "isActive": False,
            "validityPeriod": ""
        }
        
        success_2 = dal.update('members', test_member_id, update_data_2)
        if success_2:
            logger.info("  ✅ 空字符串validity period更新成功")
        else:
            logger.error("  ❌ 空字符串validity period更新失败")
            return False
        
        # 测试3: 更新成员，不包含validity period字段
        logger.info("\n🧪 测试3: 更新时不包含validity period字段")
        update_data_3 = {
            "id": test_member_id,
            "name": "Test No Validity Period",
            "isActive": True
        }
        
        success_3 = dal.update('members', test_member_id, update_data_3)
        if success_3:
            logger.info("  ✅ 不包含validity period字段更新成功")
        else:
            logger.error("  ❌ 不包含validity period字段更新失败")
            return False
        
        # 测试4: 验证数据库中的数据
        logger.info("\n🧪 测试4: 验证数据库中的最终数据")
        final_member = dal.get_by_id('members', test_member_id)
        if final_member:
            logger.info(f"  ✅ 最终成员数据: {json.dumps(final_member, indent=2, ensure_ascii=False)}")
        else:
            logger.error("  ❌ 无法获取最终成员数据")
            return False
        
        # 清理测试数据
        logger.info(f"\n🧹 清理测试数据: {test_member_id}")
        dal.delete('members', test_member_id)
        
        logger.info("\n🎉 所有validity period测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def test_server_api_validation():
    """测试server.py API的验证逻辑"""
    
    logger.info("\n🔍 检查server.py API验证逻辑...")
    
    server_file = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'server.py')
    
    try:
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修复的验证逻辑
        checks = [
            ('validityPeriod must be a string or null', '✅ 修复了validityPeriod验证'),
            ('required_fields = [\'name\', \'isActive\']', '✅ 移除了validityPeriod必填要求'),
            ('isinstance(validity_period, str)', '✅ 允许字符串类型的validityPeriod')
        ]
        
        for pattern, message in checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        # 检查是否还有旧的错误验证
        old_patterns = [
            ('validityPeriod must be an object', '⚠️  仍有旧的对象验证'),
            ('required_fields = [\'name\', \'isActive\', \'validityPeriod\']', '⚠️  仍要求validityPeriod必填')
        ]
        
        for pattern, warning in old_patterns:
            if pattern in content:
                logger.warning(f"  {warning}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查server.py时发生错误: {e}")
        return False

def test_member_admin_status_logic():
    """检查member_admin.html中的状态变更逻辑"""
    
    logger.info("\n🔍 检查member_admin.html状态变更逻辑...")
    
    admin_file = os.path.join(os.path.dirname(__file__), '..', '..', 'member_admin.html')
    
    try:
        with open(admin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查状态变更逻辑
        checks = [
            ('// If status is changing from active to inactive (Expired)', '✅ 添加了过期状态处理'),
            ('member.validityPeriod = \'\';', '✅ 过期时清空validity period'),
            ('validityInput.value = \'\';', '✅ 过期时清空输入框'),
            ('if (!oldStatus && newStatus)', '✅ 激活时生成validity period')
        ]
        
        for pattern, message in checks:
            if pattern in content:
                logger.info(f"  {message}")
            else:
                logger.warning(f"  ⚠️  未找到: {pattern}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查member_admin.html时发生错误: {e}")
        return False

if __name__ == '__main__':
    # 检查server.py API验证
    test_server_api_validation()
    
    # 检查member_admin.html状态逻辑
    test_member_admin_status_logic()
    
    # 运行validity period测试
    success = test_validity_period_fix()
    
    if success:
        logger.info("\n🎊 Member Validity Period修复验证成功！")
        logger.info("✅ validity period现在可以为空")
        logger.info("✅ 状态变更时自动处理validity period")
        logger.info("✅ 所有CRUD操作正常工作")
    else:
        logger.error("\n❌ Member Validity Period修复验证失败")
    
    sys.exit(0 if success else 1)
