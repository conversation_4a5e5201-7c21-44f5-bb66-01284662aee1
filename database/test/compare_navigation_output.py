#!/usr/bin/env python3
"""
对比Navigation数据在database模式和json模式下的返回值
确保两种模式返回的数据完全一致
"""

import os
import sys
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def compare_navigation_output():
    """对比两种模式下的navigation数据输出"""
    
    # 设置环境
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
    os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
    
    try:
        from database_access import DataAccessLayer
        
        logger.info("🔍 开始对比Navigation数据输出")
        logger.info("=" * 80)
        
        # 1. 测试JSON模式
        logger.info("📄 测试JSON模式...")
        json_dal = DataAccessLayer(mode='json')
        json_data = json_dal.get_all('navigation')
        logger.info(f"JSON模式: 获取到 {len(json_data.get('items', []))} 条导航项")
        
        # 2. 测试Database模式
        logger.info("🗄️  测试Database模式...")
        db_dal = DataAccessLayer(mode='database')
        db_data = db_dal.get_all('navigation')
        logger.info(f"Database模式: 获取到 {len(db_data.get('items', []))} 条导航项")
        
        # 3. 对比数据结构
        if 'items' not in json_data or 'items' not in db_data:
            logger.error("❌ 数据结构不正确: 缺少'items'字段")
            return False
        
        json_items = json_data['items']
        db_items = db_data['items']
        
        # 4. 对比数据数量
        if len(json_items) != len(db_items):
            logger.error(f"❌ 导航项数量不一致: JSON={len(json_items)}, Database={len(db_items)}")
            return False
        
        logger.info(f"✅ 导航项数量一致: {len(json_items)} 条记录")
        
        # 5. 对比具体数据
        logger.info("\n📊 详细数据对比:")
        
        mismatches = []
        matches = 0
        
        # 按order排序进行对比
        json_items_sorted = sorted(json_items, key=lambda x: x.get('order', 0))
        db_items_sorted = sorted(db_items, key=lambda x: x.get('order', 0))
        
        for i, (json_item, db_item) in enumerate(zip(json_items_sorted, db_items_sorted)):
            item_id = json_item.get('id', f'item_{i}')
            
            # 对比每个字段
            field_mismatches = []
            
            # 检查所有JSON中的字段
            for field in json_item.keys():
                json_value = json_item.get(field)
                db_value = db_item.get(field)
                
                # 特殊处理translations字段
                if field == 'translations':
                    if json_value != db_value:
                        field_mismatches.append(f"  {field}: 翻译对象不匹配")
                        # 详细对比翻译对象
                        if isinstance(json_value, dict) and isinstance(db_value, dict):
                            for lang in set(json_value.keys()) | set(db_value.keys()):
                                json_trans = json_value.get(lang, "缺失")
                                db_trans = db_value.get(lang, "缺失")
                                if json_trans != db_trans:
                                    field_mismatches.append(f"    {lang}: JSON='{json_trans}' vs DB='{db_trans}'")
                elif json_value != db_value:
                    field_mismatches.append(f"  {field}: JSON='{json_value}' vs DB='{db_value}'")
            
            # 检查Database中是否有额外字段
            for field in db_item.keys():
                if field not in json_item:
                    field_mismatches.append(f"  {field}: 仅在Database中存在='{db_item[field]}'")
            
            if field_mismatches:
                mismatches.append(f"导航项 {item_id} 字段不匹配:")
                mismatches.extend(field_mismatches)
            else:
                matches += 1
                logger.info(f"✅ {item_id}: 完全匹配")
        
        # 6. 输出结果
        logger.info(f"\n📈 对比结果:")
        logger.info(f"  完全匹配: {matches}/{len(json_items)}")
        logger.info(f"  不匹配: {len(mismatches)}")
        
        if mismatches:
            logger.error("\n❌ 发现不匹配项:")
            for mismatch in mismatches[:15]:  # 显示前15个
                logger.error(f"  {mismatch}")
            if len(mismatches) > 15:
                logger.error(f"  ... 还有 {len(mismatches) - 15} 个不匹配项")
            return False
        else:
            logger.info("\n🎉 所有数据完全匹配！")
            
            # 7. 显示数据结构示例
            logger.info("\n📋 数据结构示例:")
            if json_items:
                sample_item = json_items[0]
                logger.info(f"JSON格式 (第一个导航项):")
                for key, value in sample_item.items():
                    if isinstance(value, dict):
                        logger.info(f"  {key}: {{翻译对象}}")
                    else:
                        logger.info(f"  {key}: {value}")
                
                logger.info(f"\nDatabase格式 (第一个导航项):")
                db_sample = db_items[0]
                for key, value in db_sample.items():
                    if isinstance(value, dict):
                        logger.info(f"  {key}: {{翻译对象}}")
                    else:
                        logger.info(f"  {key}: {value}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 对比过程中发生错误: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def test_specific_navigation_item():
    """测试特定导航项的数据"""
    
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
    os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
    
    try:
        from database_access import DataAccessLayer
        
        logger.info("\n🔍 测试特定导航项数据...")
        
        # 选择一个测试导航项
        test_item_id = "courses"
        
        # JSON模式
        json_dal = DataAccessLayer(mode='json')
        json_data = json_dal.get_all('navigation')
        json_item = None
        for item in json_data.get('items', []):
            if item.get('id') == test_item_id:
                json_item = item
                break
        
        # Database模式
        db_dal = DataAccessLayer(mode='database')
        db_data = db_dal.get_all('navigation')
        db_item = None
        for item in db_data.get('items', []):
            if item.get('id') == test_item_id:
                db_item = item
                break
        
        if not json_item or not db_item:
            logger.error(f"❌ 找不到导航项: {test_item_id}")
            return
        
        logger.info(f"\n导航项 {test_item_id} 详细对比:")
        logger.info("JSON数据:")
        logger.info(json.dumps(json_item, indent=2, ensure_ascii=False))
        
        logger.info("\nDatabase数据:")
        logger.info(json.dumps(db_item, indent=2, ensure_ascii=False))
        
        # 字段级对比
        logger.info("\n字段级对比:")
        all_fields = set(json_item.keys()) | set(db_item.keys())
        for field in sorted(all_fields):
            json_val = json_item.get(field, "❌ 缺失")
            db_val = db_item.get(field, "❌ 缺失")
            
            # 对于翻译对象，进行深度比较
            if isinstance(json_val, dict) and isinstance(db_val, dict):
                if json_val == db_val:
                    status = "✅"
                else:
                    status = "❌"
                logger.info(f"  {status} {field}: 翻译对象 - {'匹配' if status == '✅' else '不匹配'}")
            else:
                status = "✅" if json_val == db_val else "❌"
                logger.info(f"  {status} {field}: JSON='{json_val}' | DB='{db_val}'")
        
    except Exception as e:
        logger.error(f"❌ 测试特定导航项时发生错误: {e}")

def check_data_source():
    """检查当前数据来源"""
    
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
    os.environ['DATABASE_URL'] = "postgresql://snownavi_user:snownavi_password@localhost:5432/snownavi"
    os.environ['DATA_MODE'] = "database"
    
    try:
        from database_access import DataAccessLayer
        
        logger.info("\n🔍 检查数据来源...")
        
        # 创建database模式的实例
        dal = DataAccessLayer(mode='database')
        logger.info(f"数据访问层模式: {dal.mode}")
        
        # 检查是否真的从数据库获取数据
        navigation = dal.get_all('navigation')
        items_count = len(navigation.get('items', []))
        logger.info(f"获取到 {items_count} 个导航项")
        
        # 检查数据库连接
        if dal.engine:
            logger.info("✅ 数据库连接正常")
        else:
            logger.error("❌ 数据库连接失败")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查数据源时发生错误: {e}")
        return False

def check_server_integration():
    """检查server.py中的集成"""
    
    logger.info("\n🔍 检查server.py集成...")
    
    server_file = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'server.py')
    
    try:
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查navigation的GET路由
        if 'data_access.get_all(\'navigation\')' in content:
            logger.info("✅ server.py中navigation GET路由使用了data_access")
        else:
            logger.warning("⚠️  server.py中navigation GET路由可能未使用data_access")
        
        # 检查是否还有直接文件操作
        if 'navigation.json' in content and 'send_from_directory' in content:
            logger.info("✅ server.py中有JSON文件的fallback机制")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查server.py集成时发生错误: {e}")
        return False

if __name__ == '__main__':
    # 检查数据源
    check_data_source()
    
    # 检查server.py集成
    check_server_integration()
    
    # 运行对比测试
    success = compare_navigation_output()
    
    # 测试特定导航项
    test_specific_navigation_item()
    
    sys.exit(0 if success else 1)
