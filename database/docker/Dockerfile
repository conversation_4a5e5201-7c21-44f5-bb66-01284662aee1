FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY backend/requirements.txt /app/backend/requirements.txt
RUN pip install -r backend/requirements.txt

# Copy the entire project
COPY . /app/

# Set Python path
ENV PYTHONPATH=/app

CMD ["python", "database/migrate.py"]
