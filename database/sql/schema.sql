-- SnowNavi Database Schema
-- This file defines the PostgreSQL database schema for SnowNavi

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Members table
CREATE TABLE IF NOT EXISTS members (
    id VARCHAR(20) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    is_active BOOLEAN DEFAULT true,
    validity_period_start DATE,
    validity_period_end DATE,
    password VARCHAR(255),
    gender VARCHAR(10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Activities table
CREATE TABLE IF NOT EXISTS activities (
    id VARCHAR(20) PRIMARY KEY,
    name_en VARCHAR(255),
    name_zh VARCHAR(255),
    name_nl VARCHAR(255),
    type VARCHAR(50),
    date DATE,
    location_en VARCHAR(255),
    location_zh VARCHAR(255),
    location_nl VARCHAR(255),
    description_en TEXT,
    description_zh TEXT,
    description_nl TEXT,
    status VARCHAR(50) DEFAULT 'active',
    feedback_template_id VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Checkins table
CREATE TABLE IF NOT EXISTS checkins (
    id VARCHAR(20) PRIMARY KEY,
    activity_id VARCHAR(20) REFERENCES activities(id),
    member_id VARCHAR(20) REFERENCES members(id),
    member_name VARCHAR(255),
    member_type VARCHAR(20) CHECK (member_type IN ('member', 'coach')),
    checkin_time TIMESTAMP WITH TIME ZONE,
    checkin_by VARCHAR(255),
    checkin_by_email VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Feedback templates table
CREATE TABLE IF NOT EXISTS feedback_templates (
    id VARCHAR(50) PRIMARY KEY,
    name_en VARCHAR(255),
    name_zh VARCHAR(255),
    name_nl VARCHAR(255),
    description_en TEXT,
    description_zh TEXT,
    description_nl TEXT,
    target_level VARCHAR(50),
    sport VARCHAR(50),
    sections JSONB,
    version VARCHAR(10) DEFAULT '1.0',
    created_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Feedbacks table
CREATE TABLE IF NOT EXISTS feedbacks (
    id VARCHAR(20) PRIMARY KEY,
    checkin_id VARCHAR(20) REFERENCES checkins(id),
    activity_id VARCHAR(20) REFERENCES activities(id),
    member_id VARCHAR(20) REFERENCES members(id),
    member_name VARCHAR(255),
    overall_feedback TEXT,
    skill_assessment JSONB,
    feedback TEXT,
    created_by VARCHAR(255),
    created_by_email VARCHAR(255),
    student_rating JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Student ratings table
CREATE TABLE IF NOT EXISTS student_ratings (
    id VARCHAR(50) PRIMARY KEY,
    feedback_id VARCHAR(20) REFERENCES feedbacks(id),
    student_email VARCHAR(255),
    student_name VARCHAR(255),
    member_id VARCHAR(20) REFERENCES members(id),
    activity_id VARCHAR(20) REFERENCES activities(id),
    instructor_name VARCHAR(255),
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Email templates table
CREATE TABLE IF NOT EXISTS email_templates (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255),
    description TEXT,
    type VARCHAR(50),
    subject VARCHAR(500),
    html_content TEXT,
    triggers JSONB,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Navigation table
CREATE TABLE IF NOT EXISTS navigation_items (
    id VARCHAR(50) PRIMARY KEY,
    url VARCHAR(500),
    translation_en VARCHAR(255),
    translation_zh VARCHAR(255),
    translation_nl VARCHAR(255),
    visible BOOLEAN DEFAULT true,
    order_index INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Courses table (static course information)
CREATE TABLE IF NOT EXISTS courses (
    id VARCHAR(50) PRIMARY KEY,
    course_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_activities_date ON activities(date);
CREATE INDEX IF NOT EXISTS idx_checkins_activity_id ON checkins(activity_id);
CREATE INDEX IF NOT EXISTS idx_checkins_member_id ON checkins(member_id);
CREATE INDEX IF NOT EXISTS idx_feedbacks_activity_id ON feedbacks(activity_id);
CREATE INDEX IF NOT EXISTS idx_feedbacks_member_id ON feedbacks(member_id);
CREATE INDEX IF NOT EXISTS idx_student_ratings_feedback_id ON student_ratings(feedback_id);
CREATE INDEX IF NOT EXISTS idx_members_email ON members(email);

-- Create triggers for updating updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_members_updated_at BEFORE UPDATE ON members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_activities_updated_at BEFORE UPDATE ON activities FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_feedback_templates_updated_at BEFORE UPDATE ON feedback_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_feedbacks_updated_at BEFORE UPDATE ON feedbacks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_templates_updated_at BEFORE UPDATE ON email_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_navigation_items_updated_at BEFORE UPDATE ON navigation_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON courses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
