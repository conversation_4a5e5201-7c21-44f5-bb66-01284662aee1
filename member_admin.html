<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Member Admin Panel</title>

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="assets/picture/snownavi_logo.png">
  <link rel="icon" type="image/png" sizes="16x16" href="assets/picture/snownavi_logo.png">
  <link rel="shortcut icon" href="assets/picture/snownavi_logo.png">

  <style>
    :root {
      --main-red: #E53512;
      --bg-light: #F9F4F3;
      --text-dark: #2F2F2F;
      --text-gray: #717171;
      --contrast-white: #FFFFFF;
      --accent-blue: #9ED4E7;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--bg-light);
      color: var(--text-dark);
      height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .header {
      background: var(--contrast-white);
      padding: 1rem 2rem;
      border-bottom: 3px solid var(--main-red);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logo-section {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .logo-section img {
      width: 50px;
      height: 50px;
    }

    .logo-section h1 {
      color: var(--main-red);
      font-size: 1.8rem;
      margin: 0;
    }

    .nav-links {
      display: flex;
      gap: 1rem;
    }

    .nav-link {
      color: var(--text-gray);
      text-decoration: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      transition: all 0.2s ease;
    }

    .nav-link:hover {
      background: var(--bg-light);
      color: var(--main-red);
    }

    /* Mobile responsive styles */
    @media (max-width: 768px) {
      .header {
        padding: 0.75rem 1rem;
      }

      .header-content {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
      }

      .logo-section {
        justify-content: center;
      }

      .logo-section h1 {
        font-size: 1.5rem;
      }

      .nav-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
        order: 2;
      }

      .nav-link {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
        white-space: nowrap;
      }

      .user-info {
        justify-content: center;
        order: 1;
        font-size: 0.9rem;
      }

      .user-info img {
        width: 28px;
        height: 28px;
      }
    }

    @media (max-width: 480px) {
      .header {
        padding: 0.5rem;
      }

      .logo-section img {
        width: 40px;
        height: 40px;
      }

      .logo-section h1 {
        font-size: 1.3rem;
      }

      .nav-links {
        gap: 0.25rem;
      }

      .nav-link {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
      }

      .user-info {
        font-size: 0.8rem;
      }

      .user-info img {
        width: 24px;
        height: 24px;
      }
    }
    h1 {
      color: #E53512;
      padding: 1rem;
      margin: 0;
    }

    .main-container {
      display: flex;
      flex: 1;
      overflow: hidden;
    }
    .member-list {
      width: 250px;
      background: #fff;
      border-right: 1px solid #ddd;
      overflow-y: auto;
      padding: 1rem;
    }
    .member-item {
      padding: 0.5rem;
      cursor: pointer;
      border-radius: 4px;
      margin-bottom: 0.25rem;
    }
    .member-item:hover {
      background: #f0f0f0;
    }
    .member-item.active {
      background: #E53512;
      color: white;
    }
    .form-container {
      flex: 1;
      padding: 1rem;
      overflow-y: auto;
    }
    .member-block {
      background: white;
      padding: 1rem;
      margin-bottom: 1rem;
      border-radius: 8px;
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
    }
    label {
      font-weight: bold;
      display: block;
      margin-top: 1rem;
    }
    input, textarea, select {
      width: 100%;
      padding: 0.5rem;
      margin-top: 0.2rem;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    textarea {
      min-height: 100px;
    }
    .save-btn, .discard-btn, .add-btn, .delete-btn, .password-btn {
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      margin: 1rem 0;
    }
    .delete-btn {
      background: #ff4444;
    }
    .add-btn {
      background: #4CAF50;
    }

    .save-btn {
      background: #2196F3;
      font-weight: bold;
    }

    .discard-btn {
      background: #6C757D;
    }

    .password-btn {
      background: #17A2B8;
    }
    .actions {
      padding: 0.5rem 1rem;
      background: #fff;
      border-top: 1px solid #ddd;
      display: flex;
      justify-content: space-between;
    }
    .no-selection {
      display: flex;
      height: 100%;
      align-items: center;
      justify-content: center;
      color: #777;
      font-size: 1.2rem;
    }
    .status-badge {
      display: inline-block;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.8rem;
      margin-left: 0.5rem;
    }
    .status-active {
      background-color: #e6f7e6;
      color: #2e7d32;
    }
    .status-expired {
      background-color: #ffebee;
      color: #c62828;
    }
    .custom-fields {
      margin-top: 1rem;
      border-top: 1px solid #eee;
      padding-top: 1rem;
    }
    .custom-field {
      display: flex;
      margin-bottom: 0.5rem;
      align-items: center;
    }
    .custom-field input {
      margin-right: 0.5rem;
    }
    .custom-field button {
      background: #ff4444;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      padding: 0.25rem 0.5rem;
    }
    .add-field-btn {
      background: #4CAF50;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 0.5rem;
    }
    .back-link {
      display: inline-block;
      margin: 1rem;
      color: #E53512;
      text-decoration: none;
    }
    .back-link:hover {
      text-decoration: underline;
    }



    /* Filter buttons with statistics styles */
    .filter-container {
      padding: 0.5rem;
      display: flex;
      gap: 0.4rem;
      flex-wrap: nowrap;
      justify-content: center;
    }
    .filter-btn {
      background: #f8f9fa;
      border: 1px solid #ddd;
      padding: 0.5rem 0.75rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.8rem;
      font-weight: 500;
      transition: all 0.2s;
      flex: 1;
      min-width: 0;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .filter-btn:hover {
      background: #e9ecef;
      border-color: #adb5bd;
    }
    .filter-btn.active {
      background: #E53512;
      color: white;
      border-color: #E53512;
      box-shadow: 0 2px 4px rgba(229, 53, 18, 0.2);
    }

    /* Authentication styles */
    .auth-container {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.9);
      z-index: 1000;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }
    .auth-message {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .auth-message h2 {
      color: #E53512;
      margin-top: 0;
    }
    .auth-btn {
      background: #E53512;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      margin-top: 1rem;
      text-decoration: none;
      display: inline-block;
    }
    .user-info {
      display: flex;
      align-items: center;
      position: absolute;
      top: 1rem;
      right: 1rem;
    }
    .user-info img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 0.5rem;
    }
    .logout-btn {
      background: none;
      border: none;
      color: #E53512;
      cursor: pointer;
      margin-left: 1rem;
      text-decoration: underline;
    }

    /* Password button hover effect */
    .password-btn:hover {
      background-color: #138496;
    }

    .discard-btn:hover {
      background-color: #5A6268;
    }



    /* Toast notification styles */
    .toast {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 8px;
      color: #333;
      font-weight: 500;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      transform: translateX(400px);
      transition: transform 0.3s ease-in-out;
      max-width: 300px;
    }

    .toast.show {
      transform: translateX(0);
    }

    .toast.unsaved {
      background: #FFF3CD;
      border-left: 4px solid #FFC107;
    }

    .toast.saved {
      background: #D4EDDA;
      border-left: 4px solid #28A745;
    }

    .toast.saving {
      background: #E2E3E5;
      border-left: 4px solid #6C757D;
    }

    /* Modal styling */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
    }

    .modal-content {
      background-color: #fff;
      margin: 10% auto;
      padding: 20px;
      border-radius: 8px;
      width: 80%;
      max-width: 600px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .close-modal {
      color: #aaa;
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
    }

    .close-modal:hover {
      color: #000;
    }

    .password-list {
      margin-top: 1rem;
      max-height: 400px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 0.5rem;
    }

    .password-item {
      padding: 0.5rem;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
    }

    .password-item:last-child {
      border-bottom: none;
    }

    /* Mobile responsive styles */
    @media (max-width: 768px) {
      body {
        padding: 0;
        min-height: 100vh;
      }

      /* Ensure viewport height is properly calculated on mobile */
      .main-container {
        min-height: calc(100vh - 180px) !important;
      }

      /* Handle mobile keyboard appearance */
      @supports (-webkit-touch-callout: none) {
        .main-container {
          min-height: calc(100vh - 180px) !important;
          height: auto !important;
        }
      }

      h1 {
        font-size: 1.5rem;
        text-align: center;
        margin: 0.5rem;
        padding: 0 1rem;
      }

      .back-link {
        margin: 0.5rem 1rem !important;
        font-size: 0.9rem;
      }



      .main-container {
        flex-direction: column;
        height: auto;
        min-height: calc(100vh - 200px);
      }

      .member-list {
        width: 100%;
        max-height: 30vh;
        min-height: 200px;
        border-right: none;
        border-bottom: 1px solid #ddd;
        flex-shrink: 0;
      }

      .member-item {
        padding: 0.75rem;
        font-size: 0.9rem;
      }

      .form-container {
        padding: 0.5rem;
        flex: 1;
        min-height: 0;
        overflow-y: auto;
      }

      .member-block {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
      }

      .member-block h3 {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
      }

      input, textarea, select {
        padding: 0.6rem;
        font-size: 0.9rem;
        /* Ensure input fields are properly visible when focused */
        scroll-margin-top: 100px;
      }

      /* Auto-scroll to focused input on mobile */
      input:focus, textarea:focus, select:focus {
        scroll-behavior: smooth;
      }

      label {
        font-size: 0.9rem;
        margin-top: 0.75rem;
      }

      .save-btn, .discard-btn, .add-btn, .delete-btn, .password-btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
        margin: 0.5rem 0.25rem 0.5rem 0;
      }

      .actions {
        padding: 0.75rem;
        flex-direction: column;
        gap: 0.5rem;
      }

      .actions button {
        width: 100%;
        margin: 0.25rem 0;
      }

      .custom-field {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
      }

      .custom-field input {
        margin-right: 0;
        margin-bottom: 0.25rem;
      }

      .custom-field button {
        align-self: flex-start;
        padding: 0.4rem 0.8rem;
      }

      .status-badge {
        display: block;
        margin: 0.5rem 0;
        text-align: center;
      }

      .user-info {
        position: static;
        justify-content: center;
        margin: 0.5rem;
        padding: 0.5rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .modal-content {
        width: 95%;
        margin: 5% auto;
        padding: 1rem;
      }

      .password-list {
        max-height: 300px;
      }

      .password-item {
        flex-direction: column;
        gap: 0.25rem;
        padding: 0.75rem;
      }

      .no-selection {
        font-size: 1rem;
        padding: 2rem 1rem;
        text-align: center;
      }

      /* Mobile responsive styles for filters */
      .filter-container {
        padding: 0.4rem;
        gap: 0.3rem;
        flex-wrap: nowrap;
      }
      .filter-btn {
        padding: 0.4rem 0.5rem;
        font-size: 0.7rem;
        min-width: 0;
        flex: 1;
      }
    }

    @media (max-width: 480px) {
      h1 {
        font-size: 1.3rem;
      }



      .member-list {
        max-height: 25vh;
        min-height: 150px;
      }

      .member-item {
        padding: 0.5rem;
        font-size: 0.85rem;
      }

      .member-block {
        padding: 0.5rem;
      }

      .member-block h3 {
        font-size: 1rem;
      }

      input, textarea, select {
        padding: 0.5rem;
        font-size: 0.85rem;
      }

      label {
        font-size: 0.85rem;
      }

      .save-btn, .discard-btn, .add-btn, .delete-btn, .password-btn {
        padding: 0.5rem 0.8rem;
        font-size: 0.85rem;
      }

      .actions {
        padding: 0.5rem;
      }

      .custom-field button {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
      }

      .add-field-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
      }

      .modal-content {
        padding: 0.75rem;
      }

      .password-item {
        padding: 0.5rem;
        font-size: 0.85rem;
      }

      /* Extra small screen styles for filters */
      .filter-container {
        padding: 0.3rem;
        gap: 0.2rem;
      }
      .filter-btn {
        padding: 0.3rem 0.4rem;
        font-size: 0.65rem;
        border-radius: 3px;
      }
    }
  </style>
</head>
<body>
  <!-- Authentication overlay -->
  <div class="auth-container" id="auth-container">
    <div class="auth-message">
      <h2>Authentication Required</h2>
      <p>You need to be logged in to access this page.</p>
      <a href="login.html" class="auth-btn">Go to Login</a>
    </div>
  </div>

  <div class="header">
    <div class="header-content">
      <div class="logo-section">
        <img src="assets/picture/snownavi_logo.png" alt="SnowNavi Logo">
        <h1>SnowNavi Admin</h1>
      </div>
      <nav class="nav-links">
        <a href="admin.html" class="nav-link">🏠 Dashboard</a>
        <a href="course_admin.html" class="nav-link">📚 Courses</a>
        <a href="member_admin.html" class="nav-link" style="color: var(--main-red);">👥 Members</a>
        <a href="activity_admin.html" class="nav-link">📅 Activities</a>
        <a href="checkin_admin.html" class="nav-link">✅ Check-ins</a>
        <a href="feedback_template_admin.html" class="nav-link">📝 Templates</a>
      </nav>
      <div class="user-info" id="user-info"></div>
    </div>
  </div>

  <div style="padding: 1rem 2rem;">
    <h2 style="color: var(--main-red); margin: 0 0 1rem 0;">Member Management</h2>

  <!-- Filter Buttons with Statistics -->
  <div class="filter-container">
    <button class="filter-btn active" onclick="filterMembers('all')" id="filter-all">All (0)</button>
    <button class="filter-btn" onclick="filterMembers('active')" id="filter-active">Active (0)</button>
    <button class="filter-btn" onclick="filterMembers('expired')" id="filter-expired">Expired (0)</button>
  </div>

  <div class="main-container">
    <div class="member-list" id="member-list"></div>
    <div class="form-container" id="form-container">
      <div class="no-selection">Select a member from the left panel or add a new member</div>
    </div>
  </div>

    <div class="actions">
      <button class="add-btn" onclick="addNewMember()">Add New Member</button>
      <button class="password-btn" onclick="generateAllPasswords()">Generate All Passwords</button>
      <button class="save-btn" onclick="saveAllMembers()">Save All Changes</button>
      <button class="discard-btn" onclick="discardChanges()">Discard Changes</button>
    </div>
  </div>

  <!-- Toast notification -->
  <div id="toast" class="toast"></div>

  <!-- Password Results Modal -->
  <div id="password-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal">&times;</span>
      <h2>Generated Passwords</h2>
      <p>The following passwords have been generated for members:</p>
      <div class="password-list" id="password-list"></div>
    </div>
  </div>

  <script>
    let members = {};
    let selectedMember = null;
    let customFields = ['gender']; // Default custom fields
    let currentFilter = 'all'; // Track current filter: 'all', 'active', 'expired'
    let deletedMembers = []; // Track deleted members for database deletion
    let newMembers = new Set(); // Track newly created members (not yet saved to database)

    // Function to format date as dd/mm/yyyy
    function formatDateDDMMYYYY(date) {
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    }

    // Function to parse date from dd/mm/yyyy format
    function parseDateDDMMYYYY(dateStr) {
      const [day, month, year] = dateStr.split('/').map(part => parseInt(part, 10));
      return new Date(year, month - 1, day);
    }

    // Function to calculate and update member statistics
    function updateMemberStatistics() {
      const totalMembers = Object.keys(members).length;
      const activeMembers = Object.values(members).filter(member => member.isActive).length;
      const expiredMembers = totalMembers - activeMembers;

      // Update the filter buttons with statistics
      // Use adaptive text based on screen size
      const isSmallScreen = window.innerWidth <= 480;

      if (isSmallScreen) {
        // Very short text for small screens
        document.getElementById('filter-all').textContent = `All (${totalMembers})`;
        document.getElementById('filter-active').textContent = `✓ (${activeMembers})`;
        document.getElementById('filter-expired').textContent = `✗ (${expiredMembers})`;
      } else {
        // Regular text for larger screens
        document.getElementById('filter-all').textContent = `All (${totalMembers})`;
        document.getElementById('filter-active').textContent = `Active (${activeMembers})`;
        document.getElementById('filter-expired').textContent = `Expired (${expiredMembers})`;
      }
    }

    // Function to filter members based on status
    function filterMembers(filterType) {
      currentFilter = filterType;

      // Update filter button states
      document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
      document.getElementById(`filter-${filterType}`).classList.add('active');

      // Re-render the member list with the filter applied
      renderMemberList();
    }

    // Function to get filtered members based on current filter
    function getFilteredMembers() {
      const memberEntries = Object.entries(members);

      switch (currentFilter) {
        case 'active':
          return memberEntries.filter(([id, member]) => member.isActive);
        case 'expired':
          return memberEntries.filter(([id, member]) => !member.isActive);
        case 'all':
        default:
          return memberEntries;
      }
    }

    // Fetch configuration and check authentication
    async function checkAuth() {
      try {
        // Fetch the authorized email from the server
        const response = await fetch('/api/config');
        if (!response.ok) {
          throw new Error('Failed to fetch configuration');
        }

        const config = await response.json();

        // Parse allowed emails - try new format first, fallback to old format
        let allowedEmails = [];
        if (config.allowedEmails && Array.isArray(config.allowedEmails)) {
          allowedEmails = config.allowedEmails;
        } else if (config.authorizedEmail) {
          allowedEmails = config.authorizedEmail.split(',').map(email => email.trim());
        }

        const auth = JSON.parse(localStorage.getItem('snownavi_auth') || '{}');
        const authContainer = document.getElementById('auth-container');
        const userInfoContainer = document.getElementById('user-info');

        // Check if auth exists, is not expired, and user is authorized
        const isAuthorized = allowedEmails.includes(auth.email);
        if (isAuthorized && auth.expiresAt && auth.expiresAt > Date.now()) {
          // User is authenticated
          authContainer.style.display = 'none';

          // Display user info
          userInfoContainer.innerHTML = `
            <img src="${auth.picture}" alt="Profile">
            <span>${auth.name}</span>
            <button class="logout-btn" onclick="logout()">Logout</button>
          `;

          // Load members
          loadMembers();
        } else {
          // User is not authenticated, show auth container
          authContainer.style.display = 'flex';
          userInfoContainer.innerHTML = '';
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        document.getElementById('auth-container').style.display = 'flex';
        document.querySelector('.auth-message p').textContent = 'Error loading configuration. Please try again later.';
      }
    }

    // Logout function
    function logout() {
      localStorage.removeItem('snownavi_auth');
      window.location.href = 'login.html';
    }

    // Function to discard changes with confirmation
    function discardChanges() {
      // Check if there are any unsaved changes by looking at the toast
      const toast = document.getElementById('toast');
      const hasUnsavedChanges = toast.classList.contains('show') && toast.classList.contains('unsaved');

      if (hasUnsavedChanges) {
        const confirmed = confirm('Are you sure you want to discard all unsaved changes?\n\nThis action cannot be undone.');
        if (!confirmed) {
          return; // User cancelled
        }
      }

      // Proceed with loading fresh data
      loadMembers();
    }

    async function loadMembers() {
      try {
        const res = await fetch('data/members.json');
        members = await res.json();

        // Clear tracking arrays when loading fresh data
        deletedMembers = [];
        newMembers.clear();

        // Standardize validity period format for all members
        standardizeValidityPeriods();

        // Update custom fields list based on all existing member data
        updateCustomFieldsList();

        updateMemberStatistics();
        renderMemberList();

        // Re-render the current member form if one is selected
        if (selectedMember && members[selectedMember]) {
          renderMemberForm(selectedMember);
        } else if (selectedMember && !members[selectedMember]) {
          // If the selected member no longer exists, clear the form
          selectedMember = null;
          document.getElementById('form-container').innerHTML = '<div class="no-selection">Select a member from the left panel or add a new member</div>';
        }

        // Hide toast (no unsaved changes after loading)
        hideToast();
      } catch (error) {
        console.error('Error loading members:', error);
        alert('Failed to load member data. Please try again later.');
      }
    }

    // Function to scan all members and update the custom fields list
    function updateCustomFieldsList() {
      const standardFields = ['id', 'name', 'isActive', 'validityPeriod', 'password'];
      const foundFields = new Set(['gender']); // Keep gender as default

      // Scan all members for custom fields
      Object.values(members).forEach(member => {
        Object.keys(member).forEach(field => {
          if (!standardFields.includes(field)) {
            foundFields.add(field);
          }
        });
      });

      // Update the customFields array
      customFields = Array.from(foundFields);
    }

    // Function to check if validity period has expired
    function checkValidityPeriodExpired(validityPeriodStr) {
      try {
        // Check if the validity period is in the dd/mm/yyyy - dd/mm/yyyy format
        const match = validityPeriodStr.match(/^(\d{2}\/\d{2}\/\d{4}) - (\d{2}\/\d{2}\/\d{4})$/);
        if (match) {
          const endDateStr = match[2];
          const endDate = parseDateDDMMYYYY(endDateStr);

          // Set time to end of day (23:59:59)
          endDate.setHours(23, 59, 59, 999);

          // Compare with current date
          const today = new Date();
          return today > endDate;
        }

        // If the format doesn't match, try to extract the end date from other formats
        // This is a fallback for any validity periods not yet converted to dd/mm/yyyy format
        const parts = validityPeriodStr.split(' - ');
        if (parts.length === 2) {
          // Try to extract a year from the end date part
          const yearMatch = parts[1].match(/\d{4}/);
          if (yearMatch) {
            const year = parseInt(yearMatch[0], 10);
            // If the year is in the past, consider it expired
            return new Date().getFullYear() > year;
          }
        }

        // If we can't determine, default to not expired
        return false;
      } catch (e) {
        console.error('Error checking validity period:', e);
        return false;
      }
    }

    // Function to standardize all validity periods to dd/mm/yyyy format
    function standardizeValidityPeriods() {
      Object.keys(members).forEach(memberId => {
        const member = members[memberId];

        // Convert old format (object with language keys) to new format (single string)
        if (member.validityPeriod && typeof member.validityPeriod === 'object') {
          let validityValue = '';

          // Try to find a non-empty value from the object
          const values = Object.values(member.validityPeriod);
          for (const value of values) {
            if (value) {
              validityValue = value;
              break;
            }
          }

          // If we found a value but it's not in dd/mm/yyyy format, convert it
          if (validityValue && !validityValue.match(/^\d{2}\/\d{2}\/\d{4} - \d{2}\/\d{2}\/\d{4}$/)) {
            try {
              let startDate, endDate;

              // Try to parse different formats
              if (validityValue.includes(',')) {
                // Format: "January 1, 2025 - December 31, 2025"
                const parts = validityValue.split(' - ');
                if (parts.length === 2) {
                  const startParts = parts[0].split(' ');
                  const endParts = parts[1].split(' ');

                  if (startParts.length >= 3 && endParts.length >= 3) {
                    const startMonth = new Date(Date.parse(`${startParts[0]} 1, 2000`)).getMonth();
                    const startDay = parseInt(startParts[1].replace(',', ''));
                    const startYear = parseInt(startParts[2]);

                    const endMonth = new Date(Date.parse(`${endParts[0]} 1, 2000`)).getMonth();
                    const endDay = parseInt(endParts[1].replace(',', ''));
                    const endYear = parseInt(endParts[2]);

                    startDate = new Date(startYear, startMonth, startDay);
                    endDate = new Date(endYear, endMonth, endDay);
                  }
                }
              } else if (validityValue.includes('年')) {
                // Format: "2025年1月1日 - 2025年12月31日"
                const parts = validityValue.split(' - ');
                if (parts.length === 2) {
                  // Extract year, month, day from Chinese format
                  const startMatch = parts[0].match(/(\d+)年(\d+)月(\d+)日/);
                  const endMatch = parts[1].match(/(\d+)年(\d+)月(\d+)日/);

                  if (startMatch && endMatch) {
                    startDate = new Date(parseInt(startMatch[1]), parseInt(startMatch[2]) - 1, parseInt(startMatch[3]));
                    endDate = new Date(parseInt(endMatch[1]), parseInt(endMatch[2]) - 1, parseInt(endMatch[3]));
                  }
                }
              } else if (validityValue.includes('januari') || validityValue.includes('december')) {
                // Format: "1 januari 2025 - 31 december 2025"
                const parts = validityValue.split(' - ');
                if (parts.length === 2) {
                  const startParts = parts[0].split(' ');
                  const endParts = parts[1].split(' ');

                  if (startParts.length >= 3 && endParts.length >= 3) {
                    // Dutch month names to numbers
                    const monthMap = {
                      'januari': 0, 'februari': 1, 'maart': 2, 'april': 3, 'mei': 4, 'juni': 5,
                      'juli': 6, 'augustus': 7, 'september': 8, 'oktober': 9, 'november': 10, 'december': 11
                    };

                    const startDay = parseInt(startParts[0]);
                    const startMonth = monthMap[startParts[1].toLowerCase()];
                    const startYear = parseInt(startParts[2]);

                    const endDay = parseInt(endParts[0]);
                    const endMonth = monthMap[endParts[1].toLowerCase()];
                    const endYear = parseInt(endParts[2]);

                    if (!isNaN(startDay) && startMonth !== undefined && !isNaN(startYear) &&
                        !isNaN(endDay) && endMonth !== undefined && !isNaN(endYear)) {
                      startDate = new Date(startYear, startMonth, startDay);
                      endDate = new Date(endYear, endMonth, endDay);
                    }
                  }
                }
              }

              // If we successfully parsed the dates, convert to standard format
              if (startDate && endDate && !isNaN(startDate) && !isNaN(endDate)) {
                const standardStartDate = formatDateDDMMYYYY(startDate);
                const standardEndDate = formatDateDDMMYYYY(endDate);
                validityValue = `${standardStartDate} - ${standardEndDate}`;
              }
            } catch (e) {
              console.error(`Error standardizing date format for ${memberId}:`, e);
              // Keep the original if there's an error
            }
          }

          // Set the standardized value as the new validityPeriod
          member.validityPeriod = validityValue;
        }

        // Check if the validity period has expired and update member status
        if (member.isActive && member.validityPeriod) {
          const isExpired = checkValidityPeriodExpired(member.validityPeriod);
          if (isExpired) {
            console.log(`Member ${memberId} validity period has expired. Updating status.`);
            member.isActive = false;
          }
        }
      });
    }



    function renderMemberList() {
      const memberList = document.getElementById('member-list');
      memberList.innerHTML = '';

      // Get filtered members based on current filter
      const filteredMembers = getFilteredMembers();

      filteredMembers.forEach(([memberId, member]) => {
        const memberItem = document.createElement('div');
        memberItem.className = 'member-item';
        memberItem.textContent = member.name;

        // Add status badge
        const statusBadge = document.createElement('span');
        statusBadge.className = `status-badge ${member.isActive ? 'status-active' : 'status-expired'}`;
        statusBadge.textContent = member.isActive ? 'Active' : 'Expired';
        memberItem.appendChild(statusBadge);

        memberItem.dataset.memberId = memberId;
        memberItem.onclick = () => selectMember(memberId);

        if (selectedMember === memberId) {
          memberItem.classList.add('active');
        }

        memberList.appendChild(memberItem);
      });
    }

    function selectMember(memberId) {
      selectedMember = memberId;
      renderMemberList();
      renderMemberForm(memberId);
    }

    function renderMemberForm(memberId) {
      const container = document.getElementById('form-container');
      container.innerHTML = '';

      const member = members[memberId];
      const div = document.createElement('div');
      div.className = 'member-block';

      // Member ID and Name header
      const titleHeader = document.createElement('h2');
      titleHeader.textContent = `${member.name} (${memberId})`;
      div.appendChild(titleHeader);

      // Member ID (read-only for existing members, editable for new members)
      const idLabel = document.createElement('label');
      idLabel.textContent = 'Member ID';
      div.appendChild(idLabel);

      const idInput = document.createElement('input');
      idInput.value = memberId;

      // Check if this is a newly created member (not yet saved to database)
      const isNewMember = newMembers.has(memberId);

      // Make the ID field read-only for existing members
      idInput.readOnly = !isNewMember;
      idInput.style.backgroundColor = isNewMember ? '#fff' : '#f5f5f5';

      // Add a tooltip for the ID format
      idInput.title = 'Format: SN + year + 4-digit sequence number (e.g., **********)';

      // Add event listener to update the member ID if changed
      idInput.oninput = e => {
        if (isNewMember) {
          // If the ID is changed, we need to update the object key in the members object
          const newId = e.target.value;

          // Don't allow empty IDs
          if (!newId) {
            e.target.value = memberId;
            return;
          }

          // Check if the new ID already exists
          if (newId !== memberId && members[newId]) {
            alert('A member with this ID already exists.');
            e.target.value = memberId;
            return;
          }

          // Validate the ID format
          if (!validateMemberId(newId)) {
            alert('Invalid member ID format. Please use the format: SN + year + 4-digit sequence number (e.g., **********)');
            e.target.value = memberId;
            return;
          }

          // Update the member object with the new ID
          member.id = newId;

          // Create a copy of the member object with the new ID
          members[newId] = member;

          // Delete the old member object
          if (newId !== memberId) {
            delete members[memberId];

            // Update the selected member
            selectedMember = newId;

            // Refresh the member list
            renderMemberList();
          }
        }
      };

      div.appendChild(idInput);

      // Member Name
      const nameLabel = document.createElement('label');
      nameLabel.textContent = 'Name';
      div.appendChild(nameLabel);

      const nameInput = document.createElement('input');
      nameInput.value = member.name;
      nameInput.oninput = e => {
        member.name = e.target.value;
        markAsUnsaved();
        updateMemberStatistics();
        renderMemberList();
      };
      div.appendChild(nameInput);

      // Membership Status
      const statusLabel = document.createElement('label');
      statusLabel.textContent = 'Membership Status';
      div.appendChild(statusLabel);

      const statusSelect = document.createElement('select');
      const activeOption = document.createElement('option');
      activeOption.value = 'true';
      activeOption.textContent = 'Active';
      statusSelect.appendChild(activeOption);

      const expiredOption = document.createElement('option');
      expiredOption.value = 'false';
      expiredOption.textContent = 'Expired';
      statusSelect.appendChild(expiredOption);

      statusSelect.value = member.isActive.toString();
      statusSelect.onchange = e => {
        const newStatus = e.target.value === 'true';
        const oldStatus = member.isActive;
        member.isActive = newStatus;

        // If status is changing from inactive to active (Full Membership)
        if (!oldStatus && newStatus) {
          // Generate a one-year validity period from today
          const today = new Date();
          const oneYearLater = new Date(today);
          oneYearLater.setFullYear(today.getFullYear() + 1);

          // Format dates in dd/mm/yyyy format
          const startDate = formatDateDDMMYYYY(today);
          const endDate = formatDateDDMMYYYY(oneYearLater);

          // Set validity period as a single string
          member.validityPeriod = `${startDate} - ${endDate}`;

          // Update the validity input if it exists
          const validityInput = document.getElementById('validity-input');
          if (validityInput) {
            validityInput.value = member.validityPeriod || '';
          }
        }

        // If status is changing from active to inactive (Expired)
        if (oldStatus && !newStatus) {
          // Remove validity period when setting to expired
          member.validityPeriod = '';

          // Update the validity input if it exists
          const validityInput = document.getElementById('validity-input');
          if (validityInput) {
            validityInput.value = '';
          }
        }

        // Mark as unsaved
        markAsUnsaved();

        // Update statistics and member list when status changes
        updateMemberStatistics();
        renderMemberList();
      };
      div.appendChild(statusSelect);

      // Validity Period
      const validityLabel = document.createElement('label');
      validityLabel.textContent = 'Validity Period';
      div.appendChild(validityLabel);

      const validityInput = document.createElement('input');
      validityInput.id = 'validity-input';

      // If validityPeriod is an object (old format), convert to string
      if (typeof member.validityPeriod === 'object') {
        let validityValue = '';
        const values = Object.values(member.validityPeriod);
        for (const value of values) {
          if (value) {
            validityValue = value;
            break;
          }
        }
        // Convert to string format
        member.validityPeriod = validityValue;
      }

      validityInput.value = member.validityPeriod || '';
      validityInput.placeholder = 'dd/mm/yyyy - dd/mm/yyyy';
      validityInput.oninput = e => {
        member.validityPeriod = e.target.value;
        markAsUnsaved();
      };
      div.appendChild(validityInput);

      // Password Field (read-only)
      const passwordLabel = document.createElement('label');
      passwordLabel.textContent = 'Password (Hashed)';
      div.appendChild(passwordLabel);

      const passwordContainer = document.createElement('div');
      passwordContainer.className = 'password-container';

      const passwordInput = document.createElement('input');
      passwordInput.type = 'text';
      passwordInput.readOnly = true;
      passwordInput.style.backgroundColor = '#f5f5f5';
      passwordInput.style.width = 'calc(100% - 110px)';

      if (member.password) {
        // Has password - show it normally
        passwordInput.value = member.password;
        passwordInput.style.color = '#000';
        passwordInput.style.fontStyle = 'normal';
      } else {
        // No password - show placeholder message
        passwordInput.value = 'Password will be generated when saved';
        passwordInput.style.color = '#999';
        passwordInput.style.fontStyle = 'italic';
      }
      passwordContainer.appendChild(passwordInput);

      // Add button to regenerate individual password
      const regenerateBtn = document.createElement('button');
      regenerateBtn.textContent = 'Regenerate';
      regenerateBtn.className = 'regenerate-btn';
      regenerateBtn.style.marginLeft = '10px';

      if (member.password) {
        // Has password - button is enabled
        regenerateBtn.disabled = false;
        regenerateBtn.style.opacity = '1';
        regenerateBtn.onclick = async () => {
          try {
            // Call API to regenerate password for existing member
            const response = await fetch(`/api/member/${memberId}/generate-password`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' }
            });

            if (!response.ok) {
              const errorData = await response.json();
              throw new Error(errorData.error || 'Failed to generate password');
            }

            const result = await response.json();

            // Show the new password
            alert(`New password for ${member.name}: ${result.password}`);

            // Update the local member data
            member.password = result.password;

            // Mark as unsaved
            markAsUnsaved();

            // Re-render the form to show the new password
            renderMemberForm(memberId);

          } catch (error) {
            console.error('Error regenerating password:', error);
            alert('Failed to regenerate password. Please try again later.');
          }
        };
      } else {
        // No password - button is disabled
        regenerateBtn.disabled = true;
        regenerateBtn.style.opacity = '0.5';
        regenerateBtn.style.cursor = 'not-allowed';
        regenerateBtn.title = 'Save member to database first to enable password generation';
        regenerateBtn.onclick = () => {
          alert('Please save the member to database first. Password will be automatically generated.');
        };
      }

      passwordContainer.appendChild(regenerateBtn);
      div.appendChild(passwordContainer);

      // Custom Fields Section
      const customFieldsSection = document.createElement('div');
      customFieldsSection.className = 'custom-fields';

      const customFieldsHeader = document.createElement('h3');
      customFieldsHeader.textContent = 'Custom Fields';
      customFieldsSection.appendChild(customFieldsHeader);

      // Render existing custom fields
      customFields.forEach(field => {
        const fieldContainer = document.createElement('div');
        fieldContainer.className = 'custom-field';

        const fieldLabel = document.createElement('label');
        fieldLabel.textContent = field.charAt(0).toUpperCase() + field.slice(1);
        fieldContainer.appendChild(fieldLabel);

        const fieldInput = document.createElement('input');
        fieldInput.value = member[field] || '';
        fieldInput.oninput = e => {
          member[field] = e.target.value;
          markAsUnsaved();
        };
        fieldContainer.appendChild(fieldInput);

        customFieldsSection.appendChild(fieldContainer);
      });

      // Add new custom field button
      const addFieldBtn = document.createElement('button');
      addFieldBtn.className = 'add-field-btn';
      addFieldBtn.textContent = 'Add Custom Field';
      addFieldBtn.onclick = () => addCustomField(member);
      customFieldsSection.appendChild(addFieldBtn);

      div.appendChild(customFieldsSection);

      // Delete button
      const deleteBtn = document.createElement('button');
      deleteBtn.textContent = 'Delete Member';
      deleteBtn.className = 'delete-btn';
      deleteBtn.onclick = () => deleteMember(memberId);
      div.appendChild(deleteBtn);

      container.appendChild(div);
    }

    function addCustomField(member) {
      const fieldName = prompt('Enter the name of the new field:');
      if (!fieldName) return;

      // Check if field already exists
      if (customFields.includes(fieldName)) {
        alert('This field already exists.');
        return;
      }

      // Add to custom fields list
      customFields.push(fieldName);

      // Initialize the field in the member object
      member[fieldName] = '';

      // Re-render the form
      renderMemberForm(selectedMember);
    }

    // Function to validate member ID format (SN + year + 4-digit sequence number)
    function validateMemberId(id) {
      // Check if the ID matches the pattern: SN + 4-digit year + 4-digit sequence
      const pattern = /^SN\d{4}\d{4}$/;
      return pattern.test(id);
    }

    // Function to generate a new member ID
    function generateMemberId() {
      // Get current year
      const currentYear = new Date().getFullYear();

      // Format: SN + year + 4-digit sequence number
      const prefix = `SN${currentYear}`;

      // Find the highest sequence number for the current year
      let maxSequence = 0;

      Object.keys(members).forEach(id => {
        if (id.startsWith(prefix)) {
          // Extract the sequence number (last 4 digits)
          const sequenceStr = id.substring(prefix.length);
          const sequence = parseInt(sequenceStr, 10);

          if (!isNaN(sequence) && sequence > maxSequence) {
            maxSequence = sequence;
          }
        }
      });

      // Increment the sequence number
      const newSequence = maxSequence + 1;

      // Format the sequence number with leading zeros
      const sequenceStr = newSequence.toString().padStart(4, '0');

      // Return the new member ID
      return `${prefix}${sequenceStr}`;
    }

    async function addNewMember() {
      // Generate a new member ID
      const generatedId = generateMemberId();

      // Prompt the user with the generated ID (allowing them to modify it)
      const memberId = prompt('Enter a unique member ID:', generatedId);
      if (!memberId) return;

      // Check if member ID already exists
      if (members[memberId]) {
        alert('A member with this ID already exists.');
        return;
      }

      // Validate the ID format
      if (!validateMemberId(memberId)) {
        alert('Invalid member ID format. Please use the format: SN + year + 4-digit sequence number (e.g., **********)');
        addNewMember(); // Try again
        return;
      }

      // Generate a one-year validity period from today
      const today = new Date();
      const oneYearLater = new Date(today);
      oneYearLater.setFullYear(today.getFullYear() + 1);

      // Format dates in dd/mm/yyyy format
      const startDate = formatDateDDMMYYYY(today);
      const endDate = formatDateDDMMYYYY(oneYearLater);

      // Create new member object without password
      // Password will be generated by server when saved
      const newMember = {
        id: memberId,
        name: 'New Member',
        isActive: true,
        validityPeriod: `${startDate} - ${endDate}`
        // No password field - will be generated by server
      };

      // Add custom fields
      customFields.forEach(field => {
        newMember[field] = '';
      });

      // Add to local members object (will be saved when user clicks Save)
      members[memberId] = newMember;

      // Track as new member (not yet in database)
      newMembers.add(memberId);

      // Mark as unsaved
      markAsUnsaved();

      // Update the UI
      updateMemberStatistics();
      renderMemberList();
      selectMember(memberId);

      // Show info message
      alert(`New member added locally: ${memberId}\n\nPassword will be automatically generated when saved to database.\nRemember to click "Save All Changes" to save to database.`);
    }

    function deleteMember(memberId) {
      if (confirm(`Are you sure you want to delete member ${members[memberId].name}?\n\nThis will only remove the member locally. Click "Save All Changes" to apply the deletion to the database.`)) {
        // Add to deleted members list for database deletion
        if (!deletedMembers.includes(memberId)) {
          deletedMembers.push(memberId);
        }

        // Remove from local members object
        delete members[memberId];
        selectedMember = null;

        // Mark as unsaved
        markAsUnsaved();

        // Update the UI
        updateMemberStatistics();
        renderMemberList();
        document.getElementById('form-container').innerHTML = '<div class="no-selection">Select a member from the left panel or add a new member</div>';
      }
    }

    // Function to show toast notification
    function showToast(message, type = 'unsaved', duration = 0) {
      const toast = document.getElementById('toast');
      toast.textContent = message;
      toast.className = `toast ${type}`;

      // Show toast
      setTimeout(() => {
        toast.classList.add('show');
      }, 100);

      // Auto hide for saved messages
      if (duration > 0) {
        setTimeout(() => {
          hideToast();
        }, duration);
      }
    }

    // Function to hide toast
    function hideToast() {
      const toast = document.getElementById('toast');
      toast.classList.remove('show');
    }

    // Function to mark data as unsaved
    function markAsUnsaved() {
      showToast('📝 You have unsaved changes', 'unsaved');
    }

    // Function to mark data as saved
    function markAsSaved() {
      showToast('✅ All changes saved successfully', 'saved', 5000);
    }

    // Function to update a single member using the database API
    async function updateMember(memberId, updatedData) {
      try {
        const response = await fetch(`/api/member/${memberId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updatedData)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update member');
        }

        // Get the updated member data from server (may include generated hash password)
        const result = await response.json();
        if (result.member) {
          // Update local data with server data (including any server-generated fields)
          members[memberId] = { ...members[memberId], ...result.member };
        }

        console.log(`Member ${memberId} updated successfully`);
        return true;

      } catch (error) {
        console.error('Error updating member:', error);
        throw error;
      }
    }

    // Function to create a new member in the database
    async function createMember(memberId, memberData) {
      try {
        const response = await fetch('/api/member', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(memberData)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create member');
        }

        // Get the updated member data from server (may include generated hash password)
        const result = await response.json();
        if (result.member) {
          // Update local data with server data (including any server-generated fields)
          members[memberId] = { ...members[memberId], ...result.member };
        }

        console.log(`Member ${memberId} created successfully in database`);
        return true;

      } catch (error) {
        console.error('Error creating member in database:', error);
        throw error;
      }
    }

    // Function to delete a member from the database
    async function deleteMemberFromDatabase(memberId) {
      try {
        const response = await fetch(`/api/member/${memberId}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to delete member');
        }

        console.log(`Member ${memberId} deleted successfully from database`);
        return true;

      } catch (error) {
        console.error('Error deleting member from database:', error);
        throw error;
      }
    }

    // Function to save all members using individual API calls
    async function saveAllMembers() {
      try {
        // Show saving status
        showToast('💾 Saving all changes...', 'saving');

        // Make sure all validity periods are in the standard format before saving
        standardizeValidityPeriods();

        // Check all members for expired validity periods
        Object.keys(members).forEach(memberId => {
          const member = members[memberId];
          if (member.isActive && member.validityPeriod) {
            const isExpired = checkValidityPeriodExpired(member.validityPeriod);
            if (isExpired) {
              console.log(`Member ${memberId} validity period has expired. Updating status.`);
              member.isActive = false;
            }
          }
        });

        // First, delete members that were marked for deletion
        let deleteSuccessCount = 0;
        let deleteErrorCount = 0;

        for (const memberId of deletedMembers) {
          try {
            await deleteMemberFromDatabase(memberId);
            deleteSuccessCount++;
          } catch (error) {
            console.error(`Failed to delete member ${memberId}:`, error);
            deleteErrorCount++;
          }
        }

        // Clear the deleted members list after processing
        if (deleteErrorCount === 0) {
          deletedMembers = [];
        }

        // Save/update each existing member individually
        const memberIds = Object.keys(members);
        let successCount = 0;
        let errorCount = 0;

        for (const memberId of memberIds) {
          try {
            // Check if this is a new member (doesn't exist in original data)
            // For simplicity, we'll try PUT first, and if it fails with 404, try POST
            try {
              await updateMember(memberId, members[memberId]);
              successCount++;
              // Remove from new members tracking since it's now saved
              newMembers.delete(memberId);
            } catch (updateError) {
              // If update fails with 404, try creating as new member
              if (updateError.message.includes('not found') || updateError.message.includes('404')) {
                await createMember(memberId, members[memberId]);
                successCount++;
                // Remove from new members tracking since it's now saved
                newMembers.delete(memberId);
              } else {
                throw updateError;
              }
            }
          } catch (error) {
            console.error(`Failed to save member ${memberId}:`, error);
            errorCount++;
          }
        }

        if (errorCount === 0 && deleteErrorCount === 0) {
          // All operations successful
          markAsSaved();

          // Clear tracking arrays since all changes are now saved
          deletedMembers = [];
          newMembers.clear();

          let message = '';
          if (deleteSuccessCount > 0 && successCount > 0) {
            message = `Successfully deleted ${deleteSuccessCount} members and saved ${successCount} members!`;
          } else if (deleteSuccessCount > 0) {
            message = `Successfully deleted ${deleteSuccessCount} members!`;
          } else if (successCount > 0) {
            message = `Successfully saved ${successCount} members!`;
          } else {
            message = 'All changes saved successfully!';
          }
          alert(message);

          // Refresh data from server to get updated passwords for new members
          await loadMembers();

          // Re-render the current member form if one is selected to show new password
          if (selectedMember) {
            renderMemberForm(selectedMember);
          }
        } else {
          // Some operations failed
          const totalErrors = errorCount + deleteErrorCount;
          const totalSuccess = successCount + deleteSuccessCount;
          showToast(`⚠️ ${totalSuccess} operations succeeded, ${totalErrors} failed`, 'unsaved');
          alert(`${totalSuccess} operations completed successfully, but ${totalErrors} failed. Please check the console for details.`);
        }

      } catch (error) {
        // Show error status
        showToast('❌ Save failed', 'unsaved');

        console.error('Error saving members:', error);
        alert('Failed to save member data. Please try again later.');
      }
    }

    // Legacy function for compatibility (now redirects to saveAllMembers)
    function saveMembers() {
      return saveAllMembers();
    }

    // Function to generate passwords for all members using server API
    async function generateAllPasswords() {
      try {
        // Show loading state
        showToast('🔑 Generating passwords...', 'saving');

        // Call the server API to generate passwords for all members
        const response = await fetch('/api/members/generate-passwords', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });

        if (!response.ok) {
          throw new Error('Failed to generate passwords');
        }

        const result = await response.json();

        // Update local members data with new hashed passwords
        result.members.forEach(memberData => {
          if (members[memberData.id]) {
            members[memberData.id].password = memberData.password;
          }
        });

        // Display the generated passwords in the modal
        const passwordList = document.getElementById('password-list');
        passwordList.innerHTML = '';

        result.members.forEach(member => {
          const passwordItem = document.createElement('div');
          passwordItem.className = 'password-item';
          passwordItem.innerHTML = `
            <div><strong>${member.name}</strong> (${member.id})</div>
            <div>Password: <code>${member.password}</code></div>
          `;
          passwordList.appendChild(passwordItem);
        });

        // Show the modal
        const modal = document.getElementById('password-modal');
        modal.style.display = 'block';

        // Add close functionality to the modal
        const closeBtn = document.querySelector('.close-modal');
        closeBtn.onclick = () => {
          modal.style.display = 'none';
        };

        // Close modal when clicking outside
        window.onclick = (event) => {
          if (event.target === modal) {
            modal.style.display = 'none';
          }
        };

        // Mark as unsaved
        markAsUnsaved();

        // Hide loading toast
        hideToast();

        // Update the UI
        updateMemberStatistics();
        renderMemberList();
        if (selectedMember) {
          renderMemberForm(selectedMember);
        }

      } catch (error) {
        console.error('Error generating passwords:', error);
        showToast('❌ Failed to generate passwords', 'unsaved');
        alert('Failed to generate passwords. Please try again later.');
      }
    }



    // Initialize the page
    window.onload = () => {
      checkAuth();

      // Add event listener for modal close button
      const closeBtn = document.querySelector('.close-modal');
      closeBtn.onclick = () => {
        document.getElementById('password-modal').style.display = 'none';
      };

      // Add window resize listener to update button text
      window.addEventListener('resize', () => {
        if (Object.keys(members).length > 0) {
          updateMemberStatistics();
        }
      });
    };
  </script>
</body>
</html>
