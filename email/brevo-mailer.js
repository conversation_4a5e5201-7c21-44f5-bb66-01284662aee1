/**
 * Brevo Email Service for SnowNavi
 * Provides email sending functionality using Brevo API
 */

class BrevoMailer {
    constructor() {
        // Use Flask backend API instead of separate Node.js service
        // Auto-detect environment
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            // Development environment - Flask server on port 8899
            this.apiBaseUrl = 'http://127.0.0.1:8899/api/email';
        } else {
            // Production environment - use same domain (Flask server behind nginx)
            this.apiBaseUrl = `${window.location.protocol}//${window.location.host}/api/email`;
        }
        this.useBackend = true; // Set to false to use direct API calls (for testing)

        // Fallback configuration for direct API calls (if backend is not available)
        this.apiKey = null; // Will be loaded from backend
        this.apiEndpoint = 'https://api.brevo.com/v3/smtp/email';
        this.defaultSender = {
            email: '<EMAIL>',
            name: 'SnowNavi Team'
        };

        // Initialize configuration
        this.init();
    }

    /**
     * Initialize mailer and get configuration from backend
     */
    async init() {
        if (this.useBackend) {
            try {
                const response = await fetch(`${this.apiBaseUrl}/config`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        console.log('✅ Backend email service available');
                        console.log('📧 Configuration:', data.config);
                        return;
                    }
                }
                throw new Error('Backend not available');
            } catch (error) {
                console.warn('⚠️ Backend email service not available');
                console.warn('❌ Direct API calls are disabled for security reasons');
                console.warn('💡 Please start the backend service: cd backend && npm start');
                this.useBackend = false;
                // Don't set API key for security reasons
                this.apiKey = null;
            }
        }
    }

    /**
     * Send email using backend API or direct Brevo API
     * @param {Object} emailData - Email configuration
     * @param {string} emailData.to - Recipient email address
     * @param {string} emailData.subject - Email subject
     * @param {string} emailData.content - Email content (plain text)
     * @param {string} [emailData.htmlContent] - Email content (HTML)
     * @param {Object} [emailData.sender] - Sender information
     * @param {string} [emailData.sender.email] - Sender email
     * @param {string} [emailData.sender.name] - Sender name
     * @returns {Promise<Object>} - API response
     */
    async sendEmail(emailData) {
        try {
            // Validate required fields
            if (!emailData.to || !emailData.subject || (!emailData.content && !emailData.htmlContent)) {
                throw new Error('Missing required fields: to, subject, and content are required');
            }

            // Ensure initialization is complete
            await this.init();

            if (this.useBackend) {
                return await this.sendEmailViaBackend(emailData);
            } else {
                throw new Error('Backend email service is not available. Please start the backend service: cd backend && npm start');
            }

        } catch (error) {
            console.error('Email sending failed:', error);
            throw error;
        }
    }

    /**
     * Send email via backend API
     */
    async sendEmailViaBackend(emailData) {
        console.log('📤 Sending email via backend API to:', emailData.to);

        const response = await fetch(`${this.apiBaseUrl}/send`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                to: emailData.to,
                subject: emailData.subject,
                content: emailData.content,
                htmlContent: emailData.htmlContent,
                toName: emailData.toName || emailData.to.split('@')[0]
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Backend API Error (${response.status}): ${errorData.error || response.statusText}`);
        }

        const result = await response.json();
        console.log('✅ Email sent successfully via backend:', result);
        return result;
    }

    /**
     * Send email directly via Brevo API (fallback)
     */
    async sendEmailDirectly(emailData) {
        console.log('📤 Sending email directly via Brevo API to:', emailData.to);

        // This method requires API key to be set
        if (!this.apiKey) {
            throw new Error('API key not available for direct API calls');
        }

        // Prepare sender information
        const sender = emailData.sender || this.defaultSender;

        // Prepare email payload
        const payload = {
            sender: {
                name: sender.name || this.defaultSender.name,
                email: sender.email || this.defaultSender.email
            },
            to: [{
                email: emailData.to,
                name: emailData.toName || emailData.to.split('@')[0]
            }],
            subject: emailData.subject
        };

        // Add content (prefer HTML if provided, otherwise convert text to HTML)
        if (emailData.htmlContent) {
            payload.htmlContent = emailData.htmlContent;
        } else {
            payload.htmlContent = this.textToHtml(emailData.content, emailData.subject);
        }

        // Add plain text content if available
        if (emailData.content && !emailData.htmlContent) {
            payload.textContent = emailData.content;
        }

        // Send email via API
        const response = await fetch(this.apiEndpoint, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'api-key': this.apiKey,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
            try {
                const errorData = await response.json();
                errorMessage = `Brevo API Error (${response.status}): ${errorData.message || errorData.code || response.statusText}`;
                console.error('Brevo API Error Details:', errorData);
            } catch (parseError) {
                console.error('Failed to parse error response:', parseError);
            }
            throw new Error(errorMessage);
        }

        const result = await response.json();
        console.log('✅ Email sent successfully via direct API:', result);
        return result;
    }

    /**
     * Convert plain text to HTML with basic formatting
     * @param {string} text - Plain text content
     * @param {string} subject - Email subject for header
     * @returns {string} - HTML content
     */
    textToHtml(text, subject) {
        const htmlContent = text
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');

        return `
            <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="border-bottom: 3px solid #E53512; margin-bottom: 20px;">
                        <h2 style="color: #E53512; margin: 0; padding-bottom: 10px;">
                            ${subject}
                        </h2>
                    </div>
                    <div style="margin: 20px 0; font-size: 16px;">
                        ${htmlContent}
                    </div>
                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">
                    <div style="text-align: center; font-size: 12px; color: #666;">
                        <p>Sent via SnowNavi Email System</p>
                        <p style="margin: 5px 0;">Powered by Brevo</p>
                    </div>
                </body>
            </html>
        `;
    }

    /**
     * Send feedback email to student
     * @param {Object} feedbackData - Feedback information
     * @param {string} feedbackData.studentEmail - Student email
     * @param {string} feedbackData.studentName - Student name
     * @param {string} feedbackData.courseName - Course name
     * @param {string} feedbackData.instructorName - Instructor name
     * @param {string} feedbackData.feedbackContent - Feedback content
     * @param {string} [feedbackData.feedbackUrl] - URL to view detailed feedback
     * @returns {Promise<Object>} - API response
     */
    async sendFeedbackEmail(feedbackData) {
        // Ensure initialization is complete
        await this.init();

        if (this.useBackend) {
            console.log('📤 Sending feedback email via backend API');

            const response = await fetch(`${this.apiBaseUrl}/feedback`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(feedbackData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Backend API Error (${response.status}): ${errorData.error || response.statusText}`);
            }

            const result = await response.json();
            console.log('✅ Feedback email sent successfully via backend:', result);
            return result;
        }

        throw new Error('Backend email service is not available. Please start the backend service: cd backend && npm start');
    }

    /**
     * Send feedback email directly (fallback method)
     */
    async sendFeedbackEmailDirectly(feedbackData) {
        const subject = `🎿 SnowNavi 课程反馈 - ${feedbackData.courseName}`;
        
        const htmlContent = `
            <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background: linear-gradient(135deg, #E53512 0%, #9ED4E7 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center;">
                        <h1 style="margin: 0; font-size: 24px;">🎿 SnowNavi</h1>
                        <p style="margin: 10px 0 0 0; font-size: 16px;">滑雪课程反馈报告</p>
                    </div>
                    
                    <div style="background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px;">
                        <h2 style="color: #E53512; margin-top: 0;">亲爱的 ${feedbackData.studentName}，</h2>
                        
                        <p>感谢您参加我们的滑雪课程！以下是您的课程反馈：</p>
                        
                        <div style="background: white; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #E53512;">
                            <h3 style="margin-top: 0; color: #E53512;">课程信息</h3>
                            <p><strong>课程名称：</strong>${feedbackData.courseName}</p>
                            <p><strong>教练：</strong>${feedbackData.instructorName}</p>
                            <p><strong>反馈时间：</strong>${new Date().toLocaleDateString('zh-CN')}</p>
                        </div>
                        
                        <div style="background: white; padding: 15px; border-radius: 6px; margin: 20px 0;">
                            <h3 style="margin-top: 0; color: #E53512;">教练反馈</h3>
                            <div style="white-space: pre-line; font-size: 16px;">
                                ${feedbackData.feedbackContent}
                            </div>
                        </div>
                        
                        ${feedbackData.feedbackUrl ? `
                        <div style="text-align: center; margin: 30px 0;">
                            <a href="${feedbackData.feedbackUrl}" 
                               style="background: #E53512; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                                📊 查看详细反馈报告
                            </a>
                        </div>
                        ` : ''}
                        
                        <div style="background: #e8f4f8; padding: 15px; border-radius: 6px; margin: 20px 0;">
                            <h4 style="margin-top: 0; color: #0c5460;">💡 学习建议</h4>
                            <p>继续保持练习，每一次滑雪都是进步的机会！如有任何问题，请随时联系我们的教练团队。</p>
                        </div>
                        
                        <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">
                        
                        <div style="text-align: center; color: #666; font-size: 14px;">
                            <p>祝您滑雪愉快！🎿</p>
                            <p><strong>SnowNavi 团队</strong></p>
                            <p style="font-size: 12px; margin-top: 20px;">
                                此邮件由 SnowNavi 系统自动发送，请勿直接回复。
                            </p>
                        </div>
                    </div>
                </body>
            </html>
        `;

        return await this.sendEmail({
            to: feedbackData.studentEmail,
            toName: feedbackData.studentName,
            subject: subject,
            htmlContent: htmlContent
        });
    }

    /**
     * Send welcome email to new student
     * @param {Object} studentData - Student information
     * @param {string} studentData.email - Student email
     * @param {string} studentData.name - Student name
     * @param {string} [studentData.courseName] - Course name
     * @returns {Promise<Object>} - API response
     */
    async sendWelcomeEmail(studentData) {
        // Ensure initialization is complete
        await this.init();

        if (this.useBackend) {
            console.log('📤 Sending welcome email via backend API');

            const response = await fetch(`${this.apiBaseUrl}/welcome`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(studentData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Backend API Error (${response.status}): ${errorData.error || response.statusText}`);
            }

            const result = await response.json();
            console.log('✅ Welcome email sent successfully via backend:', result);
            return result;
        }

        throw new Error('Backend email service is not available. Please start the backend service: cd backend && npm start');
    }

    /**
     * Send welcome email directly (fallback method)
     */
    async sendWelcomeEmailDirectly(studentData) {
        const subject = `🎿 欢迎加入 SnowNavi！`;
        
        const htmlContent = `
            <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background: linear-gradient(135deg, #E53512 0%, #9ED4E7 100%); color: white; padding: 30px; border-radius: 8px; text-align: center;">
                        <h1 style="margin: 0; font-size: 28px;">🎿 欢迎来到 SnowNavi！</h1>
                        <p style="margin: 15px 0 0 0; font-size: 18px;">让每一次滑雪都成为进步的阶梯</p>
                    </div>
                    
                    <div style="padding: 30px 20px;">
                        <h2 style="color: #E53512;">亲爱的 ${studentData.name}，</h2>
                        
                        <p style="font-size: 16px;">欢迎加入 SnowNavi 滑雪学习系统！我们很高兴能够陪伴您的滑雪学习之旅。</p>
                        
                        <div style="background: #f9f4f3; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #E53512;">
                            <h3 style="margin-top: 0; color: #E53512;">🎯 您将获得：</h3>
                            <ul style="margin: 0; padding-left: 20px;">
                                <li>📊 详细的学习进度追踪</li>
                                <li>🎓 个性化的教练反馈</li>
                                <li>📱 随时查看学习报告</li>
                                <li>🏆 技能成就系统</li>
                                <li>📈 长期学习规划</li>
                            </ul>
                        </div>
                        
                        ${studentData.courseName ? `
                        <div style="background: #e8f4f8; padding: 20px; border-radius: 8px; margin: 25px 0;">
                            <h3 style="margin-top: 0; color: #0c5460;">📚 您的课程</h3>
                            <p style="font-size: 16px; margin: 0;"><strong>${studentData.courseName}</strong></p>
                        </div>
                        ` : ''}
                        
                        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #856404;">
                            <h3 style="margin-top: 0; color: #856404;">💡 小贴士</h3>
                            <p>课程结束后，您将收到详细的反馈邮件。请保存好您的邮箱地址，以便随时查看学习进度！</p>
                        </div>
                        
                        <div style="text-align: center; margin: 30px 0;">
                            <p style="font-size: 18px; color: #E53512; font-weight: bold;">准备好开始您的滑雪之旅了吗？🎿</p>
                        </div>
                        
                        <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">
                        
                        <div style="text-align: center; color: #666; font-size: 14px;">
                            <p>祝您学习愉快！</p>
                            <p><strong>SnowNavi 团队</strong></p>
                        </div>
                    </div>
                </body>
            </html>
        `;

        return await this.sendEmail({
            to: studentData.email,
            toName: studentData.name,
            subject: subject,
            htmlContent: htmlContent
        });
    }

    /**
     * Test email connectivity
     * @param {string} testEmail - Email address to send test email to
     * @returns {Promise<Object>} - API response
     */
    async sendTestEmail(testEmail) {
        // Ensure initialization is complete
        await this.init();

        if (this.useBackend) {
            console.log('📤 Sending test email via backend API');

            const response = await fetch(`${this.apiBaseUrl}/test`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email: testEmail })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Backend API Error (${response.status}): ${errorData.error || response.statusText}`);
            }

            const result = await response.json();
            console.log('✅ Test email sent successfully via backend:', result);
            return result;
        }

        throw new Error('Backend email service is not available. Please start the backend service: cd backend && npm start');
    }

    /**
     * Send test email directly (fallback method)
     */
    async sendTestEmailDirectly(testEmail) {
        const subject = 'SnowNavi 邮件系统测试 / Email System Test';
        const content = `您好！/ Hello!

这是来自 SnowNavi 系统的测试邮件。
This is a test email from SnowNavi system.

如果您收到这封邮件，说明邮件发送功能正常工作。
If you receive this email, it means the email sending function is working properly.

测试时间 / Test Time: ${new Date().toLocaleString('zh-CN')}

祝好！/ Best regards,
SnowNavi Team`;

        return await this.sendEmail({
            to: testEmail,
            subject: subject,
            content: content
        });
    }
}

// Create global instance
window.brevoMailer = new BrevoMailer();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BrevoMailer;
}

console.log('📧 Brevo Mailer loaded successfully');
