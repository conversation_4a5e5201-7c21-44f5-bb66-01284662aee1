<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flask Email Integration Test - SnowNavi</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f4f3;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #E53512;
        }
        .header h1 {
            color: #E53512;
            margin: 0;
        }
        .status-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #E53512;
        }
        .status-section h3 {
            color: #E53512;
            margin-top: 0;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #E53512;
        }
        .test-section h3 {
            color: #E53512;
            margin-top: 0;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #E53512;
        }
        .btn {
            background: #E53512;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #c42e0f;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            display: none;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .config-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Flask Email Integration Test</h1>
            <p>测试 Flask 服务器集成的邮件功能</p>
        </div>

        <!-- Flask Server Status -->
        <div class="status-section">
            <h3>📡 Flask 服务器状态</h3>
            <div id="flask-status">检查中...</div>
            <button class="btn" onclick="checkFlaskStatus()">检查状态</button>
        </div>

        <!-- Configuration Display -->
        <div class="status-section">
            <h3>⚙️ 邮件配置信息</h3>
            <div id="config-display" class="config-display">加载中...</div>
            <button class="btn" onclick="loadConfig()">刷新配置</button>
        </div>

        <!-- Test Email Section -->
        <div class="test-section">
            <h3>1. 发送测试邮件</h3>
            <div class="form-group">
                <label for="test-email">测试邮箱地址:</label>
                <input type="email" id="test-email" placeholder="<EMAIL>">
            </div>
            <button class="btn" onclick="sendTestEmail()">发送测试邮件</button>
        </div>

        <!-- Welcome Email Section -->
        <div class="test-section">
            <h3>2. 发送欢迎邮件</h3>
            <div class="form-group">
                <label for="welcome-email">学员邮箱:</label>
                <input type="email" id="welcome-email" placeholder="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="welcome-name">学员姓名:</label>
                <input type="text" id="welcome-name" placeholder="张三">
            </div>
            <div class="form-group">
                <label for="welcome-course">课程名称:</label>
                <input type="text" id="welcome-course" placeholder="初级滑雪课程">
            </div>
            <button class="btn" onclick="sendWelcomeEmail()">发送欢迎邮件</button>
        </div>

        <!-- Feedback Email Section -->
        <div class="test-section">
            <h3>3. 发送反馈邮件</h3>
            <div class="form-group">
                <label for="feedback-email">学员邮箱:</label>
                <input type="email" id="feedback-email" placeholder="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="feedback-name">学员姓名:</label>
                <input type="text" id="feedback-name" placeholder="张三">
            </div>
            <div class="form-group">
                <label for="feedback-course">课程名称:</label>
                <input type="text" id="feedback-course" placeholder="初级滑雪课程">
            </div>
            <div class="form-group">
                <label for="feedback-instructor">教练姓名:</label>
                <input type="text" id="feedback-instructor" placeholder="李教练">
            </div>
            <div class="form-group">
                <label for="feedback-content">反馈内容:</label>
                <textarea id="feedback-content" placeholder="输入详细的课程反馈内容..."></textarea>
            </div>
            <button class="btn" onclick="sendFeedbackEmail()">发送反馈邮件</button>
        </div>

        <!-- Status Display -->
        <div id="status" class="status"></div>

        <!-- Instructions -->
        <div class="test-section">
            <h3>📋 使用说明</h3>
            <ol>
                <li>确保 Flask 服务器正在运行：<code>cd backend && python server.py</code></li>
                <li>检查服务器状态是否正常</li>
                <li>查看配置信息确认 API Key 已正确加载</li>
                <li>输入有效的邮箱地址进行测试</li>
                <li>检查邮箱收件箱确认邮件发送成功</li>
            </ol>
        </div>
    </div>

    <!-- Include updated Brevo Mailer -->
    <script src="brevo-mailer.js"></script>

    <script>
        // Use Flask server API base URL
        const API_BASE = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' 
            ? 'http://127.0.0.1:8899' 
            : `${window.location.protocol}//${window.location.host}`;

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.textContent = message;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 5000);
        }

        async function checkFlaskStatus() {
            const statusDiv = document.getElementById('flask-status');
            statusDiv.textContent = '检查中...';
            
            try {
                const response = await fetch(`${API_BASE}/api/email-health`);
                
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.innerHTML = `
                        <div style="color: #155724;">
                            ✅ Flask 服务器正常运行<br>
                            服务: ${data.service}<br>
                            时间: ${new Date(data.timestamp).toLocaleString('zh-CN')}<br>
                            发件人: ${data.config.senderEmail}
                        </div>
                    `;
                } else {
                    statusDiv.innerHTML = `<div style="color: #721c24;">❌ Flask 服务器响应异常 (${response.status})</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div style="color: #721c24;">❌ 无法连接到 Flask 服务器: ${error.message}</div>`;
            }
        }

        async function loadConfig() {
            const configDiv = document.getElementById('config-display');
            configDiv.textContent = '加载中...';
            
            try {
                const response = await fetch(`${API_BASE}/api/email/config`);
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        configDiv.innerHTML = `
API Endpoint: ${data.config.apiEndpoint}
Sender Email: ${data.config.senderEmail}
Sender Name: ${data.config.senderName}
Has API Key: ${data.config.hasApiKey}
API Key Preview: ${data.config.apiKeyPreview}
                        `;
                    } else {
                        configDiv.textContent = '配置加载失败';
                    }
                } else {
                    configDiv.textContent = `配置加载失败 (${response.status})`;
                }
            } catch (error) {
                configDiv.textContent = `配置加载出错: ${error.message}`;
            }
        }

        async function sendTestEmail() {
            const email = document.getElementById('test-email').value.trim();
            if (!email) {
                showStatus('请输入邮箱地址', 'error');
                return;
            }

            try {
                showStatus('正在发送测试邮件...', 'info');
                
                const response = await fetch(`${API_BASE}/api/email/test`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email: email })
                });

                const result = await response.json();
                
                if (result.success) {
                    showStatus(`测试邮件发送成功! Message ID: ${result.messageId}`, 'success');
                } else {
                    showStatus(`发送失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showStatus(`发送失败: ${error.message}`, 'error');
            }
        }

        async function sendWelcomeEmail() {
            const email = document.getElementById('welcome-email').value.trim();
            const name = document.getElementById('welcome-name').value.trim();
            const course = document.getElementById('welcome-course').value.trim();

            if (!email || !name) {
                showStatus('请填写邮箱和姓名', 'error');
                return;
            }

            try {
                showStatus('正在发送欢迎邮件...', 'info');
                
                const response = await fetch(`${API_BASE}/api/email/welcome`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        name: name,
                        courseName: course || undefined
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showStatus(`欢迎邮件发送成功! Message ID: ${result.messageId}`, 'success');
                } else {
                    showStatus(`发送失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showStatus(`发送失败: ${error.message}`, 'error');
            }
        }

        async function sendFeedbackEmail() {
            const email = document.getElementById('feedback-email').value.trim();
            const name = document.getElementById('feedback-name').value.trim();
            const course = document.getElementById('feedback-course').value.trim();
            const instructor = document.getElementById('feedback-instructor').value.trim();
            const content = document.getElementById('feedback-content').value.trim();

            if (!email || !name || !course || !instructor || !content) {
                showStatus('请填写所有必填字段', 'error');
                return;
            }

            try {
                showStatus('正在发送反馈邮件...', 'info');
                
                const response = await fetch(`${API_BASE}/api/email/feedback`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        studentEmail: email,
                        studentName: name,
                        courseName: course,
                        instructorName: instructor,
                        feedbackContent: content,
                        feedbackUrl: `${window.location.origin}/student_feedback.html?email=${encodeURIComponent(email)}`
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showStatus(`反馈邮件发送成功! Message ID: ${result.messageId}`, 'success');
                } else {
                    showStatus(`发送失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showStatus(`发送失败: ${error.message}`, 'error');
            }
        }

        // Initialize page
        window.addEventListener('load', function() {
            // Set default values
            document.getElementById('test-email').value = '<EMAIL>';
            document.getElementById('welcome-email').value = '<EMAIL>';
            document.getElementById('welcome-name').value = '张三';
            document.getElementById('welcome-course').value = '初级滑雪课程';
            document.getElementById('feedback-email').value = '<EMAIL>';
            document.getElementById('feedback-name').value = '张三';
            document.getElementById('feedback-course').value = '初级滑雪课程';
            document.getElementById('feedback-instructor').value = '李教练';
            document.getElementById('feedback-content').value = `亲爱的张三，

感谢您参加初级滑雪课程！

在本次课程中，您的表现非常出色。以下是详细的反馈：

✅ 基础姿势掌握良好
✅ 转弯技巧有明显进步  
✅ 安全意识很强

建议继续练习：
• 加强平衡感训练
• 多练习连续转弯
• 提高滑行速度控制

期待您的下次课程！

李教练
${new Date().toLocaleDateString('zh-CN')}`;

            // Auto-check Flask status
            setTimeout(() => {
                checkFlaskStatus();
                loadConfig();
            }, 1000);
        });
    </script>
</body>
</html>
