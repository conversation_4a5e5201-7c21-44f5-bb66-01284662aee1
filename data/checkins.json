{"CHK20250001": {"id": "CHK20250001", "activityId": "ACT20250005", "memberId": "SN20230001", "memberName": "<PERSON><PERSON><PERSON>", "memberType": "member", "checkinTime": "2025-06-09T19:33:20.329Z", "checkinBy": "okboy", "checkinByEmail": "<EMAIL>", "notes": "Checked in via QR code scan as member by okboy", "createdAt": "2025-06-09T19:33:20.329Z"}, "CHK20250002": {"id": "CHK20250002", "activityId": "ACT20250006", "memberId": "SN99999999", "memberName": "SnowNavi Member", "memberType": "member", "checkinTime": "2025-06-09T20:22:54.337Z", "checkinBy": "okboy", "checkinByEmail": "<EMAIL>", "notes": "Checked in via QR code scan as member by okboy", "createdAt": "2025-06-09T20:22:54.337Z"}, "CHK20250003": {"id": "CHK20250003", "activityId": "ACT20250007", "memberId": "SN99999999", "memberName": "SnowNavi Member", "memberType": "member", "checkinTime": "2025-06-09T20:23:22.576Z", "checkinBy": "okboy", "checkinByEmail": "<EMAIL>", "notes": "Checked in via QR code scan as member by okboy", "createdAt": "2025-06-09T20:23:22.576Z"}, "CHK20250004": {"id": "CHK20250004", "activityId": "ACT20250003", "memberId": "SN20250009", "memberName": "<PERSON><PERSON><PERSON><PERSON>", "memberType": "member", "checkinTime": "2025-06-14T08:01:37.279Z", "checkinBy": "okboy", "checkinByEmail": "<EMAIL>", "notes": "Checked in via QR code scan as member by okboy", "createdAt": "2025-06-14T08:01:37.279Z"}, "CHK20250005": {"id": "CHK20250005", "activityId": "ACT20250001", "memberId": "SN20250003", "memberName": "<PERSON><PERSON><PERSON>", "memberType": "member", "checkinTime": "2025-06-14T08:04:22.070Z", "checkinBy": "okboy", "checkinByEmail": "<EMAIL>", "notes": "Checked in via QR code scan as member by okboy", "createdAt": "2025-06-14T08:04:22.070Z"}, "CHK20250006": {"id": "CHK20250006", "activityId": "ACT20250001", "memberId": "SN20250008", "memberName": "<PERSON><PERSON><PERSON>", "memberType": "member", "checkinTime": "2025-06-14T08:05:09.556Z", "checkinBy": "okboy", "checkinByEmail": "<EMAIL>", "notes": "Checked in via QR code scan as member by okboy", "createdAt": "2025-06-14T08:05:09.556Z"}, "CHK20250007": {"id": "CHK20250007", "activityId": "ACT20250001", "memberId": "SN20250004", "memberName": "<PERSON><PERSON>", "memberType": "member", "checkinTime": "2025-06-14T08:05:22.754Z", "checkinBy": "okboy", "checkinByEmail": "<EMAIL>", "notes": "Checked in via QR code scan as member by okboy", "createdAt": "2025-06-14T08:05:22.754Z"}, "CHK20250008": {"id": "CHK20250008", "activityId": "ACT20250001", "memberId": "SN20250007", "memberName": "<PERSON><PERSON><PERSON>", "memberType": "member", "checkinTime": "2025-06-14T08:06:44.839Z", "checkinBy": "okboy", "checkinByEmail": "<EMAIL>", "notes": "Checked in via QR code scan as member by okboy", "createdAt": "2025-06-14T08:06:44.839Z"}, "CHK20250009": {"id": "CHK20250009", "activityId": "ACT20250001", "memberId": "SN20250001", "memberName": "<PERSON><PERSON>", "memberType": "member", "checkinTime": "2025-06-14T08:08:22.550Z", "checkinBy": "okboy", "checkinByEmail": "<EMAIL>", "notes": "Checked in via QR code scan as member by okboy", "createdAt": "2025-06-14T08:08:22.550Z"}, "CHK20250010": {"id": "CHK20250010", "activityId": "ACT20250001", "memberId": "SN20250002", "memberName": "<PERSON>", "memberType": "member", "checkinTime": "2025-06-14T08:08:32.827Z", "checkinBy": "okboy", "checkinByEmail": "<EMAIL>", "notes": "Checked in via QR code scan as member by okboy", "createdAt": "2025-06-14T08:08:32.827Z"}, "CHK20250011": {"id": "CHK20250011", "activityId": "ACT20250003", "memberId": "SN20250005", "memberName": "<PERSON><PERSON><PERSON>", "memberType": "member", "checkinTime": "2025-06-14T08:12:00.981Z", "checkinBy": "okboy", "checkinByEmail": "<EMAIL>", "notes": "Checked in via QR code scan as member by okboy", "createdAt": "2025-06-14T08:12:00.981Z"}}